package com.tfkcolin.cebsscada.ui.settings

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.ui.CommunicationScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationSettings(
    onBack: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Communication Hub Testing") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            CommunicationScreen()
        }
    }
}

/**
 * Content-only version for use with CentralScaffold
 */
@Composable
fun CommunicationSettingsContent() {
    CommunicationScreen()
}