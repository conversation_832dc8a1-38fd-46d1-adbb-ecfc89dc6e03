package com.tfkcolin.cebsscada

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.tfkcolin.cebsscada.navigation.AppNavigation
import com.tfkcolin.cebsscada.navigation.rememberNavigationState
import com.tfkcolin.cebsscada.ui.components.CentralScaffold
import com.tfkcolin.cebsscada.ui.components.ScreenConfig
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager
import com.tfkcolin.cebsscada.ui.theme.CEBSSCADATheme
import com.tfkcolin.cebsscada.ui.theme.rememberResponsiveLayout
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var bluetoothPermissionManager: BluetoothPermissionManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CEBSSCADATheme {
                CommunicationApp(bluetoothPermissionManager)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationApp(bluetoothPermissionManager: BluetoothPermissionManager) {
    val navigationState = rememberNavigationState()
    val layout = rememberResponsiveLayout()

    // Screen configuration state - will be updated by individual screens
    var screenConfig by remember {
        mutableStateOf(
            ScreenConfig(
                title = "CEBS SCADA",
                showBackButton = false,
                showSettingsButton = true
            )
        )
    }

    CentralScaffold(
        navigationState = navigationState,
        screenConfig = screenConfig,
        layout = layout
    ) { innerPadding ->
        AppNavigation(
            navController = navigationState.navController,
            bluetoothPermissionManager = bluetoothPermissionManager,
            onScreenConfigChange = { config -> screenConfig = config },
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        )
    }
}


