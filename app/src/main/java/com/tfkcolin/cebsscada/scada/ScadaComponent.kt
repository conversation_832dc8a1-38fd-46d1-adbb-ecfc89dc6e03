package com.tfkcolin.cebsscada.scada

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * Base interface for all SCADA components
 */
interface ScadaComponent {
    val id: String
    val type: ComponentType
    val position: ComponentPosition
    val size: ComponentSize
    val config: ComponentConfig

    // Organization properties
    val groupId: String? // For component grouping
    val layer: Int // Z-index for layering (higher = front)
    val isLocked: Boolean // Prevent editing when true
    val isVisible: Boolean // Component visibility
    val tags: Set<String> // For organization and filtering

    /**
     * Check if a point is within the component bounds
     */
    fun containsPoint(x: Float, y: Float): Boolean {
        return x >= position.x && x <= position.x + size.width.value &&
               y >= position.y && y <= position.y + size.height.value
    }

    /**
     * Move component to new position
     */
    fun moveTo(newPosition: ComponentPosition): ScadaComponent

    /**
     * Resize component to new size
     */
    fun resizeTo(newSize: ComponentSize): ScadaComponent

    /**
     * Update component configuration
     */
    fun updateConfig(newConfig: ComponentConfig): ScadaComponent

    /**
     * Update organization properties
     */
    fun updateOrganization(groupId: String?, layer: Int, isLocked: Boolean, isVisible: Boolean, tags: Set<String>): ScadaComponent

    /**
     * Validate component configuration
     */
    fun validate(): List<ValidationError>
}

/**
 * Component types enumeration
 */
enum class ComponentType {
    PUSH_BUTTON,
    TOGGLE_SWITCH,
    SLIDER,
    NUMERIC_INPUT,
    VALUE_DISPLAY,
    INDICATOR_LIGHT,
    GAUGE,
    LINE_CHART,
    GROUP_BOX
}

/**
 * Position of a component on the workspace
 */
@Serializable
data class ComponentPosition(
    val x: Float = 0f,
    val y: Float = 0f
)

/**
 * Size of a component
 */
@Serializable
data class ComponentSize(
    @Contextual val width: Dp = 100.dp,
    @Contextual val height: Dp = 50.dp
)

/**
 * Base configuration interface for all components
 */
interface ComponentConfig {
    val label: String
    val backgroundColor: Color
    val borderColor: Color
    val borderWidth: Dp
}

/**
 * Data binding configuration for components that interact with communication protocols
 */
@Serializable
data class DataBinding(
    val protocol: CommunicationProtocol? = null,
    val address: String = "", // MQTT topic, BLE characteristic UUID, etc.
    val dataFormat: DataFormat = DataFormat.STRING,
    val payload: String = "", // For write operations
    val prefix: String = "",
    val suffix: String = "",
    val jsonPath: String = "", // For JSON data parsing
    val decimalPlaces: Int = 2
)

/**
 * Data format types
 */
enum class DataFormat {
    STRING,
    INTEGER,
    FLOAT,
    BOOLEAN,
    JSON
}

/**
 * Color condition for indicator lights and gauges
 */
@Serializable
data class ColorCondition(
    val condition: String, // e.g., "value > 90", "value == true"
    @Contextual val color: Color,
    val flashing: Boolean = false
)

/**
 * Validation error for component configuration
 */
data class ValidationError(
    val field: String,
    val message: String,
    val severity: ValidationSeverity = ValidationSeverity.ERROR
)

/**
 * Validation severity levels
 */
enum class ValidationSeverity {
    INFO,
    WARNING,
    ERROR
}

/**
 * Component group for organization
 */
@Serializable
data class ComponentGroup(
    val id: String,
    val name: String,
    val color: @Contextual Color = Color.Gray,
    val description: String = "",
    val isCollapsed: Boolean = false
)