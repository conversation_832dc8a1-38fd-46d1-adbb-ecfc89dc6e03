package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.scada.ComponentType
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.components.*

/**
 * Renders individual SCADA components based on their type
 */
@Composable
fun ScadaComponentRenderer(
    component: ScadaComponent,
    isSelected: Boolean = false,
    onComponentClick: () -> Unit = {}
) {
    // Don't render invisible components
    if (!component.isVisible) return

    val baseModifier = Modifier
        .offset(
            x = component.position.x.dp,
            y = component.position.y.dp
        )
        .width(component.size.width)
        .height(component.size.height)
        .clickable(enabled = !component.isLocked, onClick = onComponentClick)

    // Apply selection and lock indicators
    val finalModifier = when {
        isSelected && component.isLocked -> {
            baseModifier
                .border(3.dp, Color.Red, RoundedCornerShape(4.dp))
                .border(1.dp, Color.Blue, RoundedCornerShape(6.dp))
        }
        isSelected -> {
            baseModifier.border(2.dp, Color.Blue, RoundedCornerShape(4.dp))
        }
        component.isLocked -> {
            baseModifier.border(2.dp, Color.Red, RoundedCornerShape(4.dp))
        }
        else -> baseModifier
    }

    when (component.type) {
        ComponentType.PUSH_BUTTON -> {
            PushButtonRenderer(
                component = component as PushButton,
                modifier = finalModifier,
                isSelected = isSelected
            )
        }
        ComponentType.VALUE_DISPLAY -> {
            ValueDisplayRenderer(
                component = component as ValueDisplay,
                modifier = finalModifier,
                isSelected = isSelected
            )
        }
        ComponentType.TOGGLE_SWITCH -> {
            ToggleSwitchRenderer(
                component = component as com.tfkcolin.cebsscada.scada.components.ToggleSwitch,
                modifier = finalModifier,
                isSelected = isSelected
            )
        }
        ComponentType.SLIDER -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Slider"
            )
        }
        ComponentType.NUMERIC_INPUT -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Numeric Input"
            )
        }
        ComponentType.INDICATOR_LIGHT -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Indicator Light"
            )
        }
        ComponentType.GAUGE -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Gauge"
            )
        }
        ComponentType.LINE_CHART -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Line Chart"
            )
        }
        ComponentType.GROUP_BOX -> {
            PlaceholderRenderer(
                component = component,
                modifier = finalModifier,
                isSelected = isSelected,
                name = "Group Box"
            )
        }
    }
}

/**
 * Push Button Renderer
 */
@Composable
fun PushButtonRenderer(
    component: PushButton,
    modifier: Modifier,
    isSelected: Boolean
) {
    val config = component.getTypedConfig()

    Button(
        onClick = { component.onPressed() },
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(
            containerColor = config.backgroundColor
        ),
        shape = when (config.buttonShape) {
            ButtonShape.CIRCLE -> androidx.compose.foundation.shape.CircleShape
            else -> RoundedCornerShape(8.dp)
        }
    ) {
        Text(
            text = config.label,
            color = config.textColor,
            fontSize = config.textSize
        )
    }
}

/**
 * Value Display Renderer
 */
@Composable
fun ValueDisplayRenderer(
    component: ValueDisplay,
    modifier: Modifier,
    isSelected: Boolean
) {
    val config = component.getTypedConfig()

    Box(
        modifier = modifier
            .background(config.backgroundColor)
            .border(config.borderWidth, config.borderColor, RoundedCornerShape(4.dp)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = component.currentValue,
            color = config.textColor,
            fontSize = config.textSize,
            fontWeight = when (config.fontWeight) {
                FontWeight.BOLD -> androidx.compose.ui.text.font.FontWeight.Bold
                else -> androidx.compose.ui.text.font.FontWeight.Normal
            }
        )
    }
}

/**
 * Toggle Switch Renderer
 */
@Composable
fun ToggleSwitchRenderer(
    component: com.tfkcolin.cebsscada.scada.components.ToggleSwitch,
    modifier: Modifier,
    isSelected: Boolean
) {
    val config = component.getTypedConfig()

    Column(
        modifier = modifier
            .background(config.backgroundColor)
            .border(config.borderWidth, config.borderColor, RoundedCornerShape(8.dp))
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Label
        if (config.label.isNotEmpty()) {
            Text(
                text = config.label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        // Switch
        Switch(
            checked = component.isChecked,
            onCheckedChange = { checked ->
                component.onToggle(checked)
            },
            colors = SwitchDefaults.colors(
                checkedThumbColor = config.onColor,
                checkedTrackColor = config.onColor.copy(alpha = 0.5f),
                uncheckedThumbColor = config.offColor,
                uncheckedTrackColor = config.offColor.copy(alpha = 0.5f)
            )
        )
    }
}

/**
 * Placeholder renderer for unimplemented component types
 */
@Composable
fun PlaceholderRenderer(
    component: ScadaComponent,
    modifier: Modifier,
    isSelected: Boolean,
    name: String
) {
    Box(
        modifier = modifier.background(Color.LightGray),
        contentAlignment = Alignment.Center
    ) {
        Text("$name\n${component.config.label}")
    }
}
