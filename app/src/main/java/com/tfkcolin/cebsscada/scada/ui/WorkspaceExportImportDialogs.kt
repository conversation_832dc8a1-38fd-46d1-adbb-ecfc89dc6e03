package com.tfkcolin.cebsscada.scada.ui

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.scada.WorkspaceExporter
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for workspace export/import operations
 */
@HiltViewModel
class WorkspaceExportImportViewModel @Inject constructor(
    private val workspaceExporter: WorkspaceExporter
) : androidx.lifecycle.ViewModel() {

    private val _isLoading = mutableStateOf(false)
    val isLoading: Boolean = _isLoading.value

    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: String? = _errorMessage.value

    private val _successMessage = mutableStateOf<String?>(null)
    val successMessage: String? = _successMessage.value

    /**
     * Export workspace to file
     */
    fun exportWorkspace(workspace: WorkspaceEntity, fileUri: Uri, onSuccess: () -> Unit) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = workspaceExporter.exportWorkspaceToFile(workspace.id, fileUri)
                when (result) {
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Success -> {
                        _successMessage.value = "Workspace '${workspace.name}' exported successfully"
                        onSuccess()
                    }
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = "Export failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Import workspace from file
     */
    fun importWorkspace(fileUri: Uri, newName: String? = null, onSuccess: (Long) -> Unit) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = workspaceExporter.importWorkspaceFromFile(fileUri, newName)
                when (result) {
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Success -> {
                        _successMessage.value = "Workspace imported successfully"
                        onSuccess(result.data)
                    }
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = "Import failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear messages
     */
    fun clearMessages() {
        _errorMessage.value = null
        _successMessage.value = null
    }

    /**
     * Get suggested filename for workspace
     */
    fun getSuggestedFilename(workspaceName: String): String {
        return workspaceExporter.getSuggestedFilename(workspaceName)
    }
}

/**
 * Dialog for exporting a workspace
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceExportDialog(
    workspace: WorkspaceEntity,
    onDismiss: () -> Unit,
    viewModel: WorkspaceExportImportViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val isLoading = viewModel.isLoading
    val errorMessage = viewModel.errorMessage
    val successMessage = viewModel.successMessage

    var selectedUri by remember { mutableStateOf<Uri?>(null) }

    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("application/json")
    ) { uri ->
        selectedUri = uri
        uri?.let {
            viewModel.exportWorkspace(workspace, it) {
                // Export successful
            }
        }
    }

    // Auto-dismiss on success
    LaunchedEffect(successMessage) {
        successMessage?.let {
            kotlinx.coroutines.delay(2000)
            onDismiss()
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = !isLoading,
            dismissOnClickOutside = !isLoading
        ),
        title = {
            Text("Export Workspace")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Workspace info
                Card(modifier = Modifier.fillMaxWidth()) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = workspace.name,
                            style = MaterialTheme.typography.titleMedium
                        )
                        if (workspace.description.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = workspace.description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "${workspace.componentCount} components",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                // Export format info
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(12.dp)) {
                        Text(
                            text = "Export Format",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Workspaces are exported as JSON files (.scada) containing all components, their configurations, and data binding settings.",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // Status messages
                errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                successMessage?.let { success ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Text(
                            text = success,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val suggestedName = viewModel.getSuggestedFilename(workspace.name)
                    filePickerLauncher.launch(suggestedName)
                },
                enabled = !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("Choose Export Location")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Dialog for importing a workspace
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceImportDialog(
    onDismiss: () -> Unit,
    onWorkspaceImported: (Long) -> Unit,
    viewModel: WorkspaceExportImportViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val isLoading = viewModel.isLoading
    val errorMessage = viewModel.errorMessage
    val successMessage = viewModel.successMessage

    var newWorkspaceName by remember { mutableStateOf("") }
    var useCustomName by remember { mutableStateOf(false) }

    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            viewModel.importWorkspace(it, if (useCustomName) newWorkspaceName else null) { workspaceId ->
                onWorkspaceImported(workspaceId)
            }
        }
    }

    // Auto-dismiss on success
    LaunchedEffect(successMessage) {
        successMessage?.let {
            kotlinx.coroutines.delay(2000)
            onDismiss()
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = !isLoading,
            dismissOnClickOutside = !isLoading
        ),
        title = {
            Text("Import Workspace")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Import info
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(12.dp)) {
                        Text(
                            text = "Import Workspace",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Select a .scada or .json file to import. If a workspace with the same name already exists, it will be imported with a unique name.",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // Custom name option
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = useCustomName,
                        onCheckedChange = { useCustomName = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Use custom name for imported workspace",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (useCustomName) {
                    OutlinedTextField(
                        value = newWorkspaceName,
                        onValueChange = { newWorkspaceName = it },
                        label = { Text("Workspace Name") },
                        placeholder = { Text("Enter custom name") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isLoading
                    )
                }

                // Status messages
                errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                successMessage?.let { success ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Text(
                            text = success,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = { filePickerLauncher.launch("*/*") },
                enabled = !isLoading && (!useCustomName || newWorkspaceName.isNotBlank())
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("Select File to Import")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Quick export/import menu for workspace actions
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceShareMenu(
    workspace: WorkspaceEntity,
    onExportClick: () -> Unit,
    onImportClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        IconButton(onClick = { expanded = true }) {
            Icon(Icons.Default.Share, contentDescription = "Share workspace")
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DropdownMenuItem(
                text = { Text("Export Workspace") },
                leadingIcon = {
                    Icon(Icons.Default.FileDownload, contentDescription = null)
                },
                onClick = {
                    expanded = false
                    onExportClick()
                }
            )

            DropdownMenuItem(
                text = { Text("Import Workspace") },
                leadingIcon = {
                    Icon(Icons.Default.FileUpload, contentDescription = null)
                },
                onClick = {
                    expanded = false
                    onImportClick()
                }
            )
        }
    }
}