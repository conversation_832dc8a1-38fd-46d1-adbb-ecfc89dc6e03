package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Service
import android.bluetooth.*
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlin.coroutines.suspendCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.io.IOException
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue

@AndroidEntryPoint
class BluetoothService : Service() {
    private val binder = LocalBinder()
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var classicConnection: ClassicConnection? = null
    private var bleConnection: BleConnection? = null

    // Synchronization lock for connection management
    private val connectionLock = Any()

    // Coroutine scope for service operations
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Callback for data reception (for SCADA communication)
    var onDataReceived: ((ByteArray) -> Unit)? = null

    // Callback for BLE services discovered
    var onBleServicesDiscovered: ((List<BluetoothGattService>) -> Unit)? = null

    // Error propagation
    private val _connectionErrors = MutableSharedFlow<String>()
    val connectionErrors: SharedFlow<String> = _connectionErrors.asSharedFlow()

    // Connection health monitoring
    private var connectionMonitorJob: Job? = null
    private var lastDataReceivedTime = 0L

    companion object {
        const val TAG = "BluetoothService"
        val UUID_SPP: UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val INITIAL_RETRY_DELAY_MS = 1000L
        private const val CONNECTION_HEALTH_CHECK_INTERVAL = 30000L // 30 seconds
        private const val CONNECTION_TIMEOUT_THRESHOLD = 60000L // 1 minute
    }

    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
    }

    override fun onBind(intent: Intent): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        disconnect()
    }

    /**
     * Retry a suspend operation with exponential backoff
     */
    private suspend fun <T> retryWithBackoff(
        maxAttempts: Int = MAX_RETRY_ATTEMPTS,
        initialDelay: Long = INITIAL_RETRY_DELAY_MS,
        operation: suspend () -> T
    ): T {
        var currentDelay = initialDelay
        repeat(maxAttempts - 1) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                Log.w(TAG, "Operation failed on attempt ${attempt + 1}, retrying in ${currentDelay}ms", e)
                kotlinx.coroutines.delay(currentDelay)
                currentDelay *= 2 // Exponential backoff
            }
        }
        // Last attempt
        return operation()
    }

    /**
     * Perform Classic Bluetooth connection with proper coroutine handling
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun performClassicConnection(connection: ClassicConnection) = withContext(Dispatchers.IO) {
        suspendCancellableCoroutine<Unit> { continuation ->
            // Store the continuation to resume when connection completes
            val originalOnDataReceived = onDataReceived
            var connectionCompleted = false

            // Set up a temporary callback to detect connection success/failure
            val tempCallback: ((ByteArray) -> Unit)? = { data ->
                if (!connectionCompleted) {
                    connectionCompleted = true
                    if (continuation.isActive) {
                        continuation.resume(Unit) {}
                    }
                }
                // Forward to original callback
                originalOnDataReceived?.invoke(data)
            }

            onDataReceived = tempCallback

            // Create a timeout to handle connection failures
            val timeoutJob = serviceScope.launch {
                kotlinx.coroutines.delay(BluetoothConstants.CONNECTION_TIMEOUT.toLong())
                if (!connectionCompleted && continuation.isActive) {
                    connectionCompleted = true
                    onDataReceived = originalOnDataReceived
                    continuation.cancel(Exception("Connection timeout"))
                }
            }

            try {
                connection.connect()

                // Wait for either success (data received) or timeout
                continuation.invokeOnCancellation {
                    timeoutJob.cancel()
                    onDataReceived = originalOnDataReceived
                    connection.disconnect()
                }
            } catch (e: Exception) {
                timeoutJob.cancel()
                onDataReceived = originalOnDataReceived
                throw e
            }
        }
    }

    /**
     * Perform BLE connection with proper coroutine handling
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun performBLEConnection(connection: BleConnection) = withContext(Dispatchers.Main) {
        suspendCancellableCoroutine<Unit> { continuation ->
            var connectionCompleted = false

            // Create a timeout to handle connection failures
            val timeoutJob = serviceScope.launch {
                kotlinx.coroutines.delay(BluetoothConstants.CONNECTION_TIMEOUT.toLong())
                if (!connectionCompleted && continuation.isActive) {
                    connectionCompleted = true
                    continuation.cancel(Exception("BLE connection timeout"))
                }
            }

            try {
                connection.connect()

                // Wait for connection state change via callback
                // This is a simplified approach - in practice, we'd need to monitor connection state
                serviceScope.launch {
                    while (!connectionCompleted && continuation.isActive) {
                        if (connection.isConnected()) {
                            connectionCompleted = true
                            if (continuation.isActive) {
                                continuation.resume(Unit) {}
                            }
                            break
                        }
                        kotlinx.coroutines.delay(100) // Check every 100ms
                    }
                }

                continuation.invokeOnCancellation {
                    timeoutJob.cancel()
                    connection.disconnect()
                }
            } catch (e: Exception) {
                timeoutJob.cancel()
                throw e
            }
        }
    }

    fun initialize(): Boolean {
        if (bluetoothAdapter == null) {
            Log.e(TAG, "Bluetooth adapter not available")
            return false
        }
        if (!bluetoothAdapter!!.isEnabled) {
            Log.w(TAG, "Bluetooth is not enabled")
            return false
        }
        return true
    }

    fun connect(device: BluetoothDevice, useBLE: Boolean) {
        synchronized(connectionLock) {
            Log.d(TAG, "Connect called - Device: ${device.name ?: "Unknown"} (${device.address}), useBLE: $useBLE")

            // Check Bluetooth adapter availability
            if (bluetoothAdapter == null) {
                Log.e(TAG, "Bluetooth adapter not available")
                return
            }

            // Check permissions using centralized utility
            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Check if Bluetooth is enabled
            if (!bluetoothAdapter!!.isEnabled) {
                Log.e(TAG, "Bluetooth is not enabled")
                return
            }

            Log.d(TAG, "Bluetooth connect permission granted, proceeding with connection")
            disconnect() // Disconnect any existing connection

            serviceScope.launch {
                try {
                    retryWithBackoff {
                        if (useBLE) {
                            Log.d(TAG, "Initializing BLE connection with retry")
                            bleConnection = BleConnection(device)
                            performBLEConnection(bleConnection!!)
                        } else {
                            Log.d(TAG, "Initializing Classic Bluetooth connection with retry")
                            classicConnection = ClassicConnection(device)
                            performClassicConnection(classicConnection!!)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "All connection attempts failed", e)
                    _connectionErrors.emit("Connection failed after retries: ${e.message}")
                }
            }
        }
    }

    fun write(bytes: ByteArray, serviceUuid: UUID? = null, characteristicUuid: UUID? = null) {
        // Validate connection state before writing
        val classicConnected = classicConnection?.isConnected() == true
        val bleConnected = bleConnection?.isConnected() == true

        if (!classicConnected && !bleConnected) {
            Log.w(TAG, "Cannot write: no active connection")
            return
        }

        if (classicConnected) {
            classicConnection?.write(bytes)
        }
        if (bleConnected && serviceUuid != null && characteristicUuid != null) {
            bleConnection?.write(bytes, serviceUuid, characteristicUuid)
        }
    }

    fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
        if (bleConnection?.isConnected() != true) {
            Log.w(TAG, "Cannot read characteristic: BLE not connected")
            return
        }
        bleConnection?.readCharacteristic(characteristic)
    }

    fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        if (bleConnection?.isConnected() != true) {
            Log.w(TAG, "Cannot enable notifications: BLE not connected")
            return
        }
        bleConnection?.enableNotifications(serviceUuid, characteristicUuid)
    }

    fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        if (bleConnection?.isConnected() != true) {
            Log.w(TAG, "Cannot disable notifications: BLE not connected")
            return
        }
        bleConnection?.disableNotifications(serviceUuid, characteristicUuid)
    }

    fun getSupportedGattServices(): List<BluetoothGattService>? {
        if (bleConnection?.isConnected() != true) {
            Log.w(TAG, "Cannot get GATT services: BLE not connected")
            return null
        }
        return bleConnection?.getSupportedGattServices()
    }

    fun disconnect() {
        synchronized(connectionLock) {
            classicConnection?.disconnect()
            bleConnection?.disconnect()
            classicConnection = null
            bleConnection = null
        }
    }

    // Broadcast methods removed - using callbacks and direct communication instead

    private abstract inner class Connection(protected val device: BluetoothDevice) {
        protected val handler = Handler(Looper.getMainLooper())
        protected var connectionState = BluetoothConstants.STATE_NONE

        abstract fun connect()
        abstract fun disconnect()

        protected val timeoutRunnable = Runnable {
            if (connectionState == BluetoothConstants.STATE_CONNECTING) {
                disconnect()
                Log.e(TAG, "Connection timeout")
            }
        }

        protected fun startTimeout() {
            handler.postDelayed(timeoutRunnable, BluetoothConstants.CONNECTION_TIMEOUT)
        }

        protected fun stopTimeout() {
            handler.removeCallbacks(timeoutRunnable)
        }

        fun isConnected(): Boolean {
            return connectionState == BluetoothConstants.STATE_CONNECTED
        }
    }

    private inner class ClassicConnection(device: BluetoothDevice) : Connection(device) {
        private var socket: BluetoothSocket? = null
        private var connectedThread: ConnectedThread? = null

        override fun connect() {
            Log.d(TAG, "ClassicConnection.connect() called for device: ${device.name ?: "Unknown"} (${device.address})")
            if (connectionState != BluetoothConstants.STATE_NONE) {
                Log.w(TAG, "Connection attempt ignored - already in state: $connectionState")
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            startTimeout()
            Log.d(TAG, "Starting connection coroutine")

            serviceScope.launch {
                try {
                    // Check permissions using centralized utility
                    if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                        val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                        Log.e(TAG, "$requiredPermission permission not granted in connection coroutine")
                        stopTimeout() // Ensure timeout is cancelled on error
                        _connectionErrors.emit("Permission denied: $requiredPermission")
                        return@launch
                    }
                    Log.d(TAG, "Creating RFCOMM socket with UUID: $UUID_SPP")
                    socket = device.createRfcommSocketToServiceRecord(UUID_SPP)
                    Log.d(TAG, "Socket created successfully: ${socket != null}")
                    Log.d(TAG, "Cancelling any ongoing discovery")
                    bluetoothAdapter?.cancelDiscovery() ?: Log.w(TAG, "Bluetooth adapter is null, cannot cancel discovery")
                    Log.d(TAG, "Attempting socket connection...")
                    socket?.connect()
                    Log.d(TAG, "Socket connection successful")
                    stopTimeout()
                    connected(socket!!)
                } catch (e: IOException) {
                    Log.e(TAG, "Socket connection error", e)
                    Log.e(TAG, "Connection failed for device: ${device.name ?: "Unknown"} (${device.address})")
                    disconnect()
                    _connectionErrors.emit("Connection failed: ${e.message}")
                } catch (e: CancellationException) {
                    Log.d(TAG, "Connection coroutine cancelled")
                    disconnect()
                }
            }
        }

        private fun connected(socket: BluetoothSocket) {
            connectionState = BluetoothConstants.STATE_CONNECTED
            connectedThread = ConnectedThread(socket)
            connectedThread?.start()
            Log.d(TAG, "Classic Bluetooth connection established")
        }

        fun write(bytes: ByteArray) {
            connectedThread?.write(bytes)
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE
            connectedThread?.cancel()
            connectedThread = null
            try {
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "Could not close the client socket", e)
            }
            // Disconnected - state will be communicated through callbacks
        }

        private inner class ConnectedThread(private val mmSocket: BluetoothSocket) {
            private val mmInStream = mmSocket.inputStream
            private val mmOutStream = mmSocket.outputStream
            private val messageChannel = Channel<ByteArray>(Channel.UNLIMITED)
            private var readJob: Job? = null
            private var writeJob: Job? = null

            fun start() {
                readJob = serviceScope.launch {
                    val buffer = ByteArray(1024)
                    var bytes: Int
                    try {
                        while (isActive && connectionState == BluetoothConstants.STATE_CONNECTED) {
                            // Use available() to check if data is ready before blocking read
                            if (mmInStream.available() > 0) {
                                bytes = mmInStream.read(buffer)
                                if (bytes > 0) {
                                    val data = buffer.copyOf(bytes)
                                    Log.d(TAG, "Received ${bytes} bytes from ${device.name ?: device.address}")
                                    // For SCADA applications, we'll pass data through a listener pattern
                                    onDataReceived?.invoke(data)
                                }
                            } else {
                                // Sleep briefly to avoid busy waiting
                                kotlinx.coroutines.delay(10)
                            }
                        }
                    } catch (e: IOException) {
                        if (isActive) { // Only log error if not cancelled
                            Log.d(TAG, "Input stream was disconnected", e)
                            disconnect()
                            Log.e(TAG, "Connection lost")
                        }
                    }
                }

                writeJob = serviceScope.launch {
                    try {
                        Log.d(TAG, "Started message writer coroutine for HC-05/HC-06 communication")
                        for (bytes in messageChannel) {
                            Log.d(TAG, "Writing ${bytes.size} bytes to HC-05/HC-06: ${String(bytes)}")
                            mmOutStream.write(bytes)
                            mmOutStream.flush() // Important for immediate transmission
                            Log.d(TAG, "Message written successfully to HC-05/HC-06")
                        }
                    } catch (e: IOException) {
                        if (isActive) { // Only log error and disconnect if not cancelled
                            Log.e(TAG, "Error occurred when sending data to HC-05/HC-06", e)
                            disconnect()
                        }
                    }
                }
            }

            fun write(bytes: ByteArray) {
                serviceScope.launch {
                    try {
                        Log.d(TAG, "Sending message via channel, length: ${bytes.size} bytes")
                        messageChannel.send(bytes)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to send message through channel", e)
                    }
                }
            }

            fun cancel() {
                readJob?.cancel()
                writeJob?.cancel()
                messageChannel.close()
            }
        }
    }

    private inner class BleConnection(device: BluetoothDevice) : Connection(device) {
        private var gatt: BluetoothGatt? = null

        private val gattCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                    val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                    Log.e(TAG, "$requiredPermission permission not granted")
                    stopTimeout() // Ensure timeout is cancelled on error
                    return
                }
                stopTimeout()
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        connectionState = BluetoothConstants.STATE_CONNECTED
                        <EMAIL> = gatt
                        // Connection established - state will be communicated through callbacks
                        gatt.discoverServices()
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        disconnect()
                    }
                } else {
                    disconnect()
                    Log.e(TAG, "BLE connection failed")
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    val services = gatt.services
                    Log.d(TAG, "BLE services discovered successfully: ${services.size} services")
                    onBleServicesDiscovered?.invoke(services)
                } else {
                    disconnect()
                    Log.e(TAG, "BLE services discovery failed")
                }
            }

            override fun onCharacteristicWrite(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "BLE characteristic write successful")
                } else {
                    Log.e(TAG, "BLE characteristic write failed")
                }
            }

            override fun onCharacteristicRead(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    characteristic.value?.let { data ->
                        Log.d(TAG, "BLE characteristic read successful, data length: ${data.size}")
                        // Data will be handled through callbacks or direct communication
                    }
                } else {
                    Log.e(TAG, "BLE characteristic read failed")
                }
            }

            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, value: ByteArray) {
                if (value.isNotEmpty()) {
                    Log.d(TAG, "BLE characteristic changed, data length: ${value.size}")
                    // Data will be handled through callbacks or direct communication
                    onDataReceived?.invoke(value)
                } else {
                    Log.w(TAG, "BLE characteristic changed with empty value")
                }
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt, descriptor: BluetoothGattDescriptor, status: Int) {
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    Log.e(TAG, "BLE descriptor write failed")
                }
            }
        }

        override fun connect() {
            if (connectionState != BluetoothConstants.STATE_NONE) return

            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            startTimeout()
            gatt = device.connectGatt(this@BluetoothService, false, gattCallback)

            // Add null check for connectGatt result
            if (gatt == null) {
                Log.e(TAG, "Failed to create GATT connection")
                connectionState = BluetoothConstants.STATE_NONE
                stopTimeout()
            }
        }

        fun write(bytes: ByteArray, serviceUuid: UUID?, characteristicUuid: UUID?) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED || serviceUuid == null || characteristicUuid == null) {
                Log.w(TAG, "Invalid write parameters or not connected")
                return
            }

            val gattInstance = gatt
            if (gattInstance == null) {
                Log.e(TAG, "GATT is null, cannot write")
                return
            }

            val service = gattInstance.getService(serviceUuid)
            if (service == null) {
                Log.e(TAG, "BLE service not found: $serviceUuid")
                return
            }

            val characteristic = service.getCharacteristic(characteristicUuid)
            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found: $characteristicUuid")
                return
            }

            // Check if characteristic supports writing
            val canWrite = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                           (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0

            if (!canWrite) {
                Log.e(TAG, "BLE characteristic does not support writing: ${characteristic.properties}")
                return
            }

            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Set the value and write type
            characteristic.value = bytes
            characteristic.writeType = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
            } else {
                BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            }

            val writeResult = gattInstance.writeCharacteristic(characteristic)
            if (writeResult) {
                Log.d(TAG, "BLE characteristic write initiated successfully")
            } else {
                Log.e(TAG, "BLE characteristic write failed to initiate")
            }
        }

        fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }
            gatt?.readCharacteristic(characteristic)
        }

        fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) {
                Log.w(TAG, "Cannot enable notifications: not connected")
                return
            }

            val gattInstance = gatt
            if (gattInstance == null) {
                Log.e(TAG, "GATT is null, cannot enable notifications")
                return
            }

            val service = gattInstance.getService(serviceUuid)
            if (service == null) {
                Log.e(TAG, "BLE service not found: $serviceUuid")
                return
            }

            val characteristic = service.getCharacteristic(characteristicUuid)
            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found: $characteristicUuid")
                return
            }

            // Check if characteristic supports notifications or indications
            val canNotify = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                            (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0

            if (!canNotify) {
                Log.e(TAG, "BLE characteristic does not support notifications: ${characteristic.properties}")
                return
            }

            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            serviceScope.launch {
                try {
                    retryWithBackoff { enableNotificationsInternal(gattInstance, characteristic) }
                    Log.d(TAG, "Successfully enabled notifications for characteristic: $characteristicUuid")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to enable notifications after retries", e)
                    _connectionErrors.emit("Failed to enable notifications: ${e.message}")
                }
            }
        }

        private suspend fun enableNotificationsInternal(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic) {
            // Enable local notifications
            val notificationEnabled = gatt.setCharacteristicNotification(characteristic, true)
            if (!notificationEnabled) {
                throw Exception("Failed to enable local notifications")
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                val value = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                } else {
                    BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                }

                descriptor.value = value
                val writeResult = gatt.writeDescriptor(descriptor)
                if (!writeResult) {
                    throw Exception("Failed to write descriptor")
                }
            } else {
                throw Exception("CCCD descriptor not found")
            }
        }

        fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) return
            val gattInstance = gatt
            if (gattInstance == null) {
                Log.e(TAG, "GATT is null, cannot disable notifications")
                return
            }
            val service = gattInstance.getService(serviceUuid)
            if (service == null) {
                Log.e(TAG, "BLE service not found: $serviceUuid")
                return
            }
            val characteristic = service.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found")
                return
            }

            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Disable local notifications
            val notificationDisabled = gatt?.setCharacteristicNotification(characteristic, false)
            if (notificationDisabled != true) {
                Log.e(TAG, "Failed to disable BLE characteristic notification")
                return
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                descriptor.value = BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                val writeResult = gatt?.writeDescriptor(descriptor)
                if (writeResult != true) {
                    Log.e(TAG, "Failed to write BLE descriptor")
                }
            }
        }

        fun getSupportedGattServices(): List<BluetoothGattService>? {
            return gatt?.services
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE

            if (!BluetoothPermissionManager.hasBluetoothConnectPermission(this@BluetoothService)) {
                val requiredPermission = BluetoothPermissionManager.getRequiredBluetoothConnectPermission()
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            gatt?.let { gattInstance ->
                gattInstance.disconnect()
                gattInstance.close()
            }
            gatt = null
            Log.d(TAG, "BLE connection disconnected")
        }
    }
}
