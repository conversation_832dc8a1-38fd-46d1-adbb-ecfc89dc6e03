package com.tfkcolin.cebsscada.services

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dedicated service for Classical Bluetooth communication
 * Optimized for HC-05/HC-06 modules and SCADA device communication
 */
@Singleton
class ClassicalBluetoothService @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) {
    companion object {
        private const val TAG = "ClassicalBluetoothService"
        private val UUID_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
        private const val RECONNECT_DELAY_MS = 5000L
        const val MAX_RECONNECT_ATTEMPTS = 3
    }

    // Connection state
    private var bluetoothSocket: BluetoothSocket? = null
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    private var connectedDevice: BluetoothDevice? = null
    private var isAutoReconnectEnabled = true
    private val _reconnectAttempts = MutableStateFlow(0)
    val reconnectAttempts: StateFlow<Int> = _reconnectAttempts.asStateFlow()
    private var reconnectJob: Job? = null

    // Coroutine scope for service operations
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // State flows
    private val _connectionState = MutableStateFlow(ClassicalBluetoothConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ClassicalBluetoothConnectionState> = _connectionState.asStateFlow()

    private val _connectedDevice = MutableStateFlow<BluetoothDevice?>(null)
    val connectedDeviceFlow: StateFlow<BluetoothDevice?> = _connectedDevice.asStateFlow()

    private val _receivedData = MutableSharedFlow<ClassicalBluetoothData>()
    val receivedData: SharedFlow<ClassicalBluetoothData> = _receivedData.asSharedFlow()

    private val _connectionErrors = MutableSharedFlow<String>()
    val connectionErrors: SharedFlow<String> = _connectionErrors.asSharedFlow()

    // Statistics
    private var connectionStartTime = 0L
    private var bytesSent = 0L
    private var bytesReceived = 0L
    private var messagesSent = 0L
    private var messagesReceived = 0L

    /**
     * Connect to a Classical Bluetooth device
     */
    suspend fun connect(device: BluetoothDevice): Boolean {
        _reconnectAttempts.value = 0
        return internalConnect(device)
    }

    /**
     * Connect to a Classical Bluetooth device
     */
    private suspend fun internalConnect(device: BluetoothDevice): Boolean = withContext(Dispatchers.IO) {
        if (_connectionState.value == ClassicalBluetoothConnectionState.CONNECTED) {
            disconnect()
        }

        _connectionState.value = ClassicalBluetoothConnectionState.CONNECTING

        try {
            Log.d(TAG, "Connecting to device: ${device.name} (${device.address})")
            
            // Cancel discovery to improve connection reliability
            bluetoothAdapter?.cancelDiscovery()
            
            // Create RFCOMM socket
            val socket = device.createRfcommSocketToServiceRecord(UUID_SPP)
            
            // Attempt connection
            socket.connect()
            
            // Connection successful
            bluetoothSocket = socket
            inputStream = socket.inputStream
            outputStream = socket.outputStream
            connectedDevice = device
            connectionStartTime = System.currentTimeMillis()
            
            _connectionState.value = ClassicalBluetoothConnectionState.CONNECTED
            _connectedDevice.value = device
            
            // Start listening for incoming data
            startDataListener()
            
            Log.d(TAG, "Successfully connected to ${device.name}")
            true
            
        } catch (e: IOException) {
            Log.e(TAG, "Connection failed: ${e.message}", e)
            _connectionState.value = ClassicalBluetoothConnectionState.ERROR
            _connectionErrors.emit("Connection failed: ${e.message}")
            
            // Attempt auto-reconnect if enabled
            if (isAutoReconnectEnabled && _reconnectAttempts.value < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect(device)
            }
            false
        }
    }

    /**
     * Disconnect from the current device
     */
    suspend fun disconnect() = withContext(Dispatchers.IO) {
        _connectionState.value = ClassicalBluetoothConnectionState.DISCONNECTING
        
        try {
            bluetoothSocket?.close()
        } catch (e: IOException) {
            Log.w(TAG, "Error closing socket: ${e.message}")
        }
        
        bluetoothSocket = null
        inputStream = null
        outputStream = null
        connectedDevice = null
        
        _connectionState.value = ClassicalBluetoothConnectionState.DISCONNECTED
        _connectedDevice.value = null
        
        Log.d(TAG, "Disconnected from device")
    }

    /**
     * Send raw data to the connected device
     */
    suspend fun sendData(data: ByteArray): Boolean = withContext(Dispatchers.IO) {
        val stream = outputStream
        if (stream == null || _connectionState.value != ClassicalBluetoothConnectionState.CONNECTED) {
            _connectionErrors.emit("Not connected to device")
            return@withContext false
        }

        try {
            stream.write(data)
            stream.flush()
            
            bytesSent += data.size
            messagesSent++
            
            Log.d(TAG, "Sent ${data.size} bytes: ${String(data)}")
            true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to send data: ${e.message}", e)
            _connectionErrors.emit("Send failed: ${e.message}")
            
            // Connection may be lost, trigger reconnect
            if (isAutoReconnectEnabled) {
                connectedDevice?.let { device ->
                    scheduleReconnect(device)
                }
            }
            false
        }
    }

    /**
     * Send text message to the connected device
     */
    suspend fun sendText(text: String): Boolean {
        return sendData(text.toByteArray())
    }

    /**
     * Send AT command to HC-05/HC-06 module
     */
    suspend fun sendATCommand(command: String): Boolean {
        val atCommand = if (command.startsWith("AT")) command else "AT+$command"
        val commandWithTerminator = "$atCommand\r\n"
        return sendData(commandWithTerminator.toByteArray())
    }

    /**
     * Send SCADA command
     */
    suspend fun sendSCADACommand(command: String): Boolean {
        val scadaCommand = "$command\n"
        return sendData(scadaCommand.toByteArray())
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): ClassicalBluetoothStats {
        val uptime = if (connectionStartTime > 0) {
            System.currentTimeMillis() - connectionStartTime
        } else 0L

        return ClassicalBluetoothStats(
            isConnected = _connectionState.value == ClassicalBluetoothConnectionState.CONNECTED,
            connectedDeviceName = connectedDevice?.name ?: "None",
            connectedDeviceAddress = connectedDevice?.address ?: "None",
            connectionUptime = uptime,
            bytesSent = bytesSent,
            bytesReceived = bytesReceived,
            messagesSent = messagesSent,
            messagesReceived = messagesReceived
        )
    }

    /**
     * Enable or disable auto-reconnect
     */
    fun setAutoReconnectEnabled(enabled: Boolean) {
        isAutoReconnectEnabled = enabled
        Log.d(TAG, "Auto-reconnect ${if (enabled) "enabled" else "disabled"}")
    }

    /**
     * Start listening for incoming data
     */
    private fun startDataListener() {
        serviceScope.launch {
            val stream = inputStream ?: return@launch
            val buffer = ByteArray(1024)

            try {
                while (_connectionState.value == ClassicalBluetoothConnectionState.CONNECTED) {
                    val bytesRead = stream.read(buffer)
                    if (bytesRead > 0) {
                        val data = buffer.copyOf(bytesRead)
                        val dataString = String(data)
                        
                        bytesReceived += bytesRead
                        messagesReceived++
                        
                        val bluetoothData = ClassicalBluetoothData(
                            data = data,
                            text = dataString,
                            timestamp = System.currentTimeMillis(),
                            deviceAddress = connectedDevice?.address ?: "Unknown"
                        )
                        
                        _receivedData.emit(bluetoothData)
                        Log.d(TAG, "Received ${bytesRead} bytes: $dataString")
                    }
                }
            } catch (e: IOException) {
                Log.e(TAG, "Data listener error: ${e.message}", e)
                if (_connectionState.value == ClassicalBluetoothConnectionState.CONNECTED) {
                    _connectionState.value = ClassicalBluetoothConnectionState.ERROR
                    _connectionErrors.emit("Connection lost: ${e.message}")
                    
                    // Attempt auto-reconnect
                    if (isAutoReconnectEnabled) {
                        connectedDevice?.let { device ->
                            scheduleReconnect(device)
                        }
                    }
                }
            }
        }
    }

    /**
     * Schedule reconnection attempt
     */
    private fun scheduleReconnect(device: BluetoothDevice) {
        if (_reconnectAttempts.value >= MAX_RECONNECT_ATTEMPTS) {
            Log.w(TAG, "Max reconnect attempts reached")
            _connectionState.value = ClassicalBluetoothConnectionState.ERROR
            return
        }

        reconnectJob = serviceScope.launch {
            _reconnectAttempts.value++
            _connectionState.value = ClassicalBluetoothConnectionState.RECONNECTING
            
            Log.d(TAG, "Scheduling reconnect attempt ${_reconnectAttempts.value} in ${RECONNECT_DELAY_MS}ms")
            delay(RECONNECT_DELAY_MS)
            
            if (_connectionState.value == ClassicalBluetoothConnectionState.RECONNECTING) {
                internalConnect(device)
            }
        }
    }

    fun cancelReconnect() {
        reconnectJob?.cancel()
        _connectionState.value = ClassicalBluetoothConnectionState.DISCONNECTED
        _reconnectAttempts.value = 0
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        serviceScope.cancel()
        runBlocking {
            disconnect()
        }
    }
}

/**
 * Connection states for Classical Bluetooth
 */
enum class ClassicalBluetoothConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR,
    RECONNECTING
}

/**
 * Data received from Classical Bluetooth device
 */
data class ClassicalBluetoothData(
    val data: ByteArray,
    val text: String,
    val timestamp: Long,
    val deviceAddress: String
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as ClassicalBluetoothData
        return data.contentEquals(other.data) && 
               text == other.text && 
               timestamp == other.timestamp && 
               deviceAddress == other.deviceAddress
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + text.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + deviceAddress.hashCode()
        return result
    }
}

/**
 * Connection statistics for Classical Bluetooth
 */
data class ClassicalBluetoothStats(
    val isConnected: Boolean,
    val connectedDeviceName: String,
    val connectedDeviceAddress: String,
    val connectionUptime: Long,
    val bytesSent: Long,
    val bytesReceived: Long,
    val messagesSent: Long,
    val messagesReceived: Long
)
