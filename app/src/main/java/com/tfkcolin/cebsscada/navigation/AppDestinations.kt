package com.tfkcolin.cebsscada.navigation

import kotlinx.serialization.Serializable

/**
 * Navigation destinations for the CEBS SCADA application.
 * Using type-safe navigation with Kotlin Serialization.
 */

/**
 * Main app destinations following the workspace-centric workflow
 */
@Serializable
sealed class AppDestination {
    
    /**
     * Workspace Home - Main dashboard for workspace management
     * Default landing screen showing saved workspaces
     */
    @Serializable
    data object WorkspaceHome : AppDestination()
    
    /**
     * Workspace Design - Visual workspace editor
     * @param workspaceId Optional workspace ID for editing existing workspace, null for new workspace
     */
    @Serializable
    data class WorkspaceDesign(
        val workspaceId: Long? = null
    ) : AppDestination()
    
    /**
     * Workspace Runtime - Live execution environment
     * @param workspaceId ID of the workspace to run
     */
    @Serializable
    data class WorkspaceRuntime(
        val workspaceId: Long
    ) : AppDestination()
    
    /**
     * Settings - Protocol testing and configuration access point
     */
    @Serializable
    data object Settings : AppDestination()
}

/**
 * Settings section destinations for protocol testing
 */
@Serializable
sealed class SettingsDestination {
    
    /**
     * Main settings screen with protocol testing sections
     */
    @Serializable
    data object Main : SettingsDestination()
    
    /**
     * Classic Bluetooth testing section
     */
    @Serializable
    data object ClassicBluetooth : SettingsDestination()
    
    /**
     * Bluetooth Low Energy testing section
     */
    @Serializable
    data object BluetoothLE : SettingsDestination()
    
    /**
     * MQTT testing section
     */
    @Serializable
    data object MQTT : SettingsDestination()
    
    /**
     * Communication Hub testing section
     */
    @Serializable
    data object CommunicationHub : SettingsDestination()
}

/**
 * Navigation routes as string constants for deep linking
 */
object AppRoutes {
    const val WORKSPACE_HOME = "workspace_home"
    const val WORKSPACE_DESIGN = "workspace_design"
    const val WORKSPACE_RUNTIME = "workspace_runtime"
    const val SETTINGS = "settings"
    
    // Settings sub-routes
    const val SETTINGS_MAIN = "settings_main"
    const val SETTINGS_CLASSIC_BLUETOOTH = "settings_classic_bluetooth"
    const val SETTINGS_BLE = "settings_ble"
    const val SETTINGS_MQTT = "settings_mqtt"
    const val SETTINGS_COMMUNICATION_HUB = "settings_communication_hub"
    
    // Route patterns with parameters
    const val WORKSPACE_DESIGN_PATTERN = "workspace_design?workspaceId={workspaceId}"
    const val WORKSPACE_RUNTIME_PATTERN = "workspace_runtime/{workspaceId}"
}

/**
 * Navigation arguments keys
 */
object NavArgs {
    const val WORKSPACE_ID = "workspaceId"
}

/**
 * Deep link patterns for external navigation
 */
object DeepLinks {
    const val BASE_URI = "cebsscada://app"
    
    // Workspace deep links
    const val WORKSPACE_HOME = "$BASE_URI/workspace"
    const val WORKSPACE_DESIGN = "$BASE_URI/workspace/design"
    const val WORKSPACE_DESIGN_WITH_ID = "$BASE_URI/workspace/design/{workspaceId}"
    const val WORKSPACE_RUNTIME = "$BASE_URI/workspace/runtime/{workspaceId}"
    
    // Settings deep links
    const val SETTINGS = "$BASE_URI/settings"
    const val SETTINGS_BLUETOOTH = "$BASE_URI/settings/bluetooth"
    const val SETTINGS_BLE = "$BASE_URI/settings/ble"
    const val SETTINGS_MQTT = "$BASE_URI/settings/mqtt"
    const val SETTINGS_COMMUNICATION = "$BASE_URI/settings/communication"
}
