package com.tfkcolin.cebsscada.scada.persistence

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * DAO for workspace operations
 */
@Dao
interface WorkspaceDao {

    @Query("SELECT * FROM workspaces ORDER BY modifiedAt DESC")
    fun getAllWorkspaces(): Flow<List<WorkspaceEntity>>

    @Query("SELECT * FROM workspaces WHERE id = :id")
    suspend fun getWorkspaceById(id: Long): WorkspaceEntity?

    @Query("SELECT * FROM workspaces WHERE name LIKE '%' || :query || '%' ORDER BY modifiedAt DESC")
    fun searchWorkspaces(query: String): Flow<List<WorkspaceEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWorkspace(workspace: WorkspaceEntity): Long

    @Update
    suspend fun updateWorkspace(workspace: WorkspaceEntity)

    @Delete
    suspend fun deleteWorkspace(workspace: WorkspaceEntity)

    @Query("DELETE FROM workspaces WHERE id = :id")
    suspend fun deleteWorkspaceById(id: Long)

    @Query("SELECT COUNT(*) FROM workspace_components WHERE workspaceId = :workspaceId")
    suspend fun getComponentCountForWorkspace(workspaceId: Long): Int
}

/**
 * DAO for component operations
 */
@Dao
interface ComponentDao {

    @Query("SELECT * FROM workspace_components WHERE workspaceId = :workspaceId")
    suspend fun getComponentsForWorkspace(workspaceId: Long): List<ComponentEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertComponent(component: ComponentEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertComponents(components: List<ComponentEntity>)

    @Update
    suspend fun updateComponent(component: ComponentEntity)

    @Delete
    suspend fun deleteComponent(component: ComponentEntity)

    @Query("DELETE FROM workspace_components WHERE workspaceId = :workspaceId AND componentId = :componentId")
    suspend fun deleteComponentById(workspaceId: Long, componentId: String)

    @Query("DELETE FROM workspace_components WHERE workspaceId = :workspaceId")
    suspend fun deleteComponentsForWorkspace(workspaceId: Long)
}