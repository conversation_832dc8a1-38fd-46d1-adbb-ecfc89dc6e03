package com.tfkcolin.cebsscada.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.navigation.NavDestination
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController

/**
 * Navigation state holder for the CEBS SCADA application.
 * Provides centralized navigation logic and state management.
 */
@Stable
class NavigationState(
    val navController: NavHostController
) {
    
    /**
     * Current destination in the navigation graph
     */
    val currentDestination: NavDestination?
        @Composable get() = navController
            .currentBackStackEntryAsState().value?.destination
    
    /**
     * Current screen type based on the destination
     */
    val currentScreenType: ScreenType
        @Composable get() = when (currentDestination?.route) {
            AppDestination.WorkspaceHome::class.qualifiedName -> ScreenType.WORKSPACE_HOME
            AppDestination.WorkspaceDesign::class.qualifiedName -> ScreenType.WORKSPACE_DESIGN
            AppDestination.WorkspaceRuntime::class.qualifiedName -> ScreenType.WORKSPACE_RUNTIME
            AppDestination.Settings::class.qualifiedName -> ScreenType.SETTINGS
            else -> ScreenType.WORKSPACE_HOME
        }
    
    /**
     * Whether the current screen can navigate back
     */
    val canNavigateBack: Boolean
        @Composable get() = navController.previousBackStackEntry != null && 
                           currentScreenType != ScreenType.WORKSPACE_HOME
    
    /**
     * Navigate to workspace design screen
     */
    fun navigateToWorkspaceDesign(workspaceId: Long? = null) {
        navController.navigate(AppDestination.WorkspaceDesign(workspaceId))
    }
    
    /**
     * Navigate to workspace runtime screen
     */
    fun navigateToWorkspaceRuntime(workspaceId: Long) {
        navController.navigate(AppDestination.WorkspaceRuntime(workspaceId))
    }
    
    /**
     * Navigate to settings screen
     */
    fun navigateToSettings() {
        navController.navigate(AppDestination.Settings)
    }
    
    /**
     * Navigate back to workspace home with proper back stack management
     */
    fun navigateToHome() {
        navController.navigate(AppDestination.WorkspaceHome) {
            // Clear back stack to home
            popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
            }
            // Avoid multiple copies of the same destination
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }
    }
    
    /**
     * Navigate up in the navigation hierarchy
     */
    fun navigateUp(): Boolean {
        return navController.navigateUp()
    }
    
    /**
     * Handle system back press with proper navigation logic
     */
    @Composable
    fun onBackPressed(): Boolean {
        return when (currentScreenType) {
            ScreenType.WORKSPACE_HOME -> false // Let system handle
            else -> {
                navigateUp()
                true
            }
        }
    }
    
    /**
     * Clear navigation back stack to a specific destination
     */
    fun clearBackStackTo(destination: AppDestination) {
        navController.navigate(destination) {
            popUpTo(navController.graph.findStartDestination().id) {
                inclusive = false
            }
        }
    }
    
    /**
     * Navigate with single top behavior (avoid duplicates)
     */
    fun navigateSingleTop(destination: AppDestination) {
        navController.navigate(destination) {
            launchSingleTop = true
        }
    }
}

/**
 * Screen types for UI state management
 */
enum class ScreenType {
    WORKSPACE_HOME,    // SCADA workspace dashboard
    WORKSPACE_DESIGN,  // Workspace editor/designer
    WORKSPACE_RUNTIME, // Read-only runtime execution
    SETTINGS          // Protocol testing and settings
}

/**
 * Remember navigation state across recompositions
 */
@Composable
fun rememberNavigationState(
    navController: NavHostController = rememberNavController()
): NavigationState {
    return remember(navController) {
        NavigationState(navController)
    }
}

/**
 * Navigation state extensions for common patterns
 */
object NavigationStateExtensions {
    
    /**
     * Get the title for the current screen
     */
    @Composable
    fun NavigationState.getScreenTitle(): String {
        return when (currentScreenType) {
            ScreenType.WORKSPACE_HOME, ScreenType.WORKSPACE_DESIGN, ScreenType.WORKSPACE_RUNTIME ->
                "CEBS SCADA - Industrial Control"
            ScreenType.SETTINGS -> "Settings"
        }
    }
    
    /**
     * Check if settings button should be shown
     */
    @Composable
    fun NavigationState.shouldShowSettingsButton(): Boolean {
        return currentScreenType in listOf(
            ScreenType.WORKSPACE_HOME,
            ScreenType.WORKSPACE_DESIGN,
            ScreenType.WORKSPACE_RUNTIME
        )
    }

    /**
     * Check if the current screen is a workspace screen
     */
    @Composable
    fun NavigationState.isWorkspaceScreen(): Boolean {
        return currentScreenType in listOf(
            ScreenType.WORKSPACE_HOME,
            ScreenType.WORKSPACE_DESIGN,
            ScreenType.WORKSPACE_RUNTIME
        )
    }
}
