### General Component Behaviors (Applicable to all components)

*   **Draggable:** All components can be dragged and dropped onto the workspace canvas.
*   **Resizable:** All components have handles to be resized.
*   **Configurable:** A long-press or double-tap on any component opens its specific configuration panel.
*   **Deletable:** Each component can be removed from the workspace.
*   **Unique ID:** Each component added to the workspace is assigned a unique ID for saving and state management.

---

### UI Component Specification List

#### Category 1: Input & Control Components (Primarily for Writing Data)

These components are used to send commands or data to a microcontroller or system.

**1. Push Button**
*   **Icon Suggestion:** A circle or square with a "press" symbol.
*   **Description:** A standard button that sends a predefined command when pressed.
*   **Primary Function:** Write
*   **Use Cases:** Triggering an action (e.g., start a motor, open a valve, run a sequence), sending a reset signal.
*   **Configurable Properties:**
    *   **General:** Component Label (e.g., "Start Pump").
    *   **Appearance:** Button Color, Text Color, Text Size, Button Shape.
    *   **Behavior (Data Binding):**
        *   **Target:** The destination address (e.g., MQTT Topic, BLE Characteristic UUID).
        *   **Payload:** The data to send when pressed (e.g., "START", `1`, `{"command":"run"}`).
        *   **Data Format:** String, Integer, Float, JSON.

**2. Toggle Switch / On-Off Switch**
*   **Icon Suggestion:** A classic sliding switch.
*   **Description:** A two-state switch for turning a function on or off.
*   **Primary Function:** Write (Can also be Read/Write to reflect device state).
*   **Use Cases:** Turning lights on/off, enabling/disabling a feature, opening/closing a relay.
*   **Configurable Properties:**
    *   **General:** Component Label.
    *   **Appearance:** "On" Color, "Off" Color, Switch Style.
    *   **Behavior (Data Binding):**
        *   **Target:** Destination address.
        *   **On Payload:** Data to send when switched to "On" (e.g., `true`, `1`, `{"state":"ON"}`).
        *   **Off Payload:** Data to send when switched to "Off" (e.g., `false`, `0`, `{"state":"OFF"}`).
        *   **Data Format:** Boolean, String, Integer, JSON.
        *   **(Optional Read) State Source:** A source address to listen to, which will automatically update the switch's visual state if the device is controlled elsewhere.

**3. Slider**
*   **Icon Suggestion:** A horizontal or vertical bar with a handle.
*   **Description:** Allows the user to select a value from a continuous or discrete range.
*   **Primary Function:** Write
*   **Use Cases:** Dimming a light (0-100%), setting motor speed, adjusting a setpoint temperature.
*   **Configurable Properties:**
    *   **General:** Component Label.
    *   **Appearance:** Bar Color, Handle Color, Orientation (Horizontal/Vertical).
    *   **Behavior (Data Binding):**
        *   **Target:** Destination address.
        *   **Minimum Value:** The value at the start of the slider (e.g., 0).
        *   **Maximum Value:** The value at the end of the slider (e.g., 255).
        *   **Step:** The increment value (e.g., `1` for integers, `0.1` for floats).
        *   **Send on Release:** A boolean to decide whether to send data continuously while sliding or only when the user lets go.

**4. Numeric Input**
*   **Icon Suggestion:** A box with "123" inside.
*   **Description:** A text field for entering a specific numerical value, with a "Send" button.
*   **Primary Function:** Write
*   **Use Cases:** Entering a precise setpoint, defining a timer duration, inputting a specific coordinate.
*   **Configurable Properties:**
    *   **General:** Component Label.
    *   **Appearance:** Text Size, Border Color.
    *   **Behavior (Data Binding):**
        *   **Target:** Destination address.
        *   **Data Format:** Integer, Float.
        *   **Min/Max Value (for validation):** Optional limits for the input.

#### Category 2: Display & Output Components (Primarily for Reading Data)

These components visualize data received from sensors or systems.

**5. Value Display / Text Label**
*   **Icon Suggestion:** A box with "Abc" or "T" inside.
*   **Description:** Displays raw text or formatted numerical data.
*   **Primary Function:** Read
*   **Use Cases:** Showing temperature, humidity, pressure, device status ("Online", "Error"), sensor readings.
*   **Configurable Properties:**
    *   **General:** Default Text (shown when no data is received).
    *   **Appearance:** Text Color, Text Size, Background Color, Font Style.
    *   **Behavior (Data Binding):**
        *   **Source:** The address to listen to for data (e.g., MQTT Topic, BLE Characteristic UUID).
        *   **Prefix/Suffix:** Text to add before or after the value (e.g., Prefix: "Temp: ", Suffix: " °C").
        *   **Data Parsing (for JSON):** Path to the value within a JSON object (e.g., `data.temperature`).
        *   **Decimal Places:** Number of decimal places to display for floats.

**6. Indicator Light (LED)**
*   **Icon Suggestion:** A simple, solid circle.
*   **Description:** A virtual LED that changes color based on the received value.
*   **Primary Function:** Read
*   **Use Cases:** Showing power status (green/red), indicating an alarm (flashing red), showing system state (green=running, yellow=standby).
*   **Configurable Properties:**
    *   **General:** Component Label.
    *   **Appearance:** Size, Shape (Circle/Square).
    *   **Behavior (Data Binding):**
        *   **Source:** Address to listen to.
        *   **Color Mapping:** A user-defined list of rules. For example:
            *   `if value == "true"` -> `Color: Green`
            *   `if value == "false"` -> `Color: Red`
            *   `if value > 90` -> `Color: Orange, Flashing: true`
            *   `Default Color:` Gray

**7. Gauge**
*   **Icon Suggestion:** A semi-circular gauge dial.
*   **Description:** Displays a numerical value on a dial, providing an intuitive sense of its position within a range.
*   **Primary Function:** Read
*   **Use Cases:** Displaying pressure, speed, tank level, CPU usage.
*   **Configurable Properties:**
    *   **General:** Component Label, Units (e.g., "PSI", "%").
    *   **Appearance:** Gauge Style (full circle, semi-circle), Dial Colors.
    *   **Behavior (Data Binding):**
        *   **Source:** Address to listen to.
        *   **Minimum Value:** The value at the start of the gauge (e.g., 0).
        *   **Maximum Value:** The value at the end of the gauge (e.g., 100).
        *   **Color Ranges (Optional):** Define colors for different value ranges (e.g., 0-50 = Green, 51-80 = Yellow, 81-100 = Red).

#### Category 3: Visualization & Layout Components

These components are for displaying data trends or organizing the workspace.

**8. Line Chart / Graph**
*   **Icon Suggestion:** A simple line graph icon.
*   **Description:** Plots incoming numerical data over time.
*   **Primary Function:** Read
*   **Use Cases:** Graphing sensor data history, monitoring process variables, visualizing trends.
*   **Configurable Properties:**
    *   **General:** Chart Title.
    *   **Appearance:** Line Color, Background Color, Grid Lines (On/Off).
    *   **Behavior (Data Binding):**
        *   **Data Series:** Ability to add one or more data series to the chart. For each series:
            *   **Source:** Address to listen to.
            *   **Name:** Legend name for the series.
            *   **Line Color:** Color for this specific data line.
        *   **Time Window:** The amount of historical data to display (e.g., Last 60 seconds, 5 minutes).
        *   **Y-Axis Min/Max:** Auto-scaling or fixed range for the vertical axis.

**9. Group Box / Container**
*   **Icon Suggestion:** A dashed-line rectangle.
*   **Description:** A visual container to group other components together for better organization.
*   **Primary Function:** Layout
*   **Use Cases:** Grouping all "Living Room" controls, separating "Motor Control" from "Sensor Readings."
*   **Configurable Properties:**
    *   **General:** Group Title.
    *   **Appearance:** Border Style (solid, dashed), Border Color, Background Color.

By implementing this set of components, you will provide users with a powerful and flexible toolkit to build a wide variety of custom SCADA interfaces tailored to their specific needs.