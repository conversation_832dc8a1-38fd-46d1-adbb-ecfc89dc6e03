package com.tfkcolin.cebsscada.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.unit.dp
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.cebsscada.ui.components.*
import com.tfkcolin.cebsscada.ui.theme.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ResponsiveDesignTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun testResponsiveLayoutSmallPhone() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                TestResponsiveLayout(screenWidthDp = 320, screenHeightDp = 480) // Small phone
            }
        }

        // Verify small phone behavior
        composeTestRule.onNodeWithTag("screen_size").assertTextContains("COMPACT")
        composeTestRule.onNodeWithTag("device_type").assertTextContains("PHONE")
        composeTestRule.onNodeWithTag("should_use_single_pane").assertTextContains("true")
        composeTestRule.onNodeWithTag("should_use_bottom_nav").assertTextContains("true")
    }

    @Test
    fun testResponsiveLayoutNormalPhone() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                TestResponsiveLayout(screenWidthDp = 393, screenHeightDp = 851) // Normal phone portrait
            }
        }

        // Verify normal phone behavior
        composeTestRule.onNodeWithTag("screen_size").assertTextContains("COMPACT")
        composeTestRule.onNodeWithTag("device_type").assertTextContains("PHONE")
        composeTestRule.onNodeWithTag("should_use_single_pane").assertTextContains("true")
        composeTestRule.onNodeWithTag("should_use_bottom_nav").assertTextContains("true")
    }

    @Test
    fun testResponsiveLayoutPhoneLandscape() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                TestResponsiveLayout(screenWidthDp = 851, screenHeightDp = 393) // Phone landscape
            }
        }

        // Verify phone landscape still treated as phone
        composeTestRule.onNodeWithTag("screen_size").assertTextContains("COMPACT")
        composeTestRule.onNodeWithTag("device_type").assertTextContains("PHONE")
        composeTestRule.onNodeWithTag("orientation").assertTextContains("LANDSCAPE")
        composeTestRule.onNodeWithTag("should_use_single_pane").assertTextContains("true")
    }

    @Test
    fun testResponsiveLayoutTablet() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                TestResponsiveLayout(screenWidthDp = 800, screenHeightDp = 1200) // Tablet
            }
        }

        // Verify tablet behavior
        composeTestRule.onNodeWithTag("screen_size").assertTextContains("MEDIUM")
        composeTestRule.onNodeWithTag("device_type").assertTextContains("TABLET")
        composeTestRule.onNodeWithTag("should_use_two_pane").assertTextContains("true")
    }

    @Test
    fun testResponsiveLayoutDesktop() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                TestResponsiveLayout(screenWidthDp = 1200, screenHeightDp = 800) // Desktop
            }
        }

        // Verify desktop behavior
        composeTestRule.onNodeWithTag("screen_size").assertTextContains("EXPANDED")
        composeTestRule.onNodeWithTag("device_type").assertTextContains("DESKTOP")
        composeTestRule.onNodeWithTag("should_use_three_pane").assertTextContains("true")
    }

    @Test
    fun testAdaptiveGridResponsiveness() {
        val testItems = listOf("Item 1", "Item 2", "Item 3", "Item 4", "Item 5")
        
        composeTestRule.setContent {
            CEBSSCADATheme {
                AdaptiveGrid(
                    items = testItems,
                    modifier = Modifier.testTag("adaptive_grid")
                ) { item ->
                    Text(
                        text = item,
                        modifier = Modifier.testTag("grid_item_$item")
                    )
                }
            }
        }

        // Verify all items are displayed
        testItems.forEach { item ->
            composeTestRule.onNodeWithTag("grid_item_$item").assertIsDisplayed()
        }
    }

    @Test
    fun testAdaptiveCardSizing() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                AdaptiveCard(
                    modifier = Modifier.testTag("adaptive_card")
                ) {
                    Text(
                        text = "Test Card Content",
                        modifier = Modifier.testTag("card_content")
                    )
                }
            }
        }

        // Verify card is displayed with content
        composeTestRule.onNodeWithTag("adaptive_card").assertIsDisplayed()
        composeTestRule.onNodeWithTag("card_content").assertIsDisplayed()
    }

    @Test
    fun testAdaptiveButtonSizing() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                AdaptiveButton(
                    onClick = { },
                    modifier = Modifier.testTag("adaptive_button")
                ) {
                    Text("Test Button")
                }
            }
        }

        // Verify button is displayed and has proper touch target
        composeTestRule.onNodeWithTag("adaptive_button")
            .assertIsDisplayed()
            .assertHasClickAction()
    }

    @Test
    fun testAdaptiveIconButtonTouchTarget() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                AdaptiveIconButton(
                    onClick = { },
                    modifier = Modifier.testTag("adaptive_icon_button")
                ) {
                    Text("Icon")
                }
            }
        }

        // Verify icon button meets minimum touch target requirements
        composeTestRule.onNodeWithTag("adaptive_icon_button")
            .assertIsDisplayed()
            .assertHasClickAction()
            .assertWidthIsAtLeast(48.dp)
            .assertHeightIsAtLeast(48.dp)
    }

    @Test
    fun testResponsiveTypographyScaling() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                val layout = rememberResponsiveLayout()
                val typography = getResponsiveTypography(layout.screenSize)
                
                Text(
                    text = "Display Large Text",
                    style = typography.displayLarge,
                    modifier = Modifier.testTag("display_large_text")
                )
                
                Text(
                    text = "Body Text",
                    style = typography.bodyLarge,
                    modifier = Modifier.testTag("body_text")
                )
            }
        }

        // Verify typography elements are displayed
        composeTestRule.onNodeWithTag("display_large_text").assertIsDisplayed()
        composeTestRule.onNodeWithTag("body_text").assertIsDisplayed()
    }

    @Test
    fun testAdaptiveThreePaneLayout() {
        composeTestRule.setContent {
            CEBSSCADATheme {
                AdaptiveThreePane(
                    leftContent = {
                        Text(
                            text = "Left Panel",
                            modifier = Modifier.testTag("left_panel")
                        )
                    },
                    centerContent = {
                        Text(
                            text = "Center Panel",
                            modifier = Modifier.testTag("center_panel")
                        )
                    },
                    rightContent = {
                        Text(
                            text = "Right Panel",
                            modifier = Modifier.testTag("right_panel")
                        )
                    },
                    modifier = Modifier.testTag("three_pane_layout")
                )
            }
        }

        // Center panel should always be visible
        composeTestRule.onNodeWithTag("center_panel").assertIsDisplayed()
        
        // Left and right panels visibility depends on screen size
        // This test will run with default screen size, so behavior may vary
        composeTestRule.onNodeWithTag("three_pane_layout").assertIsDisplayed()
    }

    @Composable
    private fun TestResponsiveLayout(screenWidthDp: Int, screenHeightDp: Int = 800) {
        // Mock configuration for testing - this would need to be updated to properly mock
        // the LocalConfiguration for testing different screen sizes
        val layout = rememberResponsiveLayout()

        Box(modifier = Modifier.fillMaxSize()) {
            Text(
                text = "Screen Size: ${layout.screenSize}",
                modifier = Modifier.testTag("screen_size")
            )
            Text(
                text = "Device Type: ${layout.deviceType}",
                modifier = Modifier.testTag("device_type")
            )
            Text(
                text = "Orientation: ${layout.orientation}",
                modifier = Modifier.testTag("orientation")
            )
            Text(
                text = "Single Pane: ${layout.shouldUseSinglePane}",
                modifier = Modifier.testTag("should_use_single_pane")
            )
            Text(
                text = "Two Pane: ${layout.shouldUseTwoPane}",
                modifier = Modifier.testTag("should_use_two_pane")
            )
            Text(
                text = "Three Pane: ${layout.shouldUseThreePane}",
                modifier = Modifier.testTag("should_use_three_pane")
            )
            Text(
                text = "Bottom Nav: ${layout.shouldUseBottomNavigation}",
                modifier = Modifier.testTag("should_use_bottom_nav")
            )
            Text(
                text = "Nav Rail: ${layout.shouldUseNavigationRail}",
                modifier = Modifier.testTag("should_use_nav_rail")
            )
            Text(
                text = "Nav Drawer: ${layout.shouldUseNavigationDrawer}",
                modifier = Modifier.testTag("should_use_nav_drawer")
            )
        }
    }
}
