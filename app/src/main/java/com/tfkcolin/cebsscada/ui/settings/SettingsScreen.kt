package com.tfkcolin.cebsscada.ui.settings

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.ui.components.ScreenConfig
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager

/**
 * Main Settings screen with protocol testing sections
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    permissionManager: BluetoothPermissionManager,
    onBackToHome: () -> Unit,
    onScreenConfigChange: (ScreenConfig) -> Unit
) {
    var selectedSection by remember { mutableStateOf<SettingsSection?>(null) }

    // Configure the central scaffold based on current section
    LaunchedEffect(selectedSection) {
        onScreenConfigChange(
            ScreenConfig(
                title = when(selectedSection) {
                    null -> "Settings"
                    SettingsSection.CLASSIC_BLUETOOTH -> "Classic Bluetooth Testing"
                    SettingsSection.BLE -> "BLE Testing"
                    SettingsSection.MQTT -> "MQTT Testing"
                    SettingsSection.COMMUNICATION -> "Communication Hub Testing"
                },
                showBackButton = selectedSection != null,
                showSettingsButton = selectedSection == null,
                navigationIcon = if (selectedSection != null) {
                    {
                        IconButton(onClick = { selectedSection = null }) {
                            Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                        }
                    }
                } else null
            )
        )
    }

    if (selectedSection == null) {
        // Main settings menu
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                Text(
                    text = "Protocol Testing",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            items(settingsSections) { sectionInfo ->
                SettingsSectionCard(
                    sectionInfo = sectionInfo,
                    onClick = { selectedSection = sectionInfo.section }
                )
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "About",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "CEBS SCADA",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Industrial Control & Monitoring System",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Version 1.0.0",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
    else {
        // Show selected section content
        when (selectedSection) {
            SettingsSection.CLASSIC_BLUETOOTH -> {
                ClassicBluetoothSettingsContent(
                    permissionManager = permissionManager
                )
            }
            SettingsSection.BLE -> {
                BleSettingsContent(
                    permissionManager = permissionManager
                )
            }
            SettingsSection.MQTT -> {
                MqttSettingsContent()
            }
            SettingsSection.COMMUNICATION -> {
                CommunicationSettingsContent()
            }
            null -> {
                // This should not happen, but handle gracefully
                selectedSection = null
            }
        }
    }
}

/**
 * Settings section enum
 */
enum class SettingsSection {
    CLASSIC_BLUETOOTH,
    BLE,
    MQTT,
    COMMUNICATION
}

/**
 * Settings section data class
 */
data class SettingsSectionInfo(
    val section: SettingsSection,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val category: String = "Protocol Testing"
)

/**
 * Available settings sections
 */
val settingsSections = listOf(
    SettingsSectionInfo(
        section = SettingsSection.CLASSIC_BLUETOOTH,
        title = "Classic Bluetooth",
        description = "Test Classic Bluetooth communication",
        icon = Icons.Default.Bluetooth
    ),
    SettingsSectionInfo(
        section = SettingsSection.BLE,
        title = "Bluetooth Low Energy",
        description = "Test BLE device communication",
        icon = Icons.Default.BluetoothConnected
    ),
    SettingsSectionInfo(
        section = SettingsSection.MQTT,
        title = "MQTT",
        description = "Test MQTT broker communication",
        icon = Icons.Default.Router
    ),
    SettingsSectionInfo(
        section = SettingsSection.COMMUNICATION,
        title = "Communication Hub",
        description = "Unified communication testing interface",
        icon = Icons.Default.Hub
    )
)

/**
 * Settings section card
 */
@Composable
fun SettingsSectionCard(
    sectionInfo: SettingsSectionInfo,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = sectionInfo.icon,
                contentDescription = sectionInfo.title,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = sectionInfo.title,
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = sectionInfo.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                Icons.Default.ChevronRight,
                contentDescription = "Open",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}