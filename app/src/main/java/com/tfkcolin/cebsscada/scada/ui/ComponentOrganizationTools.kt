package com.tfkcolin.cebsscada.scada.ui

import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentSize
import com.tfkcolin.cebsscada.scada.ScadaComponent
import kotlin.math.max
import kotlin.math.min

/**
 * Component organization and layout utilities
 */
object ComponentOrganizationTools {

    /**
     * Alignment options for components
     */
    enum class Alignment {
        LEFT, CENTER, RIGHT, TOP, MIDDLE, BOTTOM
    }

    /**
     * Distribution options for components
     */
    enum class Distribution {
        HORIZONTAL, VERTICAL
    }

    /**
     * Align selected components
     */
    fun alignComponents(
        components: List<ScadaComponent>,
        alignment: Alignment,
        workspaceWidth: Float = 800f,
        workspaceHeight: Float = 600f
    ): List<Pair<ScadaComponent, ComponentPosition>> {
        if (components.isEmpty()) return emptyList()

        return when (alignment) {
            Alignment.LEFT -> alignLeft(components)
            Alignment.CENTER -> alignCenter(components, workspaceWidth)
            Alignment.RIGHT -> alignRight(components, workspaceWidth)
            Alignment.TOP -> alignTop(components)
            Alignment.MIDDLE -> alignMiddle(components, workspaceHeight)
            Alignment.BOTTOM -> alignBottom(components, workspaceHeight)
        }
    }

    /**
     * Distribute selected components
     */
    fun distributeComponents(
        components: List<ScadaComponent>,
        distribution: Distribution,
        workspaceWidth: Float = 800f,
        workspaceHeight: Float = 600f
    ): List<Pair<ScadaComponent, ComponentPosition>> {
        if (components.size < 3) return emptyList() // Need at least 3 components to distribute

        return when (distribution) {
            Distribution.HORIZONTAL -> distributeHorizontally(components, workspaceWidth)
            Distribution.VERTICAL -> distributeVertically(components, workspaceHeight)
        }
    }

    /**
     * Group components into a logical group
     */
    fun createComponentGroup(
        components: List<ScadaComponent>,
        groupName: String,
        groupColor: androidx.compose.ui.graphics.Color = androidx.compose.ui.graphics.Color.Gray
    ): com.tfkcolin.cebsscada.scada.ComponentGroup {
        val groupId = "group_${System.currentTimeMillis()}"
        return com.tfkcolin.cebsscada.scada.ComponentGroup(
            id = groupId,
            name = groupName,
            color = groupColor,
            description = "Group containing ${components.size} components"
        )
    }

    /**
     * Calculate bounding box for components
     */
    fun calculateBoundingBox(components: List<ScadaComponent>): ComponentBounds {
        if (components.isEmpty()) {
            return ComponentBounds(0f, 0f, 0f, 0f)
        }

        var minX = Float.MAX_VALUE
        var minY = Float.MAX_VALUE
        var maxX = Float.MIN_VALUE
        var maxY = Float.MIN_VALUE

        components.forEach { component ->
            minX = min(minX, component.position.x)
            minY = min(minY, component.position.y)
            maxX = max(maxX, component.position.x + component.size.width.value)
            maxY = max(maxY, component.position.y + component.size.height.value)
        }

        return ComponentBounds(minX, minY, maxX - minX, maxY - minY)
    }

    /**
     * Resize components to match sizes
     */
    fun matchSizes(
        components: List<ScadaComponent>,
        matchWidth: Boolean = true,
        matchHeight: Boolean = true
    ): List<Pair<ScadaComponent, ComponentSize>> {
        if (components.isEmpty()) return emptyList()

        // Find the maximum dimensions
        var maxWidth = 0f
        var maxHeight = 0f

        components.forEach { component ->
            if (matchWidth) maxWidth = max(maxWidth, component.size.width.value)
            if (matchHeight) maxHeight = max(maxHeight, component.size.height.value)
        }

        return components.map { component ->
            val newWidth = if (matchWidth) maxWidth else component.size.width.value
            val newHeight = if (matchHeight) maxHeight else component.size.height.value
            val newSize = ComponentSize(
                width = androidx.compose.ui.unit.Dp(newWidth),
                height = androidx.compose.ui.unit.Dp(newHeight)
            )
            component to newSize
        }
    }

    /**
     * Arrange components in a grid layout
     */
    fun arrangeInGrid(
        components: List<ScadaComponent>,
        columns: Int,
        spacing: Float = 20f,
        startX: Float = 50f,
        startY: Float = 50f
    ): List<Pair<ScadaComponent, ComponentPosition>> {
        val result = mutableListOf<Pair<ScadaComponent, ComponentPosition>>()

        components.forEachIndexed { index, component ->
            val row = index / columns
            val col = index % columns

            val x = startX + col * (component.size.width.value + spacing)
            val y = startY + row * (component.size.height.value + spacing)

            result.add(component to ComponentPosition(x, y))
        }

        return result
    }

    /**
     * Bring components to front (increase layer)
     */
    fun bringToFront(components: List<ScadaComponent>, maxLayer: Int): List<Pair<ScadaComponent, Int>> {
        val newLayer = maxLayer + 1
        return components.map { it to newLayer }
    }

    /**
     * Send components to back (decrease layer)
     */
    fun sendToBack(components: List<ScadaComponent>): List<Pair<ScadaComponent, Int>> {
        return components.map { it to 0 }
    }

    // Private helper functions

    private fun alignLeft(components: List<ScadaComponent>): List<Pair<ScadaComponent, ComponentPosition>> {
        val minX = components.minOf { it.position.x }
        return components.map { component ->
            component to component.position.copy(x = minX)
        }
    }

    private fun alignCenter(components: List<ScadaComponent>, workspaceWidth: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val centerX = workspaceWidth / 2
        return components.map { component ->
            val componentCenter = component.position.x + component.size.width.value / 2
            val offset = centerX - componentCenter
            component to component.position.copy(x = component.position.x + offset)
        }
    }

    private fun alignRight(components: List<ScadaComponent>, workspaceWidth: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val maxX = components.maxOf { it.position.x + it.size.width.value }
        return components.map { component ->
            val offset = workspaceWidth - maxX
            component to component.position.copy(x = component.position.x + offset)
        }
    }

    private fun alignTop(components: List<ScadaComponent>): List<Pair<ScadaComponent, ComponentPosition>> {
        val minY = components.minOf { it.position.y }
        return components.map { component ->
            component to component.position.copy(y = minY)
        }
    }

    private fun alignMiddle(components: List<ScadaComponent>, workspaceHeight: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val centerY = workspaceHeight / 2
        return components.map { component ->
            val componentCenter = component.position.y + component.size.height.value / 2
            val offset = centerY - componentCenter
            component to component.position.copy(y = component.position.y + offset)
        }
    }

    private fun alignBottom(components: List<ScadaComponent>, workspaceHeight: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val maxY = components.maxOf { it.position.y + it.size.height.value }
        return components.map { component ->
            val offset = workspaceHeight - maxY
            component to component.position.copy(y = component.position.y + offset)
        }
    }

    private fun distributeHorizontally(components: List<ScadaComponent>, workspaceWidth: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val sortedComponents = components.sortedBy { it.position.x }
        val firstComponent = sortedComponents.first()
        val lastComponent = sortedComponents.last()

        val totalWidth = lastComponent.position.x + lastComponent.size.width.value - firstComponent.position.x
        val spacing = (workspaceWidth - totalWidth) / (components.size - 1)

        val result = mutableListOf<Pair<ScadaComponent, ComponentPosition>>()
        var currentX = firstComponent.position.x

        sortedComponents.forEach { component ->
            result.add(component to component.position.copy(x = currentX))
            currentX += component.size.width.value + spacing
        }

        return result
    }

    private fun distributeVertically(components: List<ScadaComponent>, workspaceHeight: Float): List<Pair<ScadaComponent, ComponentPosition>> {
        val sortedComponents = components.sortedBy { it.position.y }
        val firstComponent = sortedComponents.first()
        val lastComponent = sortedComponents.last()

        val totalHeight = lastComponent.position.y + lastComponent.size.height.value - firstComponent.position.y
        val spacing = (workspaceHeight - totalHeight) / (components.size - 1)

        val result = mutableListOf<Pair<ScadaComponent, ComponentPosition>>()
        var currentY = firstComponent.position.y

        sortedComponents.forEach { component ->
            result.add(component to component.position.copy(y = currentY))
            currentY += component.size.height.value + spacing
        }

        return result
    }
}

/**
 * Component bounds for layout calculations
 */
data class ComponentBounds(
    val x: Float,
    val y: Float,
    val width: Float,
    val height: Float
)

/**
 * Undo/Redo system for component operations
 */
class ComponentHistoryManager {

    private val history = mutableListOf<ComponentSnapshot>()
    private var currentIndex = -1
    private val maxHistorySize = 50

    /**
     * Save current state for undo
     */
    fun saveState(components: List<ScadaComponent>) {
        val snapshot = ComponentSnapshot(
            components = components.map { component ->
                ComponentState(
                    id = component.id,
                    position = component.position,
                    size = component.size,
                    config = component.config,
                    groupId = component.groupId,
                    layer = component.layer,
                    isLocked = component.isLocked,
                    isVisible = component.isVisible,
                    tags = component.tags
                )
            }
        )

        // Remove any history after current index (when doing new action after undo)
        while (history.size > currentIndex + 1) {
            history.removeAt(history.size - 1)
        }

        history.add(snapshot)
        currentIndex++

        // Limit history size
        if (history.size > maxHistorySize) {
            history.removeAt(0)
            currentIndex--
        }
    }

    /**
     * Check if undo is available
     */
    fun canUndo(): Boolean = currentIndex > 0

    /**
     * Check if redo is available
     */
    fun canRedo(): Boolean = currentIndex < history.size - 1

    /**
     * Undo last operation
     */
    fun undo(): List<ComponentState>? {
        if (!canUndo()) return null
        currentIndex--
        return history[currentIndex].components
    }

    /**
     * Redo last undone operation
     */
    fun redo(): List<ComponentState>? {
        if (!canRedo()) return null
        currentIndex++
        return history[currentIndex].components
    }

    /**
     * Clear history
     */
    fun clear() {
        history.clear()
        currentIndex = -1
    }
}

/**
 * Snapshot of component states
 */
private data class ComponentSnapshot(
    val components: List<ComponentState>
)

/**
 * Individual component state
 */
data class ComponentState(
    val id: String,
    val position: ComponentPosition,
    val size: ComponentSize,
    val config: com.tfkcolin.cebsscada.scada.ComponentConfig,
    val groupId: String?,
    val layer: Int,
    val isLocked: Boolean,
    val isVisible: Boolean,
    val tags: Set<String>
)