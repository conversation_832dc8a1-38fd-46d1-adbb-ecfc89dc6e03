package com.tfkcolin.cebsscada.communication.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice as AndroidBluetoothDevice
import android.content.Context
import android.util.Log
import androidx.annotation.RequiresPermission
import com.tfkcolin.cebsscada.bluetooth.BLEScanner
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.DeviceType
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScanner
import com.tfkcolin.cebsscada.communication.models.BluetoothDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

/**
 * BLE device scanner implementing the new architecture
 */
class BLEDeviceScanner @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : DeviceScanner, BLEScanner.BLEScanCallback {

    companion object {
        private const val TAG = "BLEDeviceScanner"
    }

    // This scanner handles only BLE Bluetooth devices
    override val supportedProtocol: CommunicationProtocol = CommunicationProtocol.BLUETOOTH_BLE

    private val _discoveredDevices = MutableStateFlow<List<CommunicationDevice>>(emptyList())
    override val discoveredDevices: Flow<List<CommunicationDevice>> = _discoveredDevices.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    override val isScanning: Flow<Boolean> = _isScanning.asStateFlow()

    private val _scanningErrors = MutableSharedFlow<String>()
    override val scanningErrors: Flow<String> = _scanningErrors.asSharedFlow()

    private val _isScanningActive = AtomicBoolean(false)
    override val isScanningActive: Boolean
        get() = _isScanningActive.get()

    private val deviceMap = mutableMapOf<String, CommunicationDevice>()
    private var bleScanner: BLEScanner? = null
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    init {
        bleScanner = BLEScanner(context) { debugLog ->
            Log.d(TAG, debugLog)
        }.apply {
            bleScanCallback = this@BLEDeviceScanner
        }
    }

    override suspend fun startScan(options: Map<String, Any>) {
        if (_isScanningActive.get()) {
            return
        }

        if (bluetoothAdapter == null) {
            _scanningErrors.emit("Bluetooth adapter not available")
            return
        }

        if (!bluetoothAdapter.isEnabled) {
            _scanningErrors.emit("Bluetooth is not enabled")
            return
        }

        _isScanningActive.set(true)
        _isScanning.value = true

        // Start BLE scan
        try {
            // Check if service UUID filter is specified
            val serviceUuidString = options["serviceUuid"] as? String
            val serviceUuid = serviceUuidString?.let { UUID.fromString(it) }

            bleScanner?.startScan(serviceUuid)
            Log.d(TAG, "Started BLE device scanning")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start BLE scan", e)
            _scanningErrors.emit("Failed to start BLE scan: ${e.message}")
            _isScanning.value = false
            _isScanningActive.set(false)
        }
    }

    override suspend fun stopScan() {
        if (!_isScanningActive.get()) {
            return
        }

        // Stop BLE scan
        bleScanner?.stopScan()

        _isScanning.value = false
        _isScanningActive.set(false)

        Log.d(TAG, "Stopped BLE device scanning")
    }

    override suspend fun clearDevices() {
        deviceMap.clear()
        _discoveredDevices.value = emptyList()
    }

    override suspend fun getCurrentDevices(): List<CommunicationDevice> {
        return _discoveredDevices.value
    }

    override suspend fun cleanup() {
        stopScan()
        bleScanner?.bleScanCallback = null
        bleScanner = null
        coroutineScope.cancel()
    }

    // BLE Scan Callback implementation
    override fun onDeviceFound(device: AndroidBluetoothDevice, rssi: Int, name: String?) {
        try {
            // This scanner only handles BLE devices
            val deviceName = name ?: device.name ?: "Unknown BLE Device"
            val deviceAddress = device.address

            if (deviceMap.containsKey(deviceAddress)) {
                return // Device already discovered
            }

            val bluetoothDevice = BluetoothDevice(
                id = deviceAddress,
                name = deviceName,
                address = deviceAddress,
                deviceType = DeviceType.BLUETOOTH_BLE,
                rssi = rssi,
                isBonded = device.bondState == AndroidBluetoothDevice.BOND_BONDED,
                serviceUuids = emptyList() // BLE service UUIDs would be discovered after connection
            )

            deviceMap[deviceAddress] = bluetoothDevice
            _discoveredDevices.value = deviceMap.values.toList()

            Log.d(TAG, "Discovered BLE device: $deviceName ($deviceAddress) RSSI: $rssi")
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception while processing discovered BLE device", e)
            coroutineScope.launch {
                _scanningErrors.emit("Permission denied while processing BLE device")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing discovered BLE device", e)
            coroutineScope.launch {
                _scanningErrors.emit("Error processing BLE device: ${e.message}")
            }
        }
    }

    override fun onScanFinished() {
        Log.d(TAG, "BLE scan finished")
        _isScanning.value = false
        _isScanningActive.set(false)
    }

    override fun onScanFailed(errorCode: Int) {
        Log.e(TAG, "BLE scan failed with error code: $errorCode")
        _isScanning.value = false
        _isScanningActive.set(false)
        coroutineScope.launch {
            _scanningErrors.emit("BLE scan failed with error code: $errorCode")
        }
    }
}