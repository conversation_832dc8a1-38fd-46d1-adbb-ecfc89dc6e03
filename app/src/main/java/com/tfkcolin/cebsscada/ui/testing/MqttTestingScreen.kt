package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.models.MqttBroker

/**
 * MQTT Testing Screen for comprehensive MQTT communication testing
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MqttTestingScreen(
    viewModel: MqttTestingViewModel = hiltViewModel()
) {
    val selectedProtocol by viewModel.selectedProtocol.collectAsState()
    val connectionState by viewModel.currentConnectionState.collectAsState(initial = ConnectionState.DISCONNECTED)
    val connectedDevice by viewModel.currentConnectedDevice.collectAsState(initial = null)
    val messages by viewModel.messages.collectAsState()
    val subscriptions by viewModel.getSubscriptions().collectAsState(initial = emptyList())
    
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val tabs = listOf("Quick Test", "Broker Setup", "Message Testing", "Test Scenarios", "Diagnostics")
    
    
    LazyColumn (
        modifier = Modifier
            .fillMaxSize()
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
    ) {
        item {
            // Header
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(8.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = "MQTT Testing Center",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "Test and validate MQTT communication",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            )
                        }

                    }
                    // Connection status indicator
                    ConnectionStatusIndicator(
                        modifier = Modifier.padding(vertical = 4.dp),
                        connectionState = connectionState
                    )
                    connectedDevice?.let { device ->
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Connected to: ${device.name}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Tab row
            ScrollableTabRow (
                selectedTabIndex = selectedTabIndex,
                edgePadding = 4.dp
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = { Text(title) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Tab content
            when (selectedTabIndex) {
                0 -> QuickTestTab(
                    viewModel = viewModel,
                    connectionState = connectionState,
                    connectedDevice = connectedDevice
                )
                1 -> BrokerSetupTab(
                    viewModel = viewModel,
                    connectionState = connectionState
                )
                2 -> MessageTestingTab(
                    viewModel = viewModel,
                    connectionState = connectionState,
                    messages = messages.filter { it.protocol == CommunicationProtocol.MQTT },
                    subscriptions = subscriptions
                )
                3 -> TestScenariosTab(
                    viewModel = viewModel,
                    connectionState = connectionState
                )
                4 -> DiagnosticsTab(
                    viewModel = viewModel,
                    connectionState = connectionState,
                    connectedDevice = connectedDevice
                )
            }
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun ConnectionStatusIndicator(
    modifier: Modifier = Modifier, connectionState: ConnectionState
) {
    val (color, icon, text) = when (connectionState) {
        ConnectionState.CONNECTED -> Triple(Color.Green, Icons.Default.CheckCircle, "Connected")
        ConnectionState.CONNECTING -> Triple(Color.Yellow, Icons.Default.Sync, "Connecting")
        ConnectionState.DISCONNECTING -> Triple(Color(0xFFFF9800), Icons.Default.Sync, "Disconnecting")
        ConnectionState.ERROR -> Triple(Color.Red, Icons.Default.Error, "Error")
        ConnectionState.RECONNECTING -> Triple(Color.Blue, Icons.Default.Sync, "Reconnecting")
        ConnectionState.DISCONNECTED -> Triple(Color.Gray, Icons.Default.Cancel, "Disconnected")
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = color,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = color
        )
    }
}

@Composable
fun QuickTestTab(
    viewModel: MqttTestingViewModel,
    connectionState: ConnectionState,
    connectedDevice: com.tfkcolin.cebsscada.communication.models.CommunicationDevice?
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        QuickTestCard(
            title = "Public Broker Test",
            description = "Test with test.mosquitto.org",
            icon = Icons.Default.Public,
            enabled = connectionState == ConnectionState.DISCONNECTED,
            onClick = {
                val testBroker = MqttBroker(
                    id = "test.mosquitto.org:1883",
                    name = "Test Mosquitto Broker",
                    address = "test.mosquitto.org",
                    port = 1883
                )
                viewModel.addManualDevice(testBroker)
                viewModel.connect(testBroker)
            }
        )

        QuickTestCard(
            title = "Local Broker Test",
            description = "Test with localhost:1883",
            icon = Icons.Default.Computer,
            enabled = connectionState == ConnectionState.DISCONNECTED,
            onClick = {
                val localBroker = MqttBroker(
                    id = "localhost:1883",
                    name = "Local MQTT Broker",
                    address = "localhost",
                    port = 1883
                )
                viewModel.addManualDevice(localBroker)
                viewModel.connect(localBroker)
            }
        )

        QuickTestCard(
            title = "Eclipse IoT Test",
            description = "Test with mqtt.eclipseprojects.io",
            icon = Icons.Default.Cloud,
            enabled = connectionState == ConnectionState.DISCONNECTED,
            onClick = {
                val eclipseBroker = MqttBroker(
                    id = "mqtt.eclipseprojects.io:1883",
                    name = "Eclipse IoT Broker",
                    address = "mqtt.eclipseprojects.io",
                    port = 1883
                )
                viewModel.addManualDevice(eclipseBroker)
                viewModel.connect(eclipseBroker)
            }
        )
        
        if (connectionState == ConnectionState.CONNECTED && connectedDevice != null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Quick Actions",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                viewModel.subscribe("test/topic")
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Subscribe Test")
                        }

                        Button(
                            onClick = {
                                viewModel.sendText("Hello MQTT!", "test/topic")
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Send Test")
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick = { viewModel.disconnect() },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("Disconnect")
                    }
                }
            }
        }
    }
}

@Composable
fun QuickTestCard(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    enabled: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = if (enabled) onClick else { {} },
        enabled = enabled
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(40.dp),
                tint = if (enabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                contentDescription = "Connect",
                tint = if (enabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            )
        }
    }
}

@Composable
fun BrokerSetupTab(
    viewModel: MqttTestingViewModel,
    connectionState: ConnectionState
) {
    var showConfigDialog by remember { mutableStateOf(false) }
    val currentDevices by viewModel.currentDevices.collectAsState(initial = emptyList())
    val mqttBrokers = currentDevices.filterIsInstance<MqttBroker>()

    Column {
        // Add broker button
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "MQTT Broker Configuration",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Add and manage MQTT broker connections for testing",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(12.dp))

                Button(
                    onClick = { showConfigDialog = true },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Add MQTT Broker")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Broker list
        if (mqttBrokers.isNotEmpty()) {
            Text(
                text = "Configured Brokers",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                mqttBrokers.forEach { broker ->
                    BrokerCard(
                        broker = broker,
                        connectionState = connectionState,
                        onConnect = { viewModel.connect(broker) },
                        onRemove = { viewModel.removeManualDevice(broker) }
                    )
                }
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.Router,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "No brokers configured",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                    )

                    Text(
                        text = "Add a broker to start testing",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }

    // Configuration dialog
    if (showConfigDialog) {
        com.tfkcolin.cebsscada.communication.ui.DeviceConfigurationDialog(
            protocol = CommunicationProtocol.MQTT,
            onDismiss = { showConfigDialog = false },
            onDeviceConfigured = { device ->
                viewModel.addManualDevice(device)
                showConfigDialog = false
            }
        )
    }
}

@Composable
fun BrokerCard(
    broker: MqttBroker,
    connectionState: ConnectionState,
    onConnect: () -> Unit,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = broker.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "${broker.address}:${broker.port}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    if (broker.username != null) {
                        Text(
                            text = "Username: ${broker.username}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                    if (broker.useTls) {
                        Text(
                            text = "TLS Enabled",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Button(
                        onClick = onConnect,
                        enabled = connectionState == ConnectionState.DISCONNECTED
                    ) {
                        Text("Connect")
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    TextButton(
                        onClick = onRemove,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("Remove")
                    }
                }
            }
        }
    }
}
