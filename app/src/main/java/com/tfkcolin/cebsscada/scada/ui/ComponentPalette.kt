package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentType

/**
 * Component palette showing available SCADA components to add to workspace
 */
@Composable
fun ComponentPalette(
    onComponentSelected: (ComponentType, ComponentPosition) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .widthIn(min = 200.dp, max = 250.dp)
            .fillMaxHeight(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "Components",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(availableComponents) { componentInfo ->
                    ComponentPaletteItem(
                        componentInfo = componentInfo,
                        onComponentSelected = { type ->
                            // Position new components at center-ish of workspace
                            val position = ComponentPosition(200f, 200f)
                            onComponentSelected(type, position)
                        }
                    )
                }
            }
        }
    }
}

/**
 * Individual component item in the palette
 */
@Composable
fun ComponentPaletteItem(
    componentInfo: ComponentInfo,
    onComponentSelected: (ComponentType) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = componentInfo.icon,
                contentDescription = componentInfo.name,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = componentInfo.name,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1
                )
                Text(
                    text = componentInfo.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray,
                    maxLines = 2
                )
            }

            IconButton(
                onClick = { onComponentSelected(componentInfo.type) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add ${componentInfo.name}",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Component information for palette display
 */
data class ComponentInfo(
    val type: ComponentType,
    val name: String,
    val description: String,
    val icon: ImageVector,
    val category: String
)

/**
 * List of available components
 */
val availableComponents = listOf(
    // Input & Control Components
    ComponentInfo(
        type = ComponentType.PUSH_BUTTON,
        name = "Push Button",
        description = "Send commands",
        icon = Icons.Default.TouchApp,
        category = "Input & Control"
    ),
    ComponentInfo(
        type = ComponentType.TOGGLE_SWITCH,
        name = "Toggle Switch",
        description = "On/Off control",
        icon = Icons.Default.ToggleOn,
        category = "Input & Control"
    ),
    ComponentInfo(
        type = ComponentType.SLIDER,
        name = "Slider",
        description = "Continuous value input",
        icon = Icons.Default.LinearScale,
        category = "Input & Control"
    ),
    ComponentInfo(
        type = ComponentType.NUMERIC_INPUT,
        name = "Numeric Input",
        description = "Enter precise values",
        icon = Icons.Default.Dialpad,
        category = "Input & Control"
    ),

    // Display & Output Components
    ComponentInfo(
        type = ComponentType.VALUE_DISPLAY,
        name = "Value Display",
        description = "Show sensor data",
        icon = Icons.Default.TextFields,
        category = "Display & Output"
    ),
    ComponentInfo(
        type = ComponentType.INDICATOR_LIGHT,
        name = "Indicator Light",
        description = "Status indicator",
        icon = Icons.Default.Lightbulb,
        category = "Display & Output"
    ),
    ComponentInfo(
        type = ComponentType.GAUGE,
        name = "Gauge",
        description = "Circular display",
        icon = Icons.Default.Speed,
        category = "Display & Output"
    ),

    // Visualization Components
    ComponentInfo(
        type = ComponentType.LINE_CHART,
        name = "Line Chart",
        description = "Data trends over time",
        icon = Icons.Default.ShowChart,
        category = "Visualization"
    ),

    // Layout Components
    ComponentInfo(
        type = ComponentType.GROUP_BOX,
        name = "Group Box",
        description = "Organize components",
        icon = Icons.Default.Tab,
        category = "Layout"
    )
)