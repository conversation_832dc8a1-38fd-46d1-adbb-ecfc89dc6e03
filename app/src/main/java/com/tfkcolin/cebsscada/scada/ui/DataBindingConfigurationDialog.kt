package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.scada.DataBinding
import com.tfkcolin.cebsscada.scada.DataFormat

/**
 * Dialog for configuring data binding settings for SCADA components
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataBindingConfigurationDialog(
    currentBinding: DataBinding,
    onDismiss: () -> Unit,
    onSave: (DataBinding) -> Unit,
    componentName: String = "Component"
) {
    var selectedProtocol by remember { mutableStateOf(currentBinding.protocol) }
    var address by remember { mutableStateOf(currentBinding.address) }
    var dataFormat by remember { mutableStateOf(currentBinding.dataFormat) }
    var payload by remember { mutableStateOf(currentBinding.payload) }
    var prefix by remember { mutableStateOf(currentBinding.prefix) }
    var suffix by remember { mutableStateOf(currentBinding.suffix) }
    var jsonPath by remember { mutableStateOf(currentBinding.jsonPath) }
    var decimalPlaces by remember { mutableStateOf(currentBinding.decimalPlaces) }

    // Device discovery dialog state
    var showDeviceDiscovery by remember { mutableStateOf(false) }

    // Validation
    val isValid = selectedProtocol == null ||
                  (selectedProtocol != null && address.isNotBlank())

    val scrollState = rememberScrollState()

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .fillMaxHeight(0.8f),
        title = {
            Text("Configure Data Binding - $componentName")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Protocol Selection
                Text(
                    text = "Communication Protocol",
                    style = MaterialTheme.typography.titleSmall
                )

                var expanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedProtocol?.name?.replace("_", " ") ?: "No Protocol (Static)",
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Protocol") },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                        },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("No Protocol (Static)") },
                            onClick = {
                                selectedProtocol = null
                                address = ""
                                expanded = false
                            }
                        )
                        CommunicationProtocol.values()
                            .filter { it != CommunicationProtocol.UNKNOWN }
                            .forEach { protocol ->
                                DropdownMenuItem(
                                    text = { Text(protocol.name.replace("_", " ")) },
                                    onClick = {
                                        selectedProtocol = protocol
                                        expanded = false
                                    }
                                )
                            }
                    }
                }

                // Address/Topic Field (only show if protocol is selected)
                if (selectedProtocol != null) {
                    val addressLabel = when (selectedProtocol) {
                        CommunicationProtocol.MQTT -> "MQTT Topic"
                        CommunicationProtocol.BLUETOOTH_BLE -> "BLE Characteristic UUID"
                        CommunicationProtocol.BLUETOOTH_CLASSIC -> "Bluetooth Address/Channel"
                        CommunicationProtocol.TCP, CommunicationProtocol.UDP -> "Host:Port"
                        CommunicationProtocol.SERIAL -> "Serial Port"
                        CommunicationProtocol.MODBUS -> "Modbus Address"
                        else -> "Address"
                    }

                    val addressPlaceholder = when (selectedProtocol) {
                        CommunicationProtocol.MQTT -> "e.g., sensors/temperature"
                        CommunicationProtocol.BLUETOOTH_BLE -> "e.g., 00002a37-0000-1000-8000-00805f9b34fb"
                        CommunicationProtocol.BLUETOOTH_CLASSIC -> "e.g., 00:11:22:33:44:55"
                        CommunicationProtocol.TCP, CommunicationProtocol.UDP -> "e.g., *************:8080"
                        CommunicationProtocol.SERIAL -> "e.g., /dev/ttyUSB0"
                        CommunicationProtocol.MODBUS -> "e.g., *************:502"
                        else -> ""
                    }

                    Column {
                        OutlinedTextField(
                            value = address,
                            onValueChange = { address = it },
                            label = { Text(addressLabel) },
                            placeholder = { Text(addressPlaceholder) },
                            modifier = Modifier.fillMaxWidth(),
                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                            isError = selectedProtocol != null && address.isBlank(),
                            supportingText = if (selectedProtocol != null && address.isBlank()) {
                                { Text("Address is required when protocol is selected") }
                            } else null,
                            trailingIcon = {
                                // Show device discovery button for discoverable protocols
                                when (selectedProtocol) {
                                    CommunicationProtocol.BLUETOOTH_CLASSIC,
                                    CommunicationProtocol.BLUETOOTH_BLE -> {
                                        IconButton(onClick = { showDeviceDiscovery = true }) {
                                            Icon(
                                                Icons.Default.Search,
                                                contentDescription = "Discover Devices"
                                            )
                                        }
                                    }
                                    else -> {}
                                }
                            }
                        )

                        // Device discovery hint for supported protocols
                        when (selectedProtocol) {
                            CommunicationProtocol.BLUETOOTH_CLASSIC,
                            CommunicationProtocol.BLUETOOTH_BLE -> {
                                TextButton(
                                    onClick = { showDeviceDiscovery = true },
                                    modifier = Modifier.align(Alignment.End)
                                ) {
                                    Icon(
                                        Icons.Default.Search,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text("Discover Devices", style = MaterialTheme.typography.bodySmall)
                                }
                            }
                            CommunicationProtocol.MQTT -> {
                                Text(
                                    text = "For MQTT, enter the topic name (e.g., sensors/temperature)",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            CommunicationProtocol.TCP, CommunicationProtocol.UDP -> {
                                Text(
                                    text = "Enter host:port (e.g., *************:8080)",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            CommunicationProtocol.SERIAL -> {
                                Text(
                                    text = "Enter serial device path (e.g., /dev/ttyUSB0)",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            CommunicationProtocol.MODBUS -> {
                                Text(
                                    text = "Enter Modbus TCP address (e.g., *************:502)",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            CommunicationProtocol.UNKNOWN, null -> {
                                // No specific hint for unknown protocols
                            }
                        }
                    }
                }

                // Data Format Selection
                Text(
                    text = "Data Format",
                    style = MaterialTheme.typography.titleSmall
                )

                var formatExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = formatExpanded,
                    onExpandedChange = { formatExpanded = it }
                ) {
                    OutlinedTextField(
                        value = dataFormat.name,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Data Format") },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = formatExpanded)
                        },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )

                    ExposedDropdownMenu(
                        expanded = formatExpanded,
                        onDismissRequest = { formatExpanded = false }
                    ) {
                        DataFormat.values().forEach { format ->
                            DropdownMenuItem(
                                text = { Text(format.name) },
                                onClick = {
                                    dataFormat = format
                                    formatExpanded = false
                                }
                            )
                        }
                    }
                }

                // Payload field (for write operations)
                OutlinedTextField(
                    value = payload,
                    onValueChange = { payload = it },
                    label = { Text("Write Payload (Optional)") },
                    placeholder = { Text("Value to send when component is activated") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                    supportingText = {
                        Text("For buttons/switches: value sent when activated")
                    }
                )

                // Display Formatting Section
                Text(
                    text = "Display Formatting",
                    style = MaterialTheme.typography.titleSmall
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = prefix,
                        onValueChange = { prefix = it },
                        label = { Text("Prefix") },
                        placeholder = { Text("e.g., Temp: ") },
                        modifier = Modifier.weight(1f),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                    )

                    OutlinedTextField(
                        value = suffix,
                        onValueChange = { suffix = it },
                        label = { Text("Suffix") },
                        placeholder = { Text("e.g., °C") },
                        modifier = Modifier.weight(1f),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                    )
                }

                // JSON Path (only for JSON format)
                if (dataFormat == DataFormat.JSON) {
                    OutlinedTextField(
                        value = jsonPath,
                        onValueChange = { jsonPath = it },
                        label = { Text("JSON Path (Optional)") },
                        placeholder = { Text("e.g., $.temperature.value") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                        supportingText = {
                            Text("JSONPath expression to extract value from JSON data")
                        }
                    )
                }

                // Decimal Places (only for numeric formats)
                if (dataFormat == DataFormat.FLOAT) {
                    Text(
                        text = "Decimal Places: $decimalPlaces",
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Slider(
                        value = decimalPlaces.toFloat(),
                        onValueChange = { decimalPlaces = it.toInt() },
                        valueRange = 0f..6f,
                        steps = 6,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Info section
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "Data Binding Information",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = when (selectedProtocol) {
                                null -> "No protocol selected. Component will display static content."
                                CommunicationProtocol.MQTT ->
                                    "Component will subscribe to MQTT topic and update when messages are received."
                                CommunicationProtocol.BLUETOOTH_BLE ->
                                    "Component will read from BLE characteristic and update on notifications."
                                CommunicationProtocol.BLUETOOTH_CLASSIC ->
                                    "Component will communicate with Bluetooth Classic device."
                                else ->
                                    "Component will communicate using the selected protocol."
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val newBinding = DataBinding(
                        protocol = selectedProtocol,
                        address = address.trim(),
                        dataFormat = dataFormat,
                        payload = payload.trim(),
                        prefix = prefix.trim(),
                        suffix = suffix.trim(),
                        jsonPath = jsonPath.trim(),
                        decimalPlaces = decimalPlaces
                    )
                    onSave(newBinding)
                },
                enabled = isValid
            ) {
                Text("Save Configuration")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )

    // Device Discovery Dialog
    if (showDeviceDiscovery && selectedProtocol != null) {
        DeviceDiscoveryDialog(
            selectedProtocol = selectedProtocol,
            onDeviceSelected = { device ->
                // Set the address based on the selected device
                address = when (device) {
                    is com.tfkcolin.cebsscada.communication.models.BluetoothDevice -> device.address
                    is com.tfkcolin.cebsscada.communication.models.MqttBroker -> "" // User enters topic manually
                    is com.tfkcolin.cebsscada.communication.models.TcpServer,
                    is com.tfkcolin.cebsscada.communication.models.UdpServer -> "${device.address}:${device.properties["port"]}"
                    else -> device.address
                }
                showDeviceDiscovery = false
            },
            onDismiss = { showDeviceDiscovery = false }
        )
    }
}

/**
 * Quick data binding setup for simple cases
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickDataBindingDialog(
    currentBinding: DataBinding,
    onDismiss: () -> Unit,
    onSave: (DataBinding) -> Unit,
    componentName: String = "Component",
    suggestedProtocol: CommunicationProtocol? = null,
    suggestedAddress: String = ""
) {
    var selectedProtocol by remember { mutableStateOf(suggestedProtocol ?: currentBinding.protocol) }
    var address by remember { mutableStateOf(suggestedAddress.ifBlank { currentBinding.address }) }

    val isValid = selectedProtocol == null || address.isNotBlank()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Quick Data Binding - $componentName")
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Protocol Selection
                var expanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedProtocol?.name?.replace("_", " ") ?: "No Protocol",
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Protocol") },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                        },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("No Protocol") },
                            onClick = {
                                selectedProtocol = null
                                address = ""
                                expanded = false
                            }
                        )
                        CommunicationProtocol.values()
                            .filter { it != CommunicationProtocol.UNKNOWN }
                            .forEach { protocol ->
                                DropdownMenuItem(
                                    text = { Text(protocol.name.replace("_", " ")) },
                                    onClick = {
                                        selectedProtocol = protocol
                                        expanded = false
                                    }
                                )
                            }
                    }
                }

                // Address field
                if (selectedProtocol != null) {
                    val placeholder = when (selectedProtocol) {
                        CommunicationProtocol.MQTT -> "MQTT Topic (e.g., sensors/temperature)"
                        CommunicationProtocol.BLUETOOTH_BLE -> "BLE UUID"
                        else -> "Address"
                    }

                    OutlinedTextField(
                        value = address,
                        onValueChange = { address = it },
                        label = { Text("Address") },
                        placeholder = { Text(placeholder) },
                        modifier = Modifier.fillMaxWidth(),
                        isError = address.isBlank(),
                        supportingText = if (address.isBlank()) {
                            { Text("Address is required") }
                        } else null
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val newBinding = currentBinding.copy(
                        protocol = selectedProtocol,
                        address = address.trim()
                    )
                    onSave(newBinding)
                },
                enabled = isValid
            ) {
                Text("Apply")
            }
        },
        dismissButton = {
            Row {
                TextButton(
                    onClick = {
                        // Open full configuration dialog
                        onDismiss()
                        // Note: This would need to be handled by parent component
                    }
                ) {
                    Text("Advanced")
                }
                Spacer(modifier = Modifier.width(8.dp))
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        }
    )
}