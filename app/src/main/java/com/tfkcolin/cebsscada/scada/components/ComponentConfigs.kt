package com.tfkcolin.cebsscada.scada.components

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.cebsscada.scada.ComponentConfig
import com.tfkcolin.cebsscada.scada.DataBinding
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * Push Button Configuration
 */
@Serializable
data class PushButtonConfig(
    override val label: String = "Button",
    @Contextual override val backgroundColor: Color = Color.Blue,
    @Contextual override val borderColor: Color = Color.Black,
    @Contextual override val borderWidth: Dp = 2.dp,
    @Contextual val textColor: Color = Color.White,
    @Contextual val textSize: TextUnit = 14.sp,
    val buttonShape: ButtonShape = ButtonShape.ROUNDED_RECTANGLE,
    val dataBinding: DataBinding = DataBinding()
) : ComponentConfig

enum class ButtonShape {
    RECTANGLE,
    ROUNDED_RECTANGLE,
    CIRCLE
}

/**
 * Toggle Switch Configuration
 */
@Serializable
data class ToggleSwitchConfig(
    override val label: String = "Switch",
    @Contextual override val backgroundColor: Color = Color.LightGray,
    @Contextual override val borderColor: Color = Color.Gray,
    @Contextual override val borderWidth: Dp = 1.dp,
    @Contextual val onColor: Color = Color.Green,
    @Contextual val offColor: Color = Color.Gray,
    val switchStyle: SwitchStyle = SwitchStyle.SLIDER,
    val dataBinding: DataBinding = DataBinding(),
    val onPayload: String = "true",
    val offPayload: String = "false",
    val stateSourceBinding: DataBinding? = null, // For reading current state
    val initialState: Boolean = false
) : ComponentConfig

enum class SwitchStyle {
    SLIDER,
    TOGGLE_BUTTON
}

/**
 * Slider Configuration
 */
@Serializable
data class SliderConfig(
    override val label: String = "Slider",
    @Contextual override val backgroundColor: Color = Color.LightGray,
    @Contextual override val borderColor: Color = Color.Gray,
    @Contextual override val borderWidth: Dp = 1.dp,
    @Contextual val barColor: Color = Color.Blue,
    @Contextual val handleColor: Color = Color.White,
    val orientation: Orientation = Orientation.HORIZONTAL,
    val dataBinding: DataBinding = DataBinding(),
    val minValue: Float = 0f,
    val maxValue: Float = 100f,
    val step: Float = 1f,
    val sendOnRelease: Boolean = true
) : ComponentConfig

enum class Orientation {
    HORIZONTAL,
    VERTICAL
}

/**
 * Numeric Input Configuration
 */
@Serializable
data class NumericInputConfig(
    override val label: String = "Input",
    @Contextual override val backgroundColor: Color = Color.White,
    @Contextual override val borderColor: Color = Color.Gray,
    @Contextual override val borderWidth: Dp = 1.dp,
    @Contextual val textColor: Color = Color.Black,
    @Contextual val textSize: TextUnit = 14.sp,
    val dataBinding: DataBinding = DataBinding(),
    val minValue: Float? = null,
    val maxValue: Float? = null
) : ComponentConfig

/**
 * Value Display Configuration
 */
@Serializable
data class ValueDisplayConfig(
    override val label: String = "",
    override @Contextual val backgroundColor: Color = Color.Transparent,
    override @Contextual val borderColor: Color = Color.Transparent,
    override @Contextual val borderWidth: Dp = 0.dp,
    @Contextual val textColor: Color = Color.Black,
    @Contextual val textSize: TextUnit = 16.sp,
    val fontWeight: FontWeight = FontWeight.NORMAL,
    val dataBinding: DataBinding = DataBinding(),
    val defaultText: String = "--"
) : ComponentConfig

enum class FontWeight {
    NORMAL,
    BOLD
}

/**
 * Indicator Light Configuration
 */
@Serializable
data class IndicatorLightConfig(
    override val label: String = "",
    override @Contextual val backgroundColor: Color = Color.LightGray,
    override @Contextual val borderColor: Color = Color.Gray,
    override @Contextual val borderWidth: Dp = 1.dp,
    val shape: LightShape = LightShape.CIRCLE,
    val dataBinding: DataBinding = DataBinding(),
    val colorConditions: List<ColorCondition> = emptyList(),
    @Contextual val defaultColor: Color = Color.Gray
) : ComponentConfig

enum class LightShape {
    CIRCLE,
    SQUARE
}

@Serializable
data class ColorCondition(
    val condition: String, // e.g., "value > 90"
    @Contextual val color: Color,
    val flashing: Boolean = false
)

/**
 * Gauge Configuration
 */
@Serializable
data class GaugeConfig(
    override val label: String = "Gauge",
    override @Contextual val backgroundColor: Color = Color.White,
    override @Contextual val borderColor: Color = Color.Gray,
    override @Contextual val borderWidth: Dp = 1.dp,
    val gaugeStyle: GaugeStyle = GaugeStyle.SEMI_CIRCLE,
    val dataBinding: DataBinding = DataBinding(),
    val minValue: Float = 0f,
    val maxValue: Float = 100f,
    val units: String = "",
    val colorRanges: List<ColorRange> = emptyList()
) : ComponentConfig

enum class GaugeStyle {
    FULL_CIRCLE,
    SEMI_CIRCLE
}

@Serializable
data class ColorRange(
    val minValue: Float,
    val maxValue: Float,
    @Contextual val color: Color
)

/**
 * Line Chart Configuration
 */
@Serializable
data class LineChartConfig(
    override val label: String = "Chart",
    override @Contextual val backgroundColor: Color = Color.White,
    override @Contextual val borderColor: Color = Color.Gray,
    override @Contextual val borderWidth: Dp = 1.dp,
    @Contextual val lineColor: Color = Color.Blue,
    val gridLines: Boolean = true,
    val dataSeries: List<ChartDataSeries> = listOf(ChartDataSeries()),
    val timeWindowSeconds: Int = 300, // 5 minutes
    val yAxisMin: Float? = null,
    val yAxisMax: Float? = null
) : ComponentConfig

@Serializable
data class ChartDataSeries(
    val name: String = "Series 1",
    val dataBinding: DataBinding = DataBinding(),
    @Contextual val lineColor: Color = Color.Blue
)

/**
 * Group Box Configuration
 */
@Serializable
data class GroupBoxConfig(
    override val label: String = "Group",
    override @Contextual val backgroundColor: Color = Color.Transparent,
    override @Contextual val borderColor: Color = Color.Gray,
    override @Contextual val borderWidth: Dp = 1.dp,
    val borderStyle: BorderStyle = BorderStyle.DASHED
) : ComponentConfig

enum class BorderStyle {
    SOLID,
    DASHED
}