package com.tfkcolin.cebsscada.communication

import android.bluetooth.BluetoothGattService
import com.tfkcolin.cebsscada.communication.bluetooth.BluetoothCommunicationService
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationService
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationServiceFactory
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScanner
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScannerFactory
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageResult
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Central manager for all communication protocols
 * Coordinates multiple communication services and provides a unified interface
 */
@Singleton
class CommunicationManager @Inject constructor(
    private val serviceFactory: CommunicationServiceFactory,
    private val scannerFactory: DeviceScannerFactory
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Active services and scanners
    private val activeServices = mutableMapOf<CommunicationProtocol, CommunicationService>()
    private val activeScanners = mutableMapOf<CommunicationProtocol, DeviceScanner>()
    
    // State flows
    private val _allMessages = MutableSharedFlow<CommunicationMessage>()
    val allMessages: Flow<CommunicationMessage> = _allMessages.asSharedFlow()
    
    private val _allErrors = MutableSharedFlow<String>()
    val allErrors: Flow<String> = _allErrors.asSharedFlow()
    
    private val _allDevices = MutableStateFlow<List<CommunicationDevice>>(emptyList())
    val allDevices: StateFlow<List<CommunicationDevice>> = _allDevices.asStateFlow()
    
    private val _connectionStates = MutableStateFlow<Map<CommunicationProtocol, ConnectionState>>(emptyMap())
    val connectionStates: StateFlow<Map<CommunicationProtocol, ConnectionState>> = _connectionStates.asStateFlow()
    
    private val _connectedDevices = MutableStateFlow<Map<CommunicationProtocol, CommunicationDevice>>(emptyMap())
    val connectedDevices: StateFlow<Map<CommunicationProtocol, CommunicationDevice>> = _connectedDevices.asStateFlow()
    
    /**
     * Get or create a communication service for the specified protocol
     */
    fun getService(protocol: CommunicationProtocol): CommunicationService? {
        return activeServices[protocol] ?: run {
            val service = serviceFactory.createService(protocol)
            service?.let {
                activeServices[protocol] = it
                setupServiceListeners(protocol, it)
            }
            service
        }
    }
    
    /**
     * Get or create a device scanner for the specified protocol
     */
    fun getScanner(protocol: CommunicationProtocol): DeviceScanner? {
        return activeScanners[protocol] ?: run {
            val scanner = scannerFactory.createScanner(protocol)
            scanner?.let {
                activeScanners[protocol] = it
                setupScannerListeners(protocol, it)
            }
            scanner
        }
    }
    
    /**
     * Connect to a device using the appropriate protocol
     */
    suspend fun connect(device: CommunicationDevice, options: Map<String, Any> = emptyMap()): Boolean {
        val service = getService(device.protocol) ?: return false
        return service.connect(device, options)
    }
    
    /**
     * Disconnect from a device
     */
    suspend fun disconnect(protocol: CommunicationProtocol) {
        activeServices[protocol]?.disconnect()
    }
    
    /**
     * Disconnect from all devices
     */
    suspend fun disconnectAll() {
        activeServices.values.forEach { it.disconnect() }
    }
    
    /**
     * Send a message using the appropriate protocol
     */
    suspend fun sendMessage(message: CommunicationMessage): MessageResult {
        val service = activeServices[message.protocol]
        return service?.sendMessage(message) ?: MessageResult(
            success = false,
            messageId = message.id,
            error = "No active service for protocol ${message.protocol}"
        )
    }
    
    /**
     * Send text to a specific device
     */
    suspend fun sendText(
        protocol: CommunicationProtocol,
        text: String,
        topic: String? = null
    ): MessageResult {
        val service = activeServices[protocol]
        return service?.sendText(text, topic) ?: MessageResult(
            success = false,
            messageId = "",
            error = "No active service for protocol $protocol"
        )
    }
    
    /**
     * Start discovery for a specific protocol
     */
    suspend fun startDiscovery(protocol: CommunicationProtocol) {
        getScanner(protocol)?.startScan()
        getService(protocol)?.startDiscovery()
    }
    
    /**
     * Start discovery for all supported protocols
     */
    suspend fun startDiscoveryAll() {
        serviceFactory.getSupportedProtocols().forEach { protocol ->
            startDiscovery(protocol)
        }
    }
    
    /**
     * Stop discovery for a specific protocol
     */
    suspend fun stopDiscovery(protocol: CommunicationProtocol) {
        activeScanners[protocol]?.stopScan()
        activeServices[protocol]?.stopDiscovery()
    }
    
    /**
     * Stop discovery for all protocols
     */
    suspend fun stopDiscoveryAll() {
        activeScanners.values.forEach { it.stopScan() }
        activeServices.values.forEach { it.stopDiscovery() }
    }
    
    /**
     * Subscribe to a topic (for protocols that support it)
     */
    suspend fun subscribe(protocol: CommunicationProtocol, subscription: TopicSubscription): Boolean {
        return activeServices[protocol]?.subscribe(subscription) ?: false
    }
    
    /**
     * Unsubscribe from a topic (for protocols that support it)
     */
    suspend fun unsubscribe(protocol: CommunicationProtocol, topic: String): Boolean {
        return activeServices[protocol]?.unsubscribe(topic) ?: false
    }
    
    /**
     * Get devices for a specific protocol
     */
    fun getDevices(protocol: CommunicationProtocol): Flow<List<CommunicationDevice>> {
        return getService(protocol)?.getAvailableDevices() ?: MutableStateFlow(emptyList())
    }

    /**
     * Get BLE services for a specific protocol
     */
    fun getServices(protocol: CommunicationProtocol): List<BluetoothGattService>? {
        val service = activeServices[protocol]
        if (service is BluetoothCommunicationService) {
            return service.getAvailableServices()
        }
        return null
    }
    
    /**
     * Get subscriptions for a specific protocol
     */
    fun getSubscriptions(protocol: CommunicationProtocol): Flow<List<TopicSubscription>> {
        return activeServices[protocol]?.getSubscriptions() ?: MutableStateFlow(emptyList())
    }
    
    /**
     * Get connection statistics for all protocols
     */
    fun getAllConnectionStats(): Map<CommunicationProtocol, Map<String, Any>> {
        return activeServices.mapValues { (_, service) ->
            service.getConnectionStats()
        }
    }
    
    /**
     * Get supported protocols
     */
    fun getSupportedProtocols(): List<CommunicationProtocol> {
        return serviceFactory.getSupportedProtocols()
    }
    
    /**
     * Check if a protocol is supported
     */
    fun isProtocolSupported(protocol: CommunicationProtocol): Boolean {
        return serviceFactory.isProtocolSupported(protocol)
    }
    
    /**
     * Clean up all resources
     */
    suspend fun cleanup() {
        activeServices.values.forEach { it.cleanup() }
        activeScanners.values.forEach { it.cleanup() }
        activeServices.clear()
        activeScanners.clear()
    }
    
    /**
     * Setup listeners for a communication service
     */
    private fun setupServiceListeners(protocol: CommunicationProtocol, service: CommunicationService) {
        scope.launch {
            service.connectionState.collect { state ->
                updateConnectionState(protocol, state)
                if (state == ConnectionState.CONNECTED) {
                    service.connectedDevice?.let { device ->
                        updateConnectedDevice(protocol, device)
                    }
                } else if (state == ConnectionState.DISCONNECTED) {
                    removeConnectedDevice(protocol)
                }
            }
        }
        
        scope.launch {
            service.incomingMessages.collect { message ->
                _allMessages.emit(message)
            }
        }
        
        scope.launch {
            service.connectionErrors.collect { error ->
                _allErrors.emit("$protocol: $error")
            }
        }
        
        scope.launch {
            service.getAvailableDevices().collect { devices ->
                updateDevicesForProtocol(protocol, devices)
            }
        }
    }
    
    /**
     * Setup listeners for a device scanner
     */
    private fun setupScannerListeners(protocol: CommunicationProtocol, scanner: DeviceScanner) {
        scope.launch {
            scanner.discoveredDevices.collect { devices ->
                updateDevicesForProtocol(protocol, devices)
            }
        }
        
        scope.launch {
            scanner.scanningErrors.collect { error ->
                _allErrors.emit("$protocol Scanner: $error")
            }
        }
    }
    
    private fun updateConnectionState(protocol: CommunicationProtocol, state: ConnectionState) {
        val current = _connectionStates.value.toMutableMap()
        current[protocol] = state
        _connectionStates.value = current
    }
    
    private fun updateConnectedDevice(protocol: CommunicationProtocol, device: CommunicationDevice) {
        val current = _connectedDevices.value.toMutableMap()
        current[protocol] = device
        _connectedDevices.value = current
    }
    
    private fun removeConnectedDevice(protocol: CommunicationProtocol) {
        val current = _connectedDevices.value.toMutableMap()
        current.remove(protocol)
        _connectedDevices.value = current
    }
    
    private fun updateDevicesForProtocol(protocol: CommunicationProtocol, devices: List<CommunicationDevice>) {
        val allDevices = _allDevices.value.toMutableList()
        // Remove existing devices for this protocol
        allDevices.removeAll { it.protocol == protocol }
        // Add new devices
        allDevices.addAll(devices)
        _allDevices.value = allDevices
    }
}
