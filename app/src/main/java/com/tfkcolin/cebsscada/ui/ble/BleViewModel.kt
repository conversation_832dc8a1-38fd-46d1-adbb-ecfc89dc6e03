package com.tfkcolin.cebsscada.ui.ble

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattService
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.services.BleService
import com.tfkcolin.cebsscada.services.BleConnectionState
import com.tfkcolin.cebsscada.services.BleCharacteristicData
import com.tfkcolin.cebsscada.services.BleStats
import com.tfkcolin.cebsscada.services.BleOperation
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager
import com.tfkcolin.cebsscada.ui.shared.BluetoothScannerManager
import com.tfkcolin.cebsscada.ui.shared.ScannedBluetoothDevice
import com.tfkcolin.cebsscada.ui.shared.ScanType
import com.tfkcolin.cebsscada.ui.shared.BluetoothDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for BLE screen
 * Manages BLE connections, scanning, and GATT operations
 */
@HiltViewModel
class BleViewModel @Inject constructor(
    private val bleService: BleService,
    private val bluetoothScannerManager: BluetoothScannerManager,
    private val bluetoothPermissionManager: BluetoothPermissionManager
) : ViewModel() {

    // Permission state
    val hasPermissions = bluetoothPermissionManager.hasAllPermissions
    val permissionState = bluetoothPermissionManager.permissionState

    // Connection state
    val connectionState = bleService.connectionState
    val connectedDevice = bleService.connectedDeviceFlow
    val gattServices = bleService.gattServices
    val characteristicData = bleService.characteristicData
    val connectionErrors = bleService.connectionErrors

    private val _connectingDevice = MutableStateFlow<BluetoothDevice?>(null)
    val connectingDevice: StateFlow<BluetoothDevice?> = _connectingDevice.asStateFlow()

    // Scanning state
    val isScanning = bluetoothScannerManager.isScanning
    val discoveredDevices = bluetoothScannerManager.discoveredDevices
        .map { devices -> devices.filter { it.deviceType == BluetoothDeviceType.BLE } }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    val scanErrors = bluetoothScannerManager.scanErrors

    // UI state
    private val _messages = MutableStateFlow<List<BleMessage>>(emptyList())
    val messages: StateFlow<List<BleMessage>> = _messages.asStateFlow()

    private val _selectedTab = MutableStateFlow(0)
    val selectedTab: StateFlow<Int> = _selectedTab.asStateFlow()

    private val _selectedService = MutableStateFlow<BluetoothGattService?>(null)
    val selectedService: StateFlow<BluetoothGattService?> = _selectedService.asStateFlow()

    init {
        // Check permissions on initialization
        bluetoothPermissionManager.checkPermissions()

        // Collect connection state changes
        viewModelScope.launch {
            bleService.connectionState.collect { state ->
                if (state == BleConnectionState.CONNECTED ||
                    state == BleConnectionState.DISCONNECTED ||
                    state == BleConnectionState.ERROR) {
                    _connectingDevice.value = null
                }
            }
        }

        // Collect characteristic data and convert to messages
        viewModelScope.launch {
            characteristicData.collect { data ->
                addMessage(
                    BleMessage(
                        serviceUuid = data.serviceUuid,
                        characteristicUuid = data.characteristicUuid,
                        content = data.text,
                        data = data.data,
                        timestamp = data.timestamp,
                        operation = data.operation
                    )
                )
            }
        }

        // Collect connection errors
        viewModelScope.launch {
            connectionErrors.collect { error ->
                // Handle connection errors (could show snackbar, etc.)
            }
        }
    }

    /**
     * Start scanning for BLE devices
     */
    fun startScan() {
        if (!bluetoothPermissionManager.checkPermissions()) {
            return
        }

        viewModelScope.launch {
            bluetoothScannerManager.startScan(ScanType.BLE)
        }
    }

    /**
     * Stop scanning
     */
    fun stopScan() {
        viewModelScope.launch {
            bluetoothScannerManager.stopScan()
        }
    }

    /**
     * Connect to a BLE device
     */
    fun connect(scannedDevice: ScannedBluetoothDevice) {
        viewModelScope.launch {
            _connectingDevice.value = scannedDevice.device
            val connected = bleService.connect(scannedDevice.device)
            if (!connected) {
                _connectingDevice.value = null
            }
        }
    }

    /**
     * Disconnect from current device
     */
    fun disconnect() {
        viewModelScope.launch {
            bleService.disconnect()
        }
    }

    /**
     * Read characteristic
     */
    fun readCharacteristic(serviceUuid: String, characteristicUuid: String) {
        viewModelScope.launch {
            bleService.readCharacteristic(serviceUuid, characteristicUuid)
        }
    }

    /**
     * Write to characteristic
     */
    fun writeCharacteristic(serviceUuid: String, characteristicUuid: String, data: ByteArray) {
        viewModelScope.launch {
            bleService.writeCharacteristic(serviceUuid, characteristicUuid, data)
        }
    }

    /**
     * Write text to characteristic
     */
    fun writeTextToCharacteristic(serviceUuid: String, characteristicUuid: String, text: String) {
        writeCharacteristic(serviceUuid, characteristicUuid, text.toByteArray())
    }

    /**
     * Enable notifications for characteristic
     */
    fun enableNotifications(serviceUuid: String, characteristicUuid: String) {
        viewModelScope.launch {
            bleService.enableNotifications(serviceUuid, characteristicUuid)
        }
    }

    /**
     * Disable notifications for characteristic
     */
    fun disableNotifications(serviceUuid: String, characteristicUuid: String) {
        viewModelScope.launch {
            bleService.disableNotifications(serviceUuid, characteristicUuid)
        }
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): BleStats {
        return bleService.getConnectionStats()
    }

    /**
     * Clear messages
     */
    fun clearMessages() {
        _messages.value = emptyList()
    }

    /**
     * Set selected tab
     */
    fun setSelectedTab(tabIndex: Int) {
        _selectedTab.value = tabIndex
    }

    /**
     * Set selected service
     */
    fun setSelectedService(service: BluetoothGattService?) {
        _selectedService.value = service
    }

    /**
     * Clear discovered devices
     */
    fun clearDevices() {
        bluetoothScannerManager.clearDevices()
    }

    /**
     * Add message to the list
     */
    private fun addMessage(message: BleMessage) {
        val currentMessages = _messages.value.toMutableList()
        currentMessages.add(message)
        
        // Keep only last 1000 messages to prevent memory issues
        if (currentMessages.size > 1000) {
            currentMessages.removeAt(0)
        }
        
        _messages.value = currentMessages
    }

    /**
     * Get characteristic properties as readable string
     */
    fun getCharacteristicProperties(properties: Int): List<String> {
        val propertyList = mutableListOf<String>()
        
        if (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_READ != 0) {
            propertyList.add("Read")
        }
        if (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE != 0) {
            propertyList.add("Write")
        }
        if (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) {
            propertyList.add("Write No Response")
        }
        if (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) {
            propertyList.add("Notify")
        }
        if (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) {
            propertyList.add("Indicate")
        }
        
        return propertyList
    }

    /**
     * Check if characteristic supports read
     */
    fun canRead(properties: Int): Boolean {
        return properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_READ != 0
    }

    /**
     * Check if characteristic supports write
     */
    fun canWrite(properties: Int): Boolean {
        return (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE != 0) ||
               (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0)
    }

    /**
     * Check if characteristic supports notifications
     */
    fun canNotify(properties: Int): Boolean {
        return (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) ||
               (properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_INDICATE != 0)
    }

    override fun onCleared() {
        super.onCleared()
        // Service cleanup is handled by dependency injection
    }
}

/**
 * BLE message for UI display
 */
data class BleMessage(
    val serviceUuid: String,
    val characteristicUuid: String,
    val content: String,
    val data: ByteArray,
    val timestamp: Long,
    val operation: BleOperation
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as BleMessage
        return serviceUuid == other.serviceUuid &&
               characteristicUuid == other.characteristicUuid &&
               content == other.content &&
               data.contentEquals(other.data) &&
               timestamp == other.timestamp &&
               operation == other.operation
    }

    override fun hashCode(): Int {
        var result = serviceUuid.hashCode()
        result = 31 * result + characteristicUuid.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + data.contentHashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + operation.hashCode()
        return result
    }
}
