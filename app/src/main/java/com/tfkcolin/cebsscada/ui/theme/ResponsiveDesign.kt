package com.tfkcolin.cebsscada.ui.theme

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Responsive design system for CEBS SCADA application
 * Provides adaptive breakpoints, spacing, and sizing based on screen characteristics
 */

/**
 * Screen size categories based on Material Design guidelines
 */
enum class ScreenSize {
    COMPACT,    // < 600dp width (phones)
    MEDIUM,     // 600dp - 840dp width (tablets, foldables)
    EXPANDED    // > 840dp width (large tablets, desktops)
}

/**
 * Screen orientation
 */
enum class ScreenOrientation {
    PORTRAIT,
    LANDSCAPE
}

/**
 * Device type classification
 */
enum class DeviceType {
    PHONE,
    TABLET,
    DESKTOP
}

/**
 * Responsive breakpoints following Material Design 3 guidelines
 * Updated to consider both width and height for better device classification
 */
object ResponsiveBreakpoints {
    // Width breakpoints
    val COMPACT_WIDTH_MAX = 600.dp
    val MEDIUM_WIDTH_MAX = 840.dp
    val EXPANDED_WIDTH_MIN = 840.dp

    // Height breakpoints for layout adjustments
    val SHORT_HEIGHT_MAX = 480.dp
    val MEDIUM_HEIGHT_MAX = 900.dp

    // Minimum dimension breakpoints for device type classification
    val PHONE_MAX_MIN_DIMENSION = 600.dp
    val TABLET_MAX_MIN_DIMENSION = 840.dp

    // Granular phone size breakpoints
    val SMALL_PHONE_MAX = 360.dp
    val NORMAL_PHONE_MAX = 480.dp
    val LARGE_PHONE_MAX = 600.dp
}

/**
 * Responsive spacing system that adapts to screen size and dimensions
 * Enhanced with granular scaling based on actual screen width and height
 */
@Stable
class ResponsiveSpacing(
    private val screenSize: ScreenSize,
    private val deviceType: DeviceType,
    private val screenWidthDp: Dp,
    private val screenHeightDp: Dp
) {
    // Height factor for vertical spacing adjustments
    private val heightFactor: Float = when {
        screenHeightDp < ResponsiveBreakpoints.SHORT_HEIGHT_MAX -> 0.8f
        screenHeightDp < ResponsiveBreakpoints.MEDIUM_HEIGHT_MAX -> 1.0f
        else -> 1.2f
    }

    // Base spacing units with granular scaling
    val extraSmall: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 2.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 4.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 6.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 8.dp
        else -> 10.dp
    }

    val small: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 4.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 6.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 8.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 12.dp
        else -> 16.dp
    }

    val medium: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 8.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 12.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 16.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 20.dp
        else -> 24.dp
    }

    val large: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 12.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 16.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 24.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 32.dp
        else -> 40.dp
    }

    val extraLarge: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 16.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 24.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 32.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 48.dp
        else -> 64.dp
    }

    // Content padding with height consideration
    val contentPadding: PaddingValues = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> PaddingValues(
            horizontal = 8.dp,
            vertical = (8.dp * heightFactor)
        )
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> PaddingValues(
            horizontal = 12.dp,
            vertical = (12.dp * heightFactor)
        )
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> PaddingValues(
            horizontal = 16.dp,
            vertical = (16.dp * heightFactor)
        )
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> PaddingValues(
            horizontal = 24.dp,
            vertical = (20.dp * heightFactor)
        )
        else -> PaddingValues(
            horizontal = 32.dp,
            vertical = (24.dp * heightFactor)
        )
    }

    // Screen edge padding
    val screenPadding: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 8.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 12.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 16.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 24.dp
        else -> 32.dp
    }

    // Component spacing
    val componentSpacing: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 4.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 6.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 8.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 12.dp
        else -> 16.dp
    }

    // Grid spacing
    val gridSpacing: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 6.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 8.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 12.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 16.dp
        else -> 20.dp
    }
}

/**
 * Responsive sizing system for components
 * Enhanced with dimension-aware scaling for better adaptation
 */
@Stable
class ResponsiveSizing(
    private val screenSize: ScreenSize,
    private val deviceType: DeviceType,
    private val screenWidthDp: Dp,
    private val screenHeightDp: Dp
) {
    // Minimum touch target size (accessibility) - remains constant
    val minTouchTarget: Dp = 48.dp

    // Button sizes with granular scaling
    val buttonHeight: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 44.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 46.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 48.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 52.dp
        else -> 56.dp
    }

    // Icon sizes with better granularity
    val iconSmall: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 14.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 16.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 18.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 20.dp
        else -> 24.dp
    }

    val iconMedium: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 20.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 22.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 24.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 28.dp
        else -> 32.dp
    }

    val iconLarge: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 24.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 28.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 32.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 40.dp
        else -> 48.dp
    }

    // Card sizes with height consideration
    val cardMinWidth: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 240.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 260.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 280.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 320.dp
        else -> 360.dp
    }

    val cardMaxWidth: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 320.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 360.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 400.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 480.dp
        else -> 560.dp
    }

    // Panel sizes for SCADA workspace - adjusted for smaller screens
    val paletteWidth: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 0.dp // Hidden on phones
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 220.dp
        else -> 280.dp
    }

    val propertiesWidth: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 0.dp // Hidden on phones
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 260.dp
        else -> 320.dp
    }

    // Dialog sizes with better scaling
    val dialogMaxWidth: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 280.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 300.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 320.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 480.dp
        else -> 600.dp
    }

    // FAB size with scaling
    val fabSize: Dp = when {
        screenWidthDp < ResponsiveBreakpoints.SMALL_PHONE_MAX -> 48.dp
        screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX -> 52.dp
        screenWidthDp < ResponsiveBreakpoints.LARGE_PHONE_MAX -> 56.dp
        screenWidthDp < ResponsiveBreakpoints.MEDIUM_WIDTH_MAX -> 64.dp
        else -> 72.dp
    }
}

/**
 * Responsive layout configuration
 * Enhanced with better orientation and height handling
 */
@Stable
class ResponsiveLayout(
    val screenSize: ScreenSize,
    val orientation: ScreenOrientation,
    val deviceType: DeviceType,
    val spacing: ResponsiveSpacing,
    val sizing: ResponsiveSizing,
    private val screenWidthDp: Dp,
    private val screenHeightDp: Dp
) {
    // Layout patterns with orientation consideration
    val shouldUseBottomNavigation: Boolean = deviceType == DeviceType.PHONE
    val shouldUseNavigationRail: Boolean = deviceType == DeviceType.TABLET ||
        (deviceType == DeviceType.PHONE && orientation == ScreenOrientation.LANDSCAPE && screenWidthDp >= ResponsiveBreakpoints.LARGE_PHONE_MAX)
    val shouldUseNavigationDrawer: Boolean = deviceType == DeviceType.DESKTOP ||
        (deviceType == DeviceType.TABLET && orientation == ScreenOrientation.LANDSCAPE)

    val shouldUseSinglePane: Boolean = deviceType == DeviceType.PHONE ||
        (deviceType == DeviceType.TABLET && orientation == ScreenOrientation.PORTRAIT && screenHeightDp < ResponsiveBreakpoints.MEDIUM_HEIGHT_MAX)
    val shouldUseTwoPane: Boolean = deviceType == DeviceType.TABLET && !shouldUseSinglePane
    val shouldUseThreePane: Boolean = deviceType == DeviceType.DESKTOP ||
        (deviceType == DeviceType.TABLET && orientation == ScreenOrientation.LANDSCAPE && screenWidthDp >= ResponsiveBreakpoints.EXPANDED_WIDTH_MIN)

    // Grid columns for different content types
    val workspaceGridColumns: Int = when {
        shouldUseSinglePane -> 1
        shouldUseTwoPane -> 2
        else -> 3
    }

    val settingsGridColumns: Int = when {
        shouldUseSinglePane -> 1
        shouldUseTwoPane -> 2
        else -> 3
    }

    // Component palette layout with better logic
    val shouldShowPaletteInline: Boolean = !shouldUseSinglePane
    val shouldShowPropertiesInline: Boolean = shouldUseThreePane

    // Additional layout helpers
    val isShortScreen: Boolean = screenHeightDp < ResponsiveBreakpoints.SHORT_HEIGHT_MAX
    val isWideScreen: Boolean = screenWidthDp >= ResponsiveBreakpoints.EXPANDED_WIDTH_MIN
    val shouldUseCompactSpacing: Boolean = screenWidthDp < ResponsiveBreakpoints.NORMAL_PHONE_MAX
}

/**
 * Remember responsive layout configuration
 * Updated to use minimum dimension for device classification to handle orientation properly
 */
@Composable
fun rememberResponsiveLayout(): ResponsiveLayout {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    return remember(configuration.screenWidthDp, configuration.screenHeightDp, configuration.orientation) {
        val screenWidthDp = configuration.screenWidthDp.dp
        val screenHeightDp = configuration.screenHeightDp.dp
        val minDimension = minOf(screenWidthDp, screenHeightDp)
        val maxDimension = maxOf(screenWidthDp, screenHeightDp)

        // Use minimum dimension for device type classification to ensure
        // phones remain phones regardless of orientation
        val screenSize = when {
            minDimension < ResponsiveBreakpoints.PHONE_MAX_MIN_DIMENSION -> ScreenSize.COMPACT
            minDimension < ResponsiveBreakpoints.TABLET_MAX_MIN_DIMENSION -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }

        val orientation = if (configuration.screenWidthDp > configuration.screenHeightDp) {
            ScreenOrientation.LANDSCAPE
        } else {
            ScreenOrientation.PORTRAIT
        }

        val deviceType = when {
            minDimension < ResponsiveBreakpoints.PHONE_MAX_MIN_DIMENSION -> DeviceType.PHONE
            minDimension < ResponsiveBreakpoints.TABLET_MAX_MIN_DIMENSION -> DeviceType.TABLET
            else -> DeviceType.DESKTOP
        }

        val spacing = ResponsiveSpacing(screenSize, deviceType, screenWidthDp, screenHeightDp)
        val sizing = ResponsiveSizing(screenSize, deviceType, screenWidthDp, screenHeightDp)

        ResponsiveLayout(screenSize, orientation, deviceType, spacing, sizing, screenWidthDp, screenHeightDp)
    }
}
