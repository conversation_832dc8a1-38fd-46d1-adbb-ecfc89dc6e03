package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Slider
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import com.tfkcolin.cebsscada.scada.ScadaComponent

/**
 * Organization tools dialog for component layout and management
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrganizationToolsDialog(
    onDismiss: () -> Unit,
    onAlignLeft: () -> Unit,
    onAlignCenter: () -> Unit,
    onAlignRight: () -> Unit,
    onAlignTop: () -> Unit,
    onAlignMiddle: () -> Unit,
    onAlignBottom: () -> Unit,
    onDistributeHorizontal: () -> Unit,
    onDistributeVertical: () -> Unit,
    onMatchSizes: (matchWidth: Boolean, matchHeight: Boolean) -> Unit,
    onArrangeInGrid: (columns: Int) -> Unit,
    onBringToFront: () -> Unit,
    onSendToBack: () -> Unit,
    onCreateGroup: (name: String, color: Color) -> Unit,
    onToggleVisibility: (ScadaComponent) -> Unit,
    onToggleLock: (ScadaComponent) -> Unit,
    selectedComponents: List<ScadaComponent>,
    componentGroups: List<com.tfkcolin.cebsscada.scada.ComponentGroup>
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("Align", "Distribute", "Size", "Arrange", "Groups", "Layer")

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier
            .fillMaxWidth(0.8f)
            .fillMaxHeight(0.7f),
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("Organization Tools")
                Text(
                    text = "${selectedComponents.size} component${if (selectedComponents.size != 1) "s" else ""} selected",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        text = {
            Column(modifier = Modifier.fillMaxSize()) {
                TabRow(selectedTabIndex = selectedTab) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            text = { Text(title) }
                        )
                    }
                }

                when (selectedTab) {
                    0 -> AlignmentTab(
                        onAlignLeft = onAlignLeft,
                        onAlignCenter = onAlignCenter,
                        onAlignRight = onAlignRight,
                        onAlignTop = onAlignTop,
                        onAlignMiddle = onAlignMiddle,
                        onAlignBottom = onAlignBottom,
                        enabled = selectedComponents.size > 1
                    )
                    1 -> DistributionTab(
                        onDistributeHorizontal = onDistributeHorizontal,
                        onDistributeVertical = onDistributeVertical,
                        enabled = selectedComponents.size >= 3
                    )
                    2 -> SizeTab(onMatchSizes = onMatchSizes, enabled = selectedComponents.size > 1)
                    3 -> ArrangeTab(onArrangeInGrid = onArrangeInGrid, enabled = selectedComponents.isNotEmpty())
                    4 -> GroupsTab(
                        componentGroups = componentGroups,
                        onCreateGroup = onCreateGroup,
                        selectedComponents = selectedComponents
                    )
                    5 -> LayerTab(
                        onBringToFront = onBringToFront,
                        onSendToBack = onSendToBack,
                        selectedComponents = selectedComponents,
                        onToggleVisibility = onToggleVisibility,
                        onToggleLock = onToggleLock
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

/**
 * Alignment tools tab
 */
@Composable
private fun AlignmentTab(
    onAlignLeft: () -> Unit,
    onAlignCenter: () -> Unit,
    onAlignRight: () -> Unit,
    onAlignTop: () -> Unit,
    onAlignMiddle: () -> Unit,
    onAlignBottom: () -> Unit,
    enabled: Boolean
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Horizontal Alignment",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OrganizationButton(
                    onClick = onAlignLeft,
                    icon = Icons.Default.FormatAlignLeft,
                    text = "Left",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
                OrganizationButton(
                    onClick = onAlignCenter,
                    icon = Icons.Default.FormatAlignCenter,
                    text = "Center",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
                OrganizationButton(
                    onClick = onAlignRight,
                    icon = Icons.Default.FormatAlignRight,
                    text = "Right",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        item {
            Text(
                text = "Vertical Alignment",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OrganizationButton(
                    onClick = onAlignTop,
                    icon = Icons.Default.VerticalAlignTop,
                    text = "Top",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
                OrganizationButton(
                    onClick = onAlignMiddle,
                    icon = Icons.Default.VerticalAlignCenter,
                    text = "Middle",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
                OrganizationButton(
                    onClick = onAlignBottom,
                    icon = Icons.Default.VerticalAlignBottom,
                    text = "Bottom",
                    enabled = enabled,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        if (!enabled) {
            item {
                Text(
                    text = "Select 2 or more components to use alignment tools",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Distribution tools tab
 */
@Composable
private fun DistributionTab(
    onDistributeHorizontal: () -> Unit,
    onDistributeVertical: () -> Unit,
    enabled: Boolean
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Distribution",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            OrganizationButton(
                onClick = onDistributeHorizontal,
                icon = Icons.Default.HorizontalDistribute,
                text = "Distribute Horizontally",
                description = "Evenly space components horizontally",
                enabled = enabled
            )
        }

        item {
            OrganizationButton(
                onClick = onDistributeVertical,
                icon = Icons.Default.VerticalDistribute,
                text = "Distribute Vertically",
                description = "Evenly space components vertically",
                enabled = enabled
            )
        }

        if (!enabled) {
            item {
                Text(
                    text = "Select 3 or more components to use distribution tools",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Size tools tab
 */
@Composable
private fun SizeTab(
    onMatchSizes: (matchWidth: Boolean, matchHeight: Boolean) -> Unit,
    enabled: Boolean
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Size Matching",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            OrganizationButton(
                onClick = { onMatchSizes(true, false) },
                icon = Icons.Default.SwitchLeft,
                text = "Match Width",
                description = "Make all components the same width",
                enabled = enabled
            )
        }

        item {
            OrganizationButton(
                onClick = { onMatchSizes(false, true) },
                icon = Icons.Default.Height,
                text = "Match Height",
                description = "Make all components the same height",
                enabled = enabled
            )
        }

        item {
            OrganizationButton(
                onClick = { onMatchSizes(true, true) },
                icon = Icons.Default.AspectRatio,
                text = "Match Size",
                description = "Make all components the same size",
                enabled = enabled
            )
        }

        if (!enabled) {
            item {
                Text(
                    text = "Select 2 or more components to use size tools",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Arrange tools tab
 */
@Composable
private fun ArrangeTab(
    onArrangeInGrid: (columns: Int) -> Unit,
    enabled: Boolean
) {
    var columns by remember { mutableStateOf(3) }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Grid Arrangement",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            Column {
                Text(
                    text = "Columns: $columns",
                    style = MaterialTheme.typography.bodyMedium
                )
                Slider(
                    value = columns.toFloat(),
                    onValueChange = { columns = it.toInt() },
                    valueRange = 1f..10f,
                    steps = 9,
                    enabled = enabled
                )
            }
        }

        item {
            OrganizationButton(
                onClick = { onArrangeInGrid(columns) },
                icon = Icons.Default.GridOn,
                text = "Arrange in Grid",
                description = "Arrange components in a $columns-column grid",
                enabled = enabled
            )
        }

        if (!enabled) {
            item {
                Text(
                    text = "Select components to use arrangement tools",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Groups tab
 */
@Composable
private fun GroupsTab(
    componentGroups: List<com.tfkcolin.cebsscada.scada.ComponentGroup>,
    onCreateGroup: (name: String, color: Color) -> Unit,
    selectedComponents: List<ScadaComponent>
) {
    var newGroupName by remember { mutableStateOf("") }
    var selectedColor by remember { mutableStateOf(Color.Blue) }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Component Groups",
                style = MaterialTheme.typography.titleMedium
            )
        }

        // Create new group
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Create New Group",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = newGroupName,
                        onValueChange = { newGroupName = it },
                        label = { Text("Group Name") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("Color:", style = MaterialTheme.typography.bodyMedium)
                        // Color picker would go here - simplified for now
                        Button(
                            onClick = {
                                if (newGroupName.isNotBlank()) {
                                    onCreateGroup(newGroupName, selectedColor)
                                    newGroupName = ""
                                }
                            },
                            enabled = newGroupName.isNotBlank()
                        ) {
                            Text("Create Group")
                        }
                    }
                }
            }
        }

        // Existing groups
        if (componentGroups.isNotEmpty()) {
            item {
                Text(
                    text = "Existing Groups",
                    style = MaterialTheme.typography.titleSmall
                )
            }

            items(componentGroups) { group ->
                Card(modifier = Modifier.fillMaxWidth()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .background(group.color, shape = MaterialTheme.shapes.small)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(group.name)
                        }
                        Text(
                            text = "${selectedComponents.count { it.groupId == group.id }} components",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

/**
 * Layer tools tab
 */
@Composable
private fun LayerTab(
    onBringToFront: () -> Unit,
    onSendToBack: () -> Unit,
    selectedComponents: List<ScadaComponent>,
    onToggleVisibility: (ScadaComponent) -> Unit,
    onToggleLock: (ScadaComponent) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Layer Management",
                style = MaterialTheme.typography.titleMedium
            )
        }

        item {
            OrganizationButton(
                onClick = onBringToFront,
                icon = Icons.Default.FlipToFront,
                text = "Bring to Front",
                description = "Move selected components to the top layer",
                enabled = selectedComponents.isNotEmpty()
            )
        }

        item {
            OrganizationButton(
                onClick = onSendToBack,
                icon = Icons.Default.FlipToBack,
                text = "Send to Back",
                description = "Move selected components to the bottom layer",
                enabled = selectedComponents.isNotEmpty()
            )
        }

        // Individual component controls
        if (selectedComponents.isNotEmpty()) {
            item {
                Text(
                    text = "Component Properties",
                    style = MaterialTheme.typography.titleSmall
                )
            }

            items(selectedComponents) { component ->
                Card(modifier = Modifier.fillMaxWidth()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = component.type.name.replace("_", " "),
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = component.id.take(8),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                            IconButton(
                                onClick = { onToggleVisibility(component) },
                                modifier = Modifier.size(32.dp)
                            ) {
                                Icon(
                                    if (component.isVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                                    contentDescription = if (component.isVisible) "Hide" else "Show"
                                )
                            }

                            IconButton(
                                onClick = { onToggleLock(component) },
                                modifier = Modifier.size(32.dp)
                            ) {
                                Icon(
                                    if (component.isLocked) Icons.Default.Lock else Icons.Default.LockOpen,
                                    contentDescription = if (component.isLocked) "Unlock" else "Lock"
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Reusable organization button
 */
@Composable
private fun OrganizationButton(
    onClick: () -> Unit,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    description: String? = null,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier,
        contentPadding = PaddingValues(12.dp)
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(icon, contentDescription = text)
            Spacer(modifier = Modifier.height(4.dp))
            Text(text, style = MaterialTheme.typography.bodySmall)
            description?.let {
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    it,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}