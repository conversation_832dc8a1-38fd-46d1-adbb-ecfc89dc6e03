package com.tfkcolin.cebsscada.scada.components

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentSize
import com.tfkcolin.cebsscada.scada.ComponentType
import com.tfkcolin.cebsscada.scada.DataBindingManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

/**
 * Toggle Switch SCADA Component
 * Can be used to control devices or display binary states
 */
class ToggleSwitch(
    private val dataBindingManager: DataBindingManager,
    id: String = UUID.randomUUID().toString(),
    position: ComponentPosition = ComponentPosition(),
    size: ComponentSize = ComponentSize(),
    config: ToggleSwitchConfig = ToggleSwitchConfig()
) : BaseScadaComponent<ToggleSwitchConfig>(
    id = id,
    type = ComponentType.TOGGLE_SWITCH,
    initialPosition = position,
    initialSize = size,
    initialConfig = config
) {

    private val scope = CoroutineScope(Dispatchers.IO)

    // Current switch state
    private val _isChecked: MutableState<Boolean> = mutableStateOf(getTypedConfig().initialState)
    val isChecked: Boolean
        get() = _isChecked.value

    init {
        // Listen for state updates from data binding
        val stateBinding = getTypedConfig().stateSourceBinding
        if (stateBinding != null) {
            // TODO: Register with data binding manager for state updates
            // This would be implemented when the data binding system supports reading state
        }
    }

    /**
     * Handle switch toggle
     */
    fun onToggle(checked: Boolean) {
        _isChecked.value = checked

        // Send data via communication layer
        val binding = getTypedConfig().dataBinding
        if (binding.protocol != null && binding.address.isNotEmpty()) {
            val payloadToSend = if (checked) {
                binding.payload.ifEmpty { getTypedConfig().onPayload }
            } else {
                getTypedConfig().offPayload
            }

            scope.launch {
                dataBindingManager.sendComponentData(this@ToggleSwitch, payloadToSend)
            }
        }
    }

    /**
     * Update switch state (called by data binding system)
     */
    fun updateState(newState: Boolean) {
        _isChecked.value = newState
    }

    /**
     * Update switch state from string value
     */
    fun updateStateFromString(value: String) {
        val booleanValue = when (value.lowercase()) {
            "true", "1", "on", "yes" -> true
            "false", "0", "off", "no" -> false
            else -> getTypedConfig().initialState
        }
        updateState(booleanValue)
    }
}