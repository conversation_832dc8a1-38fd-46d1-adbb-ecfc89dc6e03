package com.tfkcolin.cebsscada.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navDeepLink
import androidx.navigation.toRoute
import com.tfkcolin.cebsscada.scada.ui.ScadaHomeScreen
import com.tfkcolin.cebsscada.scada.ui.ScadaScreen
import com.tfkcolin.cebsscada.scada.ui.ScadaRuntimeScreen
import com.tfkcolin.cebsscada.ui.components.ScreenConfig
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager

/**
 * Main navigation graph for the CEBS SCADA application.
 * Implements the workspace-centric navigation flow as defined in APP_WORKFLOW.md
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    bluetoothPermissionManager: BluetoothPermissionManager,
    onScreenConfigChange: (ScreenConfig) -> Unit,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = AppDestination.WorkspaceHome,
        modifier = modifier
    ) {
        // Workspace Home - Main dashboard (Default landing screen)
        composable<AppDestination.WorkspaceHome>(
            deepLinks = listOf(
                navDeepLink<AppDestination.WorkspaceHome>(basePath = DeepLinks.WORKSPACE_HOME)
            )
        ) {
            ScadaHomeScreen(
                onCreateNewWorkspace = {
                    navController.navigate(AppDestination.WorkspaceDesign())
                },
                onEditWorkspace = { workspaceId ->
                    navController.navigate(AppDestination.WorkspaceDesign(workspaceId))
                },
                onRunWorkspace = { workspaceId ->
                    navController.navigate(AppDestination.WorkspaceRuntime(workspaceId))
                },
                onScreenConfigChange = onScreenConfigChange
            )
        }
        
        // Workspace Design - Visual workspace editor
        composable<AppDestination.WorkspaceDesign>(
            deepLinks = listOf(
                navDeepLink<AppDestination.WorkspaceDesign>(basePath = DeepLinks.WORKSPACE_DESIGN),
                navDeepLink<AppDestination.WorkspaceDesign>(basePath = DeepLinks.WORKSPACE_DESIGN_WITH_ID)
            )
        ) { backStackEntry ->
            val destination = backStackEntry.toRoute<AppDestination.WorkspaceDesign>()
            ScadaScreen(
                workspaceId = destination.workspaceId,
                onBackToHome = {
                    navController.navigate(AppDestination.WorkspaceHome) {
                        popUpTo(AppDestination.WorkspaceHome) {
                            inclusive = false
                        }
                    }
                },
                onScreenConfigChange = onScreenConfigChange
            )
        }
        
        // Workspace Runtime - Live execution environment
        composable<AppDestination.WorkspaceRuntime>(
            deepLinks = listOf(
                navDeepLink<AppDestination.WorkspaceRuntime>(basePath = DeepLinks.WORKSPACE_RUNTIME)
            )
        ) { backStackEntry ->
            val destination = backStackEntry.toRoute<AppDestination.WorkspaceRuntime>()
            ScadaRuntimeScreen(
                workspaceId = destination.workspaceId,
                onBackToHome = {
                    navController.navigate(AppDestination.WorkspaceHome) {
                        popUpTo(AppDestination.WorkspaceHome) {
                            inclusive = false
                        }
                    }
                },
                onScreenConfigChange = onScreenConfigChange
            )
        }
        
        // Settings - Protocol testing and configuration
        composable<AppDestination.Settings>(
            deepLinks = listOf(
                navDeepLink<AppDestination.Settings>(basePath = DeepLinks.SETTINGS)
            )
        ) {
            com.tfkcolin.cebsscada.ui.settings.SettingsScreen(
                permissionManager = bluetoothPermissionManager,
                onBackToHome = {
                    navController.navigate(AppDestination.WorkspaceHome) {
                        popUpTo(AppDestination.WorkspaceHome) {
                            inclusive = false
                        }
                    }
                },
                onScreenConfigChange = onScreenConfigChange
            )
        }
    }
}

/**
 * Navigation extensions for common navigation patterns
 */
object NavigationExtensions {
    
    /**
     * Navigate to workspace design with proper back stack management
     */
    fun NavHostController.navigateToWorkspaceDesign(workspaceId: Long? = null) {
        navigate(AppDestination.WorkspaceDesign(workspaceId))
    }
    
    /**
     * Navigate to workspace runtime with proper back stack management
     */
    fun NavHostController.navigateToWorkspaceRuntime(workspaceId: Long) {
        navigate(AppDestination.WorkspaceRuntime(workspaceId))
    }
    
    /**
     * Navigate to settings with proper back stack management
     */
    fun NavHostController.navigateToSettings() {
        navigate(AppDestination.Settings)
    }
    
    /**
     * Navigate back to workspace home with proper back stack cleanup
     */
    fun NavHostController.navigateBackToHome() {
        navigate(AppDestination.WorkspaceHome) {
            popUpTo(AppDestination.WorkspaceHome) {
                inclusive = false
            }
        }
    }
    
    /**
     * Handle system back press with proper navigation logic
     */
    fun NavHostController.handleBackPress(): Boolean {
        return if (currentDestination?.route != AppDestination.WorkspaceHome::class.qualifiedName) {
            navigateBackToHome()
            true
        } else {
            false
        }
    }
}
