package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.tfkcolin.cebsscada.scada.ErrorHandler

/**
 * Comprehensive error dialog with recovery actions
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ErrorDialog(
    error: ErrorHandler.ScadaError,
    onDismiss: () -> Unit,
    onAction: ((ErrorHandler.RecoveryAction) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val dialogContent = ErrorHandler.createErrorDialogContent(error)

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = dialogContent.canContinue,
            dismissOnClickOutside = dialogContent.canContinue
        ),
        icon = {
            Icon(
                imageVector = when (error.severity) {
                    ErrorHandler.ErrorSeverity.CRITICAL -> Icons.Default.Error
                    ErrorHandler.ErrorSeverity.HIGH -> Icons.Default.Error
                    ErrorHandler.ErrorSeverity.MEDIUM -> Icons.Default.Warning
                    ErrorHandler.ErrorSeverity.LOW -> Icons.Default.Info
                },
                contentDescription = error.severity.name,
                tint = when (error.severity) {
                    ErrorHandler.ErrorSeverity.CRITICAL,
                    ErrorHandler.ErrorSeverity.HIGH -> MaterialTheme.colorScheme.error
                    ErrorHandler.ErrorSeverity.MEDIUM -> MaterialTheme.colorScheme.primary
                    ErrorHandler.ErrorSeverity.LOW -> MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        },
        title = {
            Text(
                text = dialogContent.title,
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Main error message
                Text(
                    text = dialogContent.message,
                    style = MaterialTheme.typography.bodyLarge
                )

                // Technical details (if available and user wants to see)
                var showTechnicalDetails by remember { mutableStateOf(false) }

                dialogContent.technicalDetails?.let { details ->
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Technical Details",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Bold
                            )
                            IconButton(
                                onClick = { showTechnicalDetails = !showTechnicalDetails },
                                modifier = Modifier.size(24.dp)
                            ) {
                                Icon(
                                    if (showTechnicalDetails) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                    contentDescription = if (showTechnicalDetails) "Hide details" else "Show details"
                                )
                            }
                        }

                        if (showTechnicalDetails) {
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                )
                            ) {
                                Text(
                                    text = details,
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(12.dp)
                                )
                            }
                        }
                    }
                }

                // Recovery actions
                if (dialogContent.recoveryActions.isNotEmpty()) {
                    Text(
                        text = "Suggested Actions:",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )

                    LazyColumn(
                        modifier = Modifier.heightIn(max = 200.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(dialogContent.recoveryActions) { action ->
                            RecoveryActionCard(
                                action = action,
                                onActionClick = { onAction?.invoke(action) }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            if (dialogContent.canContinue) {
                TextButton(onClick = onDismiss) {
                    Text("Continue")
                }
            }
        },
        dismissButton = {
            if (!dialogContent.canContinue) {
                TextButton(onClick = onDismiss) {
                    Text("Close Application")
                }
            }
        },
        modifier = modifier
    )
}

/**
 * Recovery action card
 */
@Composable
private fun RecoveryActionCard(
    action: ErrorHandler.RecoveryAction,
    onActionClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onActionClick,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                Icons.Default.Build,
                contentDescription = "Action",
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = action.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = action.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                Icons.Default.ChevronRight,
                contentDescription = "Execute action",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * Quick error snackbar composable
 */
@Composable
fun ErrorSnackbar(
    error: ErrorHandler.ScadaError,
    onAction: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val userMessage = ErrorHandler.getUserFriendlyMessage(error)

    Snackbar(
        modifier = modifier,
        action = onAction?.let {
            {
                TextButton(onClick = it) {
                    Text("Fix")
                }
            }
        },
        containerColor = when (error.severity) {
            ErrorHandler.ErrorSeverity.CRITICAL,
            ErrorHandler.ErrorSeverity.HIGH -> MaterialTheme.colorScheme.errorContainer
            ErrorHandler.ErrorSeverity.MEDIUM -> MaterialTheme.colorScheme.primaryContainer
            ErrorHandler.ErrorSeverity.LOW -> MaterialTheme.colorScheme.surfaceVariant
        },
        contentColor = when (error.severity) {
            ErrorHandler.ErrorSeverity.CRITICAL,
            ErrorHandler.ErrorSeverity.HIGH -> MaterialTheme.colorScheme.onErrorContainer
            ErrorHandler.ErrorSeverity.MEDIUM -> MaterialTheme.colorScheme.onPrimaryContainer
            ErrorHandler.ErrorSeverity.LOW -> MaterialTheme.colorScheme.onSurfaceVariant
        }
    ) {
        Text(text = userMessage)
    }
}

/**
 * Error state composable for full-screen error display
 */
@Composable
fun ErrorState(
    error: ErrorHandler.ScadaError,
    onRetry: (() -> Unit)? = null,
    onGoBack: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val dialogContent = ErrorHandler.createErrorDialogContent(error)

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Error icon
        Icon(
            imageVector = when (error.severity) {
                ErrorHandler.ErrorSeverity.CRITICAL -> Icons.Default.Error
                ErrorHandler.ErrorSeverity.HIGH -> Icons.Default.Error
                ErrorHandler.ErrorSeverity.MEDIUM -> Icons.Default.Warning
                ErrorHandler.ErrorSeverity.LOW -> Icons.Default.Info
            },
            contentDescription = "Error",
            modifier = Modifier.size(80.dp),
            tint = when (error.severity) {
                ErrorHandler.ErrorSeverity.CRITICAL,
                ErrorHandler.ErrorSeverity.HIGH -> MaterialTheme.colorScheme.error
                ErrorHandler.ErrorSeverity.MEDIUM -> MaterialTheme.colorScheme.primary
                ErrorHandler.ErrorSeverity.LOW -> MaterialTheme.colorScheme.onSurfaceVariant
            }
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Error title
        Text(
            text = dialogContent.title,
            style = MaterialTheme.typography.headlineMedium,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Error message
        Text(
            text = dialogContent.message,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // Technical details
        dialogContent.technicalDetails?.let { details ->
            Spacer(modifier = Modifier.height(16.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Technical Details:",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = details,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Action buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            onGoBack?.let {
                OutlinedButton(onClick = it) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Go back")
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Go Back")
                }
            }

            onRetry?.let {
                Button(onClick = it) {
                    Icon(Icons.Default.Refresh, contentDescription = "Retry")
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Retry")
                }
            }
        }

        // Recovery actions
        if (dialogContent.recoveryActions.isNotEmpty()) {
            Spacer(modifier = Modifier.height(32.dp))

            Text(
                text = "Alternative Actions:",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth(0.8f)
            ) {
                dialogContent.recoveryActions.forEach { action ->
                    OutlinedButton(
                        onClick = { action.action?.invoke() },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(action.title)
                    }
                }
            }
        }
    }
}