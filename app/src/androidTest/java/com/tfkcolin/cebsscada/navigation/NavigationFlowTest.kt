package com.tfkcolin.cebsscada.navigation

import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.navigation.compose.ComposeNavigator
import androidx.navigation.testing.TestNavHostController
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject

/**
 * Integration tests for navigation flows in the CEBS SCADA application
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class NavigationFlowTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createComposeRule()

    @Inject
    lateinit var bluetoothPermissionManager: BluetoothPermissionManager

    private lateinit var navController: TestNavHostController

    @Before
    fun setup() {
        hiltRule.inject()
        
        composeTestRule.setContent {
            navController = TestNavHostController(LocalContext.current)
            navController.navigatorProvider.addNavigator(ComposeNavigator())
            
            AppNavigation(
                navController = navController,
                bluetoothPermissionManager = bluetoothPermissionManager
            )
        }
    }

    @Test
    fun testWorkspaceHomeIsDefaultDestination() {
        // Verify that workspace home is the default destination
        composeTestRule.onNodeWithText("CEBS SCADA - Industrial Control")
            .assertIsDisplayed()
    }

    @Test
    fun testNavigationToSettings() {
        // Click settings button
        composeTestRule.onNodeWithContentDescription("Settings")
            .performClick()

        // Verify navigation to settings
        composeTestRule.onNodeWithText("Settings")
            .assertIsDisplayed()
        
        // Verify protocol testing sections are displayed
        composeTestRule.onNodeWithText("Protocol Testing")
            .assertIsDisplayed()
    }

    @Test
    fun testNavigationBackFromSettings() {
        // Navigate to settings
        composeTestRule.onNodeWithContentDescription("Settings")
            .performClick()

        // Navigate back
        composeTestRule.onNodeWithContentDescription("Back")
            .performClick()

        // Verify we're back to home
        composeTestRule.onNodeWithText("CEBS SCADA - Industrial Control")
            .assertIsDisplayed()
    }

    @Test
    fun testWorkspaceCreationFlow() {
        // This test would verify the complete workspace creation flow
        // From home -> create new workspace -> design screen -> save -> back to home
        
        // Note: This would require mocking the workspace creation functionality
        // For now, we'll just verify the navigation structure exists
        composeTestRule.onNodeWithText("CEBS SCADA - Industrial Control")
            .assertIsDisplayed()
    }

    @Test
    fun testDeepLinkNavigation() {
        // Test deep link navigation to specific screens
        // This would require setting up deep link intents
        
        // For now, verify the navigation structure supports deep linking
        assert(navController.graph.findNode(AppDestination.WorkspaceHome::class.qualifiedName!!) != null)
        assert(navController.graph.findNode(AppDestination.WorkspaceDesign::class.qualifiedName!!) != null)
        assert(navController.graph.findNode(AppDestination.WorkspaceRuntime::class.qualifiedName!!) != null)
        assert(navController.graph.findNode(AppDestination.Settings::class.qualifiedName!!) != null)
    }

    @Test
    fun testBackStackManagement() {
        // Navigate through multiple screens
        composeTestRule.onNodeWithContentDescription("Settings")
            .performClick()

        // Verify back stack has entries
        assert(navController.backQueue.size > 1)

        // Navigate back
        composeTestRule.onNodeWithContentDescription("Back")
            .performClick()

        // Verify back stack is properly managed
        composeTestRule.onNodeWithText("CEBS SCADA - Industrial Control")
            .assertIsDisplayed()
    }

    @Test
    fun testNavigationStatePreservation() {
        // This test would verify that navigation state is preserved
        // across configuration changes and process death
        
        // For now, verify the basic navigation structure
        composeTestRule.onNodeWithText("CEBS SCADA - Industrial Control")
            .assertIsDisplayed()
    }
}
