package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.cebsscada.ui.components.AdaptiveContainer
import com.tfkcolin.cebsscada.ui.components.AdaptiveGrid
import com.tfkcolin.cebsscada.ui.components.AdaptiveFAB
import com.tfkcolin.cebsscada.ui.components.ScreenConfig
import com.tfkcolin.cebsscada.ui.theme.rememberResponsiveLayout
import com.tfkcolin.cebsscada.ui.theme.ResponsiveLayout
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.launch

/**
 * Home screen for SCADA workspaces - dashboard view
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScadaHomeScreen(
    viewModel: ScadaHomeViewModel = hiltViewModel(),
    onCreateNewWorkspace: () -> Unit,
    onEditWorkspace: (Long) -> Unit,
    onRunWorkspace: (Long) -> Unit,
    onScreenConfigChange: (ScreenConfig) -> Unit
) {
    val workspaces by viewModel.workspaces.collectAsState(initial = emptyList())
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    val errorMessage by viewModel.errorMessage.collectAsState(initial = null)
    var showDeleteDialog by remember { mutableStateOf<WorkspaceEntity?>(null) }
    var showExportDialog by remember { mutableStateOf<WorkspaceEntity?>(null) }
    var showImportDialog by remember { mutableStateOf(false) }

    // Get responsive layout configuration
    val layout = rememberResponsiveLayout()
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // Show error message when it changes
    LaunchedEffect(errorMessage) {
        errorMessage?.let { message ->
            scope.launch {
                snackbarHostState.showSnackbar(message)
                viewModel.clearErrorMessage()
            }
        }
    }

    // Configure the central scaffold
    LaunchedEffect(Unit) {
        onScreenConfigChange(
            ScreenConfig(
                title = "SCADA Workspaces",
                showBackButton = false,
                showSettingsButton = true,
                actions = listOf {
                    IconButton(onClick = onCreateNewWorkspace) {
                        Icon(Icons.Default.Add, contentDescription = "Create new workspace")
                    }
                },
                snackbarHostState = snackbarHostState
            )
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
            // Search bar
            OutlinedTextField(
                value = searchQuery,
                onValueChange = viewModel::updateSearchQuery,
                label = { Text("Search workspaces") },
                leadingIcon = {
                    Icon(Icons.Default.Search, contentDescription = "Search")
                },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (workspaces.isEmpty()) {
                // Empty state
                EmptyWorkspaceState(
                    onCreateNewWorkspace = onCreateNewWorkspace,
                    layout = layout
                )
            } else {
                // Adaptive workspace grid
                AdaptiveGrid(
                    items = workspaces,
                    modifier = Modifier.fillMaxSize(),
                    layout = layout,
                    minItemWidth = layout.sizing.cardMinWidth
                ) { workspace ->
                    WorkspaceCard(
                        workspace = workspace,
                        onEdit = { onEditWorkspace(workspace.id) },
                        onRun = { onRunWorkspace(workspace.id) },
                        onDelete = { showDeleteDialog = workspace },
                        onExport = { showExportDialog = workspace },
                        onImport = { showImportDialog = true },
                        layout = layout
                    )
                }
            }
        }

    // Delete confirmation dialog
    showDeleteDialog?.let { workspace ->
        AlertDialog(
            onDismissRequest = { showDeleteDialog = null },
            title = { Text("Delete Workspace") },
            text = {
                Text("Are you sure you want to delete '${workspace.name}'? This action cannot be undone.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteWorkspace(workspace.id)
                        showDeleteDialog = null
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = null }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Export workspace dialog
    showExportDialog?.let { workspace ->
        WorkspaceExportDialog(
            workspace = workspace,
            onDismiss = { showExportDialog = null }
        )
    }

    // Import workspace dialog
    if (showImportDialog) {
        WorkspaceImportDialog(
            onDismiss = { showImportDialog = false },
            onWorkspaceImported = { workspaceId ->
                showImportDialog = false
                // Could navigate to the imported workspace or show a success message
            }
        )
    }
}

/**
 * Individual workspace card
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceCard(
    workspace: WorkspaceEntity,
    onEdit: () -> Unit,
    onRun: () -> Unit,
    onDelete: () -> Unit,
    onExport: () -> Unit = {},
    onImport: () -> Unit = {},
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .widthIn(
                min = layout.sizing.cardMinWidth,
                max = layout.sizing.cardMaxWidth
            ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(layout.spacing.medium)
        ) {
            // Workspace name and description
            Text(
                text = workspace.name,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 2
            )

            if (workspace.description.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = workspace.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Metadata
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "${workspace.componentCount} components",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = formatDate(workspace.modifiedAt),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Action buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    FilledTonalButton(
                        onClick = onRun,
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Icon(
                            Icons.Default.PlayArrow,
                            contentDescription = "Run workspace",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Run", style = MaterialTheme.typography.bodySmall)
                    }

                    OutlinedButton(
                        onClick = onEdit,
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit workspace",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Edit", style = MaterialTheme.typography.bodySmall)
                    }

                    WorkspaceShareMenu(
                        workspace = workspace,
                        onExportClick = onExport,
                        onImportClick = onImport
                    )

                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete workspace",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

/**
 * Empty state when no workspaces exist
 */
@Composable
fun EmptyWorkspaceState(
    onCreateNewWorkspace: () -> Unit,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Dashboard,
            contentDescription = null,
            modifier = Modifier.size(layout.sizing.iconLarge * 2),
            tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.height(layout.spacing.medium))

        Text(
            text = "No workspaces yet",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Create your first SCADA workspace to start monitoring and controlling your devices",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 32.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = onCreateNewWorkspace,
            contentPadding = PaddingValues(horizontal = 24.dp, vertical = 12.dp)
        ) {
            Icon(Icons.Default.Add, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("Create Your First Workspace")
        }
    }
}

/**
 * Format date for display
 */
fun formatDate(timestamp: Long): String {
    val sdf = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    return sdf.format(Date(timestamp))
}