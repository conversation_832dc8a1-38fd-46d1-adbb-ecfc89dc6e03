package com.tfkcolin.cebsscada.scada.components

import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentSize
import com.tfkcolin.cebsscada.scada.ComponentType
import com.tfkcolin.cebsscada.scada.DataBindingManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

/**
 * Push Button SCADA Component
 * Sends a command when pressed
 */
class PushButton(
    private val dataBindingManager: DataBindingManager,
    id: String = UUID.randomUUID().toString(),
    position: ComponentPosition = ComponentPosition(),
    size: ComponentSize = ComponentSize(),
    config: PushButtonConfig = PushButtonConfig()
) : BaseScadaComponent<PushButtonConfig>(
    id = id,
    type = ComponentType.PUSH_BUTTON,
    initialPosition = position,
    initialSize = size,
    initialConfig = config
) {

    private val scope = CoroutineScope(Dispatchers.IO)

    /**
     * Handle button press - send data via communication layer
     */
    fun onPressed() {
        val binding = getTypedConfig().dataBinding
        if (binding.protocol != null && binding.address.isNotEmpty()) {
            // Use the configured payload or binding payload
            val payloadToSend = binding.payload.ifEmpty { "pressed" }

            scope.launch {
                dataBindingManager.sendComponentData(this@PushButton, payloadToSend)
            }
        }
    }
}