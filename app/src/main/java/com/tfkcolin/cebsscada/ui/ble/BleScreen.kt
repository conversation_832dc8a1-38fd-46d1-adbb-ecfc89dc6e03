package com.tfkcolin.cebsscada.ui.ble

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.BluetoothSearching
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.services.BleConnectionState
import com.tfkcolin.cebsscada.services.BleOperation
import com.tfkcolin.cebsscada.ui.shared.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * BLE screen with dedicated UI for Bluetooth Low Energy communication
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BleScreen(
    viewModel: BleViewModel = hiltViewModel(),
    permissionManager: BluetoothPermissionManager
) {
    val hasPermissions by viewModel.hasPermissions.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()
    val connectedDevice by viewModel.connectedDevice.collectAsState()
    val selectedTab by viewModel.selectedTab.collectAsState()

    if (!hasPermissions) {
        BluetoothPermissionHandler(
            permissionManager = permissionManager,
            onPermissionsGranted = { /* Permissions granted, UI will update */ },
            onPermissionsDenied = { /* Show permission denied screen */ }
        )
        return
    }

    LazyColumn (
        modifier = Modifier
            .fillMaxSize()
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
    ) {
        item {
            // Header with connection status
            BleHeader(
                connectionState = connectionState,
                connectedDevice = connectedDevice,
                onDisconnect = { viewModel.disconnect() }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Tab row
            val tabs = listOf("Devices", "Services", "Characteristics", "Data Monitor")
            ScrollableTabRow(
                selectedTabIndex = selectedTab,
                edgePadding = 4.dp
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { viewModel.setSelectedTab(index) },
                        text = { Text(title) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Tab content
            when (selectedTab) {
                0 -> BleDevicesTab(viewModel = viewModel)
                1 -> BleServicesTab(viewModel = viewModel)
                2 -> BleCharacteristicsTab(viewModel = viewModel)
                3 -> BleDataMonitorTab(viewModel = viewModel)
            }
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun BleHeader(
    connectionState: BleConnectionState,
    connectedDevice: android.bluetooth.BluetoothDevice?,
    onDisconnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (connectionState) {
                BleConnectionState.CONNECTED -> MaterialTheme.colorScheme.primaryContainer
                BleConnectionState.CONNECTING -> MaterialTheme.colorScheme.secondaryContainer
                BleConnectionState.ERROR -> MaterialTheme.colorScheme.errorContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Bluetooth Low Energy (BLE)",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = when (connectionState) {
                            BleConnectionState.DISCONNECTED -> "Disconnected"
                            BleConnectionState.CONNECTING -> "Connecting..."
                            BleConnectionState.CONNECTED -> "Connected to ${connectedDevice?.name ?: "Unknown"}"
                            BleConnectionState.DISCONNECTING -> "Disconnecting..."
                            BleConnectionState.ERROR -> "Connection Error"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                if (connectionState == BleConnectionState.CONNECTED) {
                    OutlinedButton(onClick = onDisconnect) {
                        Icon(Icons.Default.BluetoothDisabled, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Disconnect")
                    }
                }
            }
        }
    }
}

@Composable
fun BleDevicesTab(viewModel: BleViewModel) {
    val isScanning by viewModel.isScanning.collectAsState()
    val discoveredDevices by viewModel.discoveredDevices.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()
    val connectingDevice by viewModel.connectingDevice.collectAsState()

    Column {
        // Scan controls
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            if (isScanning) {
                Button(
                    onClick = { viewModel.stopScan() },
                    modifier = Modifier.weight(1f)
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Stop Scan")
                }
            } else {
                Button(
                    onClick = { viewModel.startScan() },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Search, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Start Scan")
                }
            }

            OutlinedButton(
                onClick = { viewModel.clearDevices() },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Clear")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Discovered devices
        Text(
            text = "Discovered BLE Devices",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        if (discoveredDevices.isEmpty() && !isScanning) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.BluetoothSearching,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "No BLE devices found",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Start scanning to discover Bluetooth Low Energy devices",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                discoveredDevices.forEach { device ->
                    BleDeviceCard(
                        device = device,
                        connectionState = connectionState,
                        connectingDevice = connectingDevice,
                        onConnect = { viewModel.connect(device) }
                    )
                }
            }
        }
    }
}

@Composable
fun BleDeviceCard(
    device: ScannedBluetoothDevice,
    connectionState: BleConnectionState,
    connectingDevice: android.bluetooth.BluetoothDevice?,
    onConnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.BluetoothSearching,
                        contentDescription = "BLE",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.secondary
                    )
                    Text(
                        text = "BLE",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.secondary
                    )
                    
                    device.rssi?.let { rssi ->
                        Text(
                            text = "RSSI: ${rssi}dBm",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }

            val isConnecting = connectionState == BleConnectionState.CONNECTING &&
                connectingDevice?.address == device.address

            Button(
                onClick = onConnect,
                enabled = connectionState == BleConnectionState.DISCONNECTED
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Icon(Icons.AutoMirrored.Filled.BluetoothSearching, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Connect")
                }
            }
        }
    }
}

@Composable
fun BleServicesTab(viewModel: BleViewModel) {
    val gattServices by viewModel.gattServices.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()

    Column {
        if (connectionState != BleConnectionState.CONNECTED) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.DeviceHub,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Not Connected",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Connect to a BLE device to explore its services",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else if (gattServices.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Discovering Services...",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        } else {
            Text(
                text = "GATT Services (${gattServices.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                gattServices.forEach { service ->
                    BleServiceCard(
                        service = service,
                        onServiceSelected = { viewModel.setSelectedService(service) }
                    )
                }
            }
        }
    }
}

@Composable
fun BleServiceCard(
    service: android.bluetooth.BluetoothGattService,
    onServiceSelected: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = getServiceName(service.uuid.toString()),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = service.uuid.toString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "${service.characteristics.size} characteristics",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                OutlinedButton(onClick = onServiceSelected) {
                    Text("Explore")
                }
            }
        }
    }
}

@Composable
fun BleCharacteristicsTab(viewModel: BleViewModel) {
    val selectedService by viewModel.selectedService.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()

    Column {
        if (connectionState != BleConnectionState.CONNECTED) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.DeviceHub,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Not Connected",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Connect to a BLE device to interact with characteristics",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else if (selectedService == null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.List,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "No Service Selected",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Select a service from the Services tab to view its characteristics",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else {
            // Service header
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = getServiceName(selectedService!!.uuid.toString()),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = selectedService!!.uuid.toString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Characteristics (${selectedService!!.characteristics.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                selectedService!!.characteristics.forEach { characteristic ->
                    BleCharacteristicCard(
                        characteristic = characteristic,
                        serviceUuid = selectedService!!.uuid.toString(),
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}

@Composable
fun BleCharacteristicCard(
    characteristic: android.bluetooth.BluetoothGattCharacteristic,
    serviceUuid: String,
    viewModel: BleViewModel
) {
    var writeText by remember { mutableStateOf("") }
    var showWriteDialog by remember { mutableStateOf(false) }

    val properties = viewModel.getCharacteristicProperties(characteristic.properties)
    val canRead = viewModel.canRead(characteristic.properties)
    val canWrite = viewModel.canWrite(characteristic.properties)
    val canNotify = viewModel.canNotify(characteristic.properties)

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = getCharacteristicName(characteristic.uuid.toString()),
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = characteristic.uuid.toString(),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Properties
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                properties.forEach { property ->
                    AssistChip(
                        onClick = { },
                        label = { Text(property, style = MaterialTheme.typography.labelSmall) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Action buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                if (canRead) {
                    OutlinedButton(
                        onClick = {
                            viewModel.readCharacteristic(serviceUuid, characteristic.uuid.toString())
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Download, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Read")
                    }
                }

                if (canWrite) {
                    Button(
                        onClick = { showWriteDialog = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Upload, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Write")
                    }
                }

                if (canNotify) {
                    OutlinedButton(
                        onClick = {
                            viewModel.enableNotifications(serviceUuid, characteristic.uuid.toString())
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Notifications, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Notify")
                    }
                }
            }
        }
    }

    // Write dialog
    if (showWriteDialog) {
        AlertDialog(
            onDismissRequest = { showWriteDialog = false },
            title = { Text("Write to Characteristic") },
            text = {
                Column {
                    Text("Enter data to write:")
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = writeText,
                        onValueChange = { writeText = it },
                        label = { Text("Data") },
                        placeholder = { Text("Enter text or hex data...") }
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        if (writeText.isNotBlank()) {
                            viewModel.writeTextToCharacteristic(
                                serviceUuid,
                                characteristic.uuid.toString(),
                                writeText
                            )
                            writeText = ""
                            showWriteDialog = false
                        }
                    }
                ) {
                    Text("Write")
                }
            },
            dismissButton = {
                OutlinedButton(onClick = { showWriteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
fun BleDataMonitorTab(viewModel: BleViewModel) {
    val messages by viewModel.messages.collectAsState()
    val connectionStats = viewModel.getConnectionStats()

    Column {
        // Connection statistics
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "BLE Statistics",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(12.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Text(
                            text = "Operations",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = connectionStats.operationsPerformed.toString(),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Column {
                        Text(
                            text = "Notifications",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = connectionStats.notificationsReceived.toString(),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Column {
                        Text(
                            text = "Services",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = connectionStats.servicesDiscovered.toString(),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Message controls
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Data History (${messages.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            OutlinedButton(
                onClick = { viewModel.clearMessages() },
                enabled = messages.isNotEmpty()
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Clear")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Messages list
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            messages.reversed().forEach { message ->
                BleMessageCard(message = message)
            }
        }
    }
}

@Composable
fun BleMessageCard(message: BleMessage) {
    val dateFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    val timeString = dateFormat.format(Date(message.timestamp))

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (message.operation) {
                BleOperation.READ -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                BleOperation.WRITE -> MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                BleOperation.NOTIFICATION -> MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = message.operation.name,
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold,
                    color = when (message.operation) {
                        BleOperation.READ -> MaterialTheme.colorScheme.primary
                        BleOperation.WRITE -> MaterialTheme.colorScheme.secondary
                        BleOperation.NOTIFICATION -> MaterialTheme.colorScheme.tertiary
                    }
                )

                Text(
                    text = timeString,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Text(
                text = "Service: ${getServiceName(message.serviceUuid)}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Text(
                text = "Characteristic: ${getCharacteristicName(message.characteristicUuid)}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Text(
                text = message.content,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

// Helper functions for BLE service and characteristic names
fun getServiceName(uuid: String): String {
    return when (uuid.uppercase()) {
        "00001800-0000-1000-8000-00805F9B34FB" -> "Generic Access"
        "00001801-0000-1000-8000-00805F9B34FB" -> "Generic Attribute"
        "0000180A-0000-1000-8000-00805F9B34FB" -> "Device Information"
        "0000180F-0000-1000-8000-00805F9B34FB" -> "Battery Service"
        "00001812-0000-1000-8000-00805F9B34FB" -> "Human Interface Device"
        "0000181A-0000-1000-8000-00805F9B34FB" -> "Environmental Sensing"
        "0000FFE0-0000-1000-8000-00805F9B34FB" -> "HM-10 Service"
        else -> "Unknown Service"
    }
}

fun getCharacteristicName(uuid: String): String {
    return when (uuid.uppercase()) {
        "00002A00-0000-1000-8000-00805F9B34FB" -> "Device Name"
        "00002A01-0000-1000-8000-00805F9B34FB" -> "Appearance"
        "00002A04-0000-1000-8000-00805F9B34FB" -> "Peripheral Preferred Connection Parameters"
        "00002A19-0000-1000-8000-00805F9B34FB" -> "Battery Level"
        "00002A29-0000-1000-8000-00805F9B34FB" -> "Manufacturer Name String"
        "00002A24-0000-1000-8000-00805F9B34FB" -> "Model Number String"
        "00002A25-0000-1000-8000-00805F9B34FB" -> "Serial Number String"
        "00002A27-0000-1000-8000-00805F9B34FB" -> "Hardware Revision String"
        "00002A26-0000-1000-8000-00805F9B34FB" -> "Firmware Revision String"
        "00002A28-0000-1000-8000-00805F9B34FB" -> "Software Revision String"
        "0000FFE1-0000-1000-8000-00805F9B34FB" -> "HM-10 Data"
        else -> "Unknown Characteristic"
    }
}
