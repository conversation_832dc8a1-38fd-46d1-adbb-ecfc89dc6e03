package com.tfkcolin.cebsscada.scada

import android.util.Log
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages data binding between SCADA components and communication protocols
 */
@Singleton
class DataBindingManager @Inject constructor(
    private val communicationManager: CommunicationManager
) {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val registeredComponents = mutableMapOf<String, ScadaComponent>()

    companion object {
        private const val TAG = "DataBindingManager"
    }

    init {
        // Start listening to communication messages
        scope.launch {
            communicationManager.allMessages.collect { message ->
                handleIncomingMessage(message)
            }
        }
    }

    /**
     * Register a component for data binding updates
     */
    fun registerComponent(component: ScadaComponent) {
        registeredComponents[component.id] = component
        Log.d(TAG, "Registered component ${component.id} for data binding")
    }

    /**
     * Unregister a component from data binding
     */
    fun unregisterComponent(componentId: String) {
        registeredComponents.remove(componentId)
        Log.d(TAG, "Unregistered component $componentId from data binding")
    }

    /**
     * Handle incoming messages and route them to appropriate components
     */
    private suspend fun handleIncomingMessage(message: CommunicationMessage) {
        val matchingComponents = findComponentsForMessage(message)

        for (component in matchingComponents) {
            updateComponentWithMessage(component, message)
        }
    }

    /**
     * Find components that should receive this message based on their data binding
     */
    private fun findComponentsForMessage(message: CommunicationMessage): List<ScadaComponent> {
        return registeredComponents.values.filter { component ->
            // Each component config has its own dataBinding property
            val binding = when (component.config) {
                is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).dataBinding
                is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).dataBinding
                // Add other config types as needed
                else -> null
            } ?: return@filter false

            // Check if protocol and topic match (MQTT topic or similar)
            binding.protocol == message.protocol &&
            binding.address == message.topic
        }
    }

    /**
     * Update a component with data from a message
     */
    private fun updateComponentWithMessage(component: ScadaComponent, message: CommunicationMessage) {
        when (component.type) {
            ComponentType.VALUE_DISPLAY -> {
                updateValueDisplay(component as com.tfkcolin.cebsscada.scada.components.ValueDisplay, message)
            }
            // TODO: Implement other component types when they are created
            ComponentType.INDICATOR_LIGHT,
            ComponentType.GAUGE,
            ComponentType.LINE_CHART -> {
                Log.d(TAG, "Component type ${component.type} not yet implemented for data binding")
            }
            else -> {
                Log.d(TAG, "Component type ${component.type} not handled for data binding")
            }
        }
    }

    /**
     * Update Value Display component
     */
    private fun updateValueDisplay(component: com.tfkcolin.cebsscada.scada.components.ValueDisplay, message: CommunicationMessage) {
        try {
            val binding = component.getTypedConfig().dataBinding
            var value = message.contentAsString

            // Handle JSON path extraction if specified
            if (binding.jsonPath.isNotEmpty()) {
                // TODO: Implement JSON path extraction
                // For now, just use the raw content
            }

            // Convert to appropriate type based on configuration
            when (binding.dataFormat) {
                DataFormat.INTEGER -> {
                    val intValue = value.toIntOrNull() ?: 0
                    component.updateValue(intValue)
                }
                DataFormat.FLOAT -> {
                    val floatValue = value.toFloatOrNull() ?: 0f
                    component.updateValue(floatValue)
                }
                DataFormat.BOOLEAN -> {
                    val boolValue = value.toBooleanStrictOrNull() ?: false
                    component.updateValue(if (boolValue) 1 else 0)
                }
                else -> {
                    component.updateValue(value)
                }
            }

            Log.d(TAG, "Updated ValueDisplay ${component.id} with value: $value")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating ValueDisplay ${component.id}: ${e.message}")
        }
    }

    // TODO: Implement updateIndicatorLight when IndicatorLight component is created
    // TODO: Implement updateGauge when Gauge component is created

    /**
     * Send data from a component through the communication layer
     */
    suspend fun sendComponentData(component: ScadaComponent, data: String) {
        val binding = when (component.config) {
            is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).dataBinding
            is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).dataBinding
            // Add other config types as needed
            else -> null
        } ?: return

        if (binding.protocol != null && binding.address.isNotEmpty()) {
            val result = communicationManager.sendText(
                protocol = binding.protocol!!,
                text = data,
                topic = binding.address
            )

            if (result.success) {
                Log.d(TAG, "Component ${component.id} sent data: $data")
            } else {
                Log.e(TAG, "Component ${component.id} failed to send data: ${result.error}")
            }
        }
    }

    /**
     * Evaluate a condition string against a value
     * Simple condition evaluation (can be extended)
     */
    private fun evaluateCondition(condition: String, value: String): Boolean {
        return try {
            when {
                condition.contains("==") -> {
                    val expected = condition.substringAfter("==").trim()
                    value == expected
                }
                condition.contains(">") -> {
                    val threshold = condition.substringAfter(">").trim().toFloatOrNull() ?: 0f
                    val numValue = value.toFloatOrNull() ?: 0f
                    numValue > threshold
                }
                condition.contains("<") -> {
                    val threshold = condition.substringAfter("<").trim().toFloatOrNull() ?: 0f
                    val numValue = value.toFloatOrNull() ?: 0f
                    numValue < threshold
                }
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating condition '$condition' with value '$value': ${e.message}")
            false
        }
    }

    /**
     * Get all registered components
     */
    fun getRegisteredComponents(): List<ScadaComponent> = registeredComponents.values.toList()

    /**
     * Clear all registered components
     */
    fun clearAllRegistrations() {
        registeredComponents.clear()
        Log.d(TAG, "Cleared all component registrations")
    }
}