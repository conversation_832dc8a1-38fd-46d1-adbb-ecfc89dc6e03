package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat

/**
 * Centralized utility for Bluetooth permission management
 */
object BluetoothPermissionManager {

    /**
     * Check if the app has the required Bluetooth connect permission for the current API level
     */
    fun hasBluetoothConnectPermission(context: Context): <PERSON><PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+: BLUETOOTH_CONNECT required
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 6-11: BLUETOOTH permission required
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Check if the app has the required Bluetooth scan permission for the current API level
     */
    fun hasBluetoothScanPermission(context: Context): <PERSON>ole<PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+: BLUETOOTH_SCAN required
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 6-11: ACCESS_FINE_LOCATION required for scanning
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Check if the app has location permissions (required for Bluetooth scanning on older Android versions)
     */
    fun hasLocationPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED ||
               ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Get the required Bluetooth connect permission name for the current API level
     */
    fun getRequiredBluetoothConnectPermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            "BLUETOOTH_CONNECT"
        } else {
            "BLUETOOTH"
        }
    }

    /**
     * Get the required Bluetooth scan permission name for the current API level
     */
    fun getRequiredBluetoothScanPermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            "BLUETOOTH_SCAN"
        } else {
            "ACCESS_FINE_LOCATION"
        }
    }

    /**
     * Check if all required Bluetooth permissions are granted
     */
    fun hasAllBluetoothPermissions(context: Context): Boolean {
        return hasBluetoothConnectPermission(context) &&
               hasBluetoothScanPermission(context) &&
               (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S || hasLocationPermission(context))
    }

    /**
     * Get list of missing Bluetooth permissions
     */
    fun getMissingBluetoothPermissions(context: Context): List<String> {
        val missing = mutableListOf<String>()

        if (!hasBluetoothConnectPermission(context)) {
            missing.add(getRequiredBluetoothConnectPermission())
        }

        if (!hasBluetoothScanPermission(context)) {
            missing.add(getRequiredBluetoothScanPermission())
        }

        // Location permission only required for older Android versions
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S && !hasLocationPermission(context)) {
            missing.add("ACCESS_FINE_LOCATION")
        }

        return missing
    }
}