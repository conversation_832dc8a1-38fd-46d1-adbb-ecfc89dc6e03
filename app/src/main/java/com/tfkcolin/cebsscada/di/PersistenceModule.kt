package com.tfkcolin.cebsscada.di

import android.content.Context
import androidx.room.Room
import com.tfkcolin.cebsscada.scada.persistence.ScadaDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing persistence-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object PersistenceModule {

    @Provides
    @Singleton
    fun provideScadaDatabase(
        @ApplicationContext context: Context
    ): ScadaDatabase {
        return Room.databaseBuilder(
            context,
            ScadaDatabase::class.java,
            ScadaDatabase.DATABASE_NAME
        ).build()
    }
}