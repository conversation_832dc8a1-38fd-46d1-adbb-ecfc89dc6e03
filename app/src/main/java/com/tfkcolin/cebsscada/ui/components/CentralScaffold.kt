package com.tfkcolin.cebsscada.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.tfkcolin.cebsscada.navigation.NavigationState
import com.tfkcolin.cebsscada.ui.theme.ResponsiveLayout
import com.tfkcolin.cebsscada.ui.theme.rememberResponsiveLayout

/**
 * Configuration for individual screens to customize the central scaffold
 */
data class ScreenConfig(
    val title: String,
    val showBackButton: Boolean = true,
    val showSettingsButton: Boolean = false,
    val navigationIcon: (@Composable () -> Unit)? = null,
    val actions: List<@Composable () -> Unit> = emptyList(),
    val floatingActionButton: (@Composable () -> Unit)? = null,
    val snackbarHostState: SnackbarHostState? = null,
    val bottomBar: (@Composable () -> Unit)? = null
)

/**
 * Central Scaffold component that provides consistent UI structure across all screens.
 * This replaces all individual Scaffold implementations to ensure design consistency.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CentralScaffold(
    navigationState: NavigationState,
    screenConfig: ScreenConfig,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    content: @Composable (PaddingValues) -> Unit
) {
    AdaptiveNavigationDrawer(
        navigationState = navigationState,
        layout = layout,
        modifier = modifier
    ) {
        Row {
            // Navigation rail for medium screens
            AdaptiveNavigationRail(
                navigationState = navigationState,
                layout = layout
            )
            
            // Main content with scaffold
            Scaffold(
                topBar = {
                    CentralTopAppBar(
                        screenConfig = screenConfig,
                        navigationState = navigationState,
                        layout = layout
                    )
                },
                bottomBar = {
                    screenConfig.bottomBar?.invoke() ?: run {
                        AdaptiveBottomNavigation(
                            navigationState = navigationState,
                            layout = layout
                        )
                    }
                },
                floatingActionButton = {
                    screenConfig.floatingActionButton?.invoke()
                },
                snackbarHost = {
                    SnackbarHost(
                        hostState = screenConfig.snackbarHostState ?: remember { SnackbarHostState() }
                    )
                },
                modifier = Modifier.weight(1f)
            ) { innerPadding ->
                content(innerPadding)
            }
        }
    }
}

/**
 * Centralized TopAppBar that handles all screen-specific requirements
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CentralTopAppBar(
    screenConfig: ScreenConfig,
    navigationState: NavigationState,
    layout: ResponsiveLayout
) {
    TopAppBar(
        title = {
            Text(
                text = screenConfig.title,
                style = MaterialTheme.typography.titleLarge
            )
        },
        navigationIcon = {
            when {
                // Custom navigation icon takes precedence
                screenConfig.navigationIcon != null -> {
                    screenConfig.navigationIcon.invoke()
                }
                // Show back button if screen requests it and navigation allows it
                screenConfig.showBackButton && navigationState.canNavigateBack -> {
                    IconButton(onClick = { navigationState.navigateUp() }) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            }
        },
        actions = {
            // Screen-specific actions
            screenConfig.actions.forEach { action ->
                action()
            }
            
            // Global settings button (if requested by screen)
            if (screenConfig.showSettingsButton) {
                IconButton(onClick = { navigationState.navigateToSettings() }) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Settings"
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface
        )
    )
}

/**
 * Hook for screens to configure the central scaffold
 */
@Composable
fun rememberScreenConfig(
    title: String,
    showBackButton: Boolean = true,
    showSettingsButton: Boolean = false,
    navigationIcon: (@Composable () -> Unit)? = null,
    actions: List<@Composable () -> Unit> = emptyList(),
    floatingActionButton: (@Composable () -> Unit)? = null,
    snackbarHostState: SnackbarHostState? = null,
    bottomBar: (@Composable () -> Unit)? = null
): ScreenConfig {
    return remember(
        title,
        showBackButton,
        showSettingsButton,
        navigationIcon,
        actions,
        floatingActionButton,
        snackbarHostState,
        bottomBar
    ) {
        ScreenConfig(
            title = title,
            showBackButton = showBackButton,
            showSettingsButton = showSettingsButton,
            navigationIcon = navigationIcon,
            actions = actions,
            floatingActionButton = floatingActionButton,
            snackbarHostState = snackbarHostState,
            bottomBar = bottomBar
        )
    }
}

/**
 * Composable function to update screen configuration dynamically
 */
@Composable
fun ScreenConfigEffect(
    screenConfig: ScreenConfig,
    onConfigChange: (ScreenConfig) -> Unit
) {
    LaunchedEffect(screenConfig) {
        onConfigChange(screenConfig)
    }
}
