package com.tfkcolin.cebsscada.communication.ui

import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle

@Composable
fun BleControlScreen(
    viewModel: CommunicationViewModel
) {
    val services by viewModel.bleServices.collectAsStateWithLifecycle()

    if (services.isEmpty()) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = androidx.compose.ui.Alignment.Center) {
            CircularProgressIndicator()
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(services) { service ->
                ServiceItem(service = service, viewModel = viewModel)
            }
        }
    }
}

@Composable
fun ServiceItem(
    service: BluetoothGattService,
    viewModel: CommunicationViewModel
) {
    var expanded by remember { mutableStateOf(false) }

    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(text = "Service: ${service.uuid}", style = MaterialTheme.typography.titleSmall)
            Button(onClick = { expanded = !expanded }) {
                Text(if (expanded) "Hide" else "Show Characteristics")
            }
            if (expanded) {
                service.characteristics.forEach { characteristic ->
                    CharacteristicItem(characteristic = characteristic, viewModel = viewModel)
                }
            }
        }
    }
}

@Composable
fun CharacteristicItem(
    characteristic: BluetoothGattCharacteristic,
    viewModel: CommunicationViewModel
) {
    val messages by viewModel.messages.collectAsStateWithLifecycle()
    val lastValue = messages.lastOrNull { it.metadata["characteristicUuid"] == characteristic.uuid.toString() }?.contentAsString ?: ""

    Column(modifier = Modifier.padding(start = 16.dp, top = 8.dp)) {
        Text(text = "Characteristic: ${characteristic.uuid}", style = MaterialTheme.typography.bodyMedium)
        Text(text = "Properties: ${getCharacteristicProperties(characteristic)}", style = MaterialTheme.typography.bodySmall)
        Text(text = "Value: $lastValue", style = MaterialTheme.typography.bodySmall)

        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) {
                Button(onClick = { viewModel.readCharacteristic(characteristic.service.uuid.toString(), characteristic.uuid.toString()) }) {
                    Text("Read")
                }
            }
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) {
                Button(onClick = { 
                    // For simplicity, writing a test value. A real app would have a dialog.
                    val testValue = "hello".toByteArray()
                    viewModel.writeCharacteristic(characteristic.service.uuid.toString(), characteristic.uuid.toString(), testValue)
                }) {
                    Text("Write")
                }
            }
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) {
                var isChecked by remember { mutableStateOf(false) }
                Row(verticalAlignment = androidx.compose.ui.Alignment.CenterVertically) {
                    Text("Notify")
                    Switch(
                        checked = isChecked,
                        onCheckedChange = {
                            isChecked = it
                            viewModel.setNotifications(characteristic.service.uuid.toString(), characteristic.uuid.toString(), it)
                        }
                    )
                }
            }
        }
    }
}

fun getCharacteristicProperties(characteristic: BluetoothGattCharacteristic): String {
    val properties = mutableListOf<String>()
    if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) properties.add("READ")
    if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) properties.add("WRITE")
    if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) properties.add("WRITE_NO_RESPONSE")
    if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) properties.add("NOTIFY")
    if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) properties.add("INDICATE")
    return properties.joinToString(", ")
}