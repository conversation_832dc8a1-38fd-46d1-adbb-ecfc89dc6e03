package com.tfkcolin.cebsscada.scada

import android.content.Context
import android.net.Uri
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity
import com.tfkcolin.cebsscada.scada.persistence.ComponentEntity
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceWithComponents
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceRepository
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for exporting and importing SCADA workspaces
 */
@Singleton
class WorkspaceExporter @Inject constructor(
    @ApplicationContext private val context: Context,
    private val workspaceRepository: WorkspaceRepository
) {
    private val json = Json {
        prettyPrint = true
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    /**
     * Export workspace data structure for JSON serialization
     */
    @Serializable
    data class ExportableWorkspace(
        val version: String = "1.0",
        val exportDate: Long = System.currentTimeMillis(),
        val workspace: ExportableWorkspaceData,
        val components: List<ExportableComponentData>
    )

    @Serializable
    data class ExportableWorkspaceData(
        val name: String,
        val description: String,
        val createdAt: Long,
        val modifiedAt: Long,
        val componentCount: Int
    )

    @Serializable
    data class ExportableComponentData(
        val componentId: String,
        val type: ComponentType,
        val positionX: Float,
        val positionY: Float,
        val sizeWidth: Float,
        val sizeHeight: Float,
        val configJson: String
    )

    /**
     * Export a workspace to JSON string
     */
    suspend fun exportWorkspaceToJson(workspaceId: Long): WorkspaceResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val workspaceResult = workspaceRepository.loadWorkspace(workspaceId)
                when (workspaceResult) {
                    is WorkspaceResult.Success -> {
                        val workspaceWithComponents = workspaceResult.data

                        val exportableComponents = workspaceWithComponents.components.map { component ->
                            ExportableComponentData(
                                componentId = component.componentId,
                                type = component.type,
                                positionX = component.positionX,
                                positionY = component.positionY,
                                sizeWidth = component.sizeWidth,
                                sizeHeight = component.sizeHeight,
                                configJson = component.configJson
                            )
                        }

                        val exportableWorkspace = ExportableWorkspace(
                            workspace = ExportableWorkspaceData(
                                name = workspaceWithComponents.workspace.name,
                                description = workspaceWithComponents.workspace.description,
                                createdAt = workspaceWithComponents.workspace.createdAt,
                                modifiedAt = workspaceWithComponents.workspace.modifiedAt,
                                componentCount = workspaceWithComponents.workspace.componentCount
                            ),
                            components = exportableComponents
                        )

                        val jsonString = json.encodeToString(
                            ExportableWorkspace.serializer(),
                            exportableWorkspace
                        )

                        WorkspaceResult.Success(jsonString)
                    }
                    is WorkspaceResult.Error -> workspaceResult
                }
            } catch (e: Exception) {
                WorkspaceResult.Error("Failed to export workspace: ${e.message}", e)
            }
        }
    }

    /**
     * Export workspace to a file
     */
    suspend fun exportWorkspaceToFile(workspaceId: Long, fileUri: Uri): WorkspaceResult<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val jsonResult = exportWorkspaceToJson(workspaceId)
                when (jsonResult) {
                    is WorkspaceResult.Success -> {
                        context.contentResolver.openFileDescriptor(fileUri, "w")?.use { pfd ->
                            FileOutputStream(pfd.fileDescriptor).use { outputStream ->
                                outputStream.write(jsonResult.data.toByteArray(Charsets.UTF_8))
                            }
                        }
                        WorkspaceResult.Success(Unit)
                    }
                    is WorkspaceResult.Error -> jsonResult
                }
            } catch (e: Exception) {
                WorkspaceResult.Error("Failed to export workspace to file: ${e.message}", e)
            }
        }
    }

    /**
     * Import workspace from JSON string
     */
    suspend fun importWorkspaceFromJson(
        jsonString: String,
        newName: String? = null
    ): WorkspaceResult<Long> {
        return withContext(Dispatchers.IO) {
            try {
                val exportableWorkspace = json.decodeFromString(
                    ExportableWorkspace.serializer(),
                    jsonString
                )

                // Validate version compatibility
                if (exportableWorkspace.version != "1.0") {
                    return@withContext WorkspaceResult.Error("Unsupported workspace version: ${exportableWorkspace.version}")
                }

                // Create component entities
                val componentEntities = exportableWorkspace.components.map { component ->
                    ComponentEntity(
                        workspaceId = 0, // Will be set after workspace creation
                        componentId = component.componentId,
                        type = component.type,
                        positionX = component.positionX,
                        positionY = component.positionY,
                        sizeWidth = component.sizeWidth,
                        sizeHeight = component.sizeHeight,
                        configJson = component.configJson
                    )
                }

                // Create workspace entity
                val workspaceName = newName ?: generateUniqueName(exportableWorkspace.workspace.name)
                val workspaceEntity = WorkspaceEntity(
                    name = workspaceName,
                    description = exportableWorkspace.workspace.description,
                    componentCount = exportableWorkspace.components.size
                )

                // Save workspace and components
                val saveResult = workspaceRepository.saveWorkspace(
                    name = workspaceEntity.name,
                    description = workspaceEntity.description,
                    components = emptyList() // We'll add components separately
                )

                when (saveResult) {
                    is WorkspaceResult.Success -> {
                        val workspaceId = saveResult.data

                        // Update component entities with correct workspace ID
                        val updatedComponents = componentEntities.map { it.copy(workspaceId = workspaceId) }

                        // Save components directly to database
                        workspaceRepository.componentDao.insertComponents(updatedComponents)

                        WorkspaceResult.Success(workspaceId)
                    }
                    is WorkspaceResult.Error -> saveResult
                }
            } catch (e: Exception) {
                WorkspaceResult.Error("Failed to import workspace: ${e.message}", e)
            }
        }
    }

    /**
     * Import workspace from file
     */
    suspend fun importWorkspaceFromFile(
        fileUri: Uri,
        newName: String? = null
    ): WorkspaceResult<Long> {
        return withContext(Dispatchers.IO) {
            try {
                val jsonString = context.contentResolver.openInputStream(fileUri)?.use { inputStream ->
                    inputStream.readBytes().toString(Charsets.UTF_8)
                } ?: return@withContext WorkspaceResult.Error("Failed to read file")

                importWorkspaceFromJson(jsonString, newName)
            } catch (e: Exception) {
                WorkspaceResult.Error("Failed to import workspace from file: ${e.message}", e)
            }
        }
    }

    /**
     * Generate a unique workspace name if the original name already exists
     */
    private suspend fun generateUniqueName(baseName: String): String {
        val existingWorkspaces = workspaceRepository.getAllWorkspaces().first()
        val existingNames = existingWorkspaces.map { it.name }.toSet()

        if (baseName !in existingNames) {
            return baseName
        }

        var counter = 1
        var uniqueName: String
        do {
            uniqueName = "${baseName} (${counter})"
            counter++
        } while (uniqueName in existingNames)

        return uniqueName
    }

    /**
     * Validate workspace JSON structure
     */
    fun validateWorkspaceJson(jsonString: String): WorkspaceResult<ExportableWorkspace> {
        return try {
            val workspace = json.decodeFromString(
                ExportableWorkspace.serializer(),
                jsonString
            )
            WorkspaceResult.Success(workspace)
        } catch (e: Exception) {
            WorkspaceResult.Error("Invalid workspace JSON: ${e.message}", e)
        }
    }

    /**
     * Get suggested filename for workspace export
     */
    fun getSuggestedFilename(workspaceName: String): String {
        val sanitizedName = workspaceName.replace(Regex("[^a-zA-Z0-9\\s-]"), "_")
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            .format(Date())
        return "${sanitizedName}_${timestamp}.scada"
    }

    /**
     * Check if a file has the correct extension
     */
    fun isValidWorkspaceFile(filename: String): Boolean {
        return filename.endsWith(".scada", ignoreCase = true) ||
               filename.endsWith(".json", ignoreCase = true)
    }
}