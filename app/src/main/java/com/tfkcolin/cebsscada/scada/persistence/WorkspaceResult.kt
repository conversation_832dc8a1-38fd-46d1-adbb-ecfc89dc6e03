package com.tfkcolin.cebsscada.scada.persistence

/**
 * Result wrapper for workspace operations
 */
sealed class WorkspaceResult<out T> {
    data class Success<T>(val data: T) : WorkspaceResult<T>()
    data class Error(val message: String, val cause: Throwable? = null) : WorkspaceResult<Nothing>()

    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error

    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }

    fun getOrThrow(): T = when (this) {
        is Success -> data
        is Error -> throw RuntimeException(message, cause)
    }

    fun errorOrNull(): String? = when (this) {
        is Success -> null
        is Error -> message
    }
}

/**
 * Result wrapper for operations that don't return data
 */
sealed class WorkspaceOperationResult {
    object Success : WorkspaceOperationResult()
    data class Error(val message: String, val cause: Throwable? = null) : WorkspaceOperationResult()

    val isSuccess: Boolean get() = this is Success
    val isError: <PERSON><PERSON><PERSON> get() = this is Error

    fun errorOrNull(): String? = when (this) {
        is Success -> null
        is Error -> message
    }
}