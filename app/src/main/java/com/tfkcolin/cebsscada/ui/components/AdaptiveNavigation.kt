package com.tfkcolin.cebsscada.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.navigation.AppDestination
import com.tfkcolin.cebsscada.navigation.NavigationState
import com.tfkcolin.cebsscada.ui.theme.*

/**
 * Adaptive navigation components that change based on screen size
 */

/**
 * Navigation destinations for the app
 * Settings is handled separately in top app bar to avoid redundancy
 */
data class NavigationItem(
    val destination: AppDestination,
    val icon: ImageVector,
    val label: String,
    val contentDescription: String
)

// Main navigation items for bottom nav and rail (workspace-focused)
val mainNavigationItems = listOf(
    NavigationItem(
        destination = AppDestination.WorkspaceHome,
        icon = Icons.Default.Home,
        label = "Home",
        contentDescription = "Workspace Home"
    )
)

// Full navigation items for drawer (includes settings for expanded screens)
val drawerNavigationItems = listOf(
    NavigationItem(
        destination = AppDestination.WorkspaceHome,
        icon = Icons.Default.Home,
        label = "Home",
        contentDescription = "Workspace Home"
    ),
    NavigationItem(
        destination = AppDestination.Settings,
        icon = Icons.Default.Settings,
        label = "Settings",
        contentDescription = "Settings"
    )
)

/**
 * Adaptive bottom navigation for compact screens
 * Only shows main workspace navigation, Settings is in top app bar
 */
@Composable
fun AdaptiveBottomNavigation(
    navigationState: NavigationState,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    if (layout.shouldUseBottomNavigation) {
        NavigationBar(
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surfaceContainer
        ) {
            mainNavigationItems.forEach { item ->
                val isSelected = navigationState.currentDestination?.route == item.destination::class.qualifiedName

                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.contentDescription,
                            modifier = Modifier.size(layout.sizing.iconMedium)
                        )
                    },
                    label = {
                        Text(
                            text = item.label,
                            style = MaterialTheme.typography.labelMedium
                        )
                    },
                    selected = isSelected,
                    onClick = {
                        when (item.destination) {
                            is AppDestination.WorkspaceHome -> navigationState.navigateToHome()
                            else -> {}
                        }
                    }
                )
            }
        }
    }
}

/**
 * Adaptive navigation rail for medium screens
 * Only shows main workspace navigation, Settings is in top app bar
 */
@Composable
fun AdaptiveNavigationRail(
    navigationState: NavigationState,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    if (layout.shouldUseNavigationRail) {
        NavigationRail(
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surfaceContainer
        ) {
            Spacer(modifier = Modifier.height(layout.spacing.medium))

            mainNavigationItems.forEach { item ->
                val isSelected = navigationState.currentDestination?.route == item.destination::class.qualifiedName

                NavigationRailItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.contentDescription,
                            modifier = Modifier.size(layout.sizing.iconMedium)
                        )
                    },
                    label = {
                        Text(
                            text = item.label,
                            style = MaterialTheme.typography.labelMedium
                        )
                    },
                    selected = isSelected,
                    onClick = {
                        when (item.destination) {
                            is AppDestination.WorkspaceHome -> navigationState.navigateToHome()
                            else -> {}
                        }
                    }
                )
            }
        }
    }
}

/**
 * Adaptive navigation drawer for expanded screens
 */
@Composable
fun AdaptiveNavigationDrawer(
    navigationState: NavigationState,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    content: @Composable () -> Unit
) {
    if (layout.shouldUseNavigationDrawer) {
        PermanentNavigationDrawer(
            drawerContent = {
                PermanentDrawerSheet(
                    modifier = Modifier.width(280.dp),
                    drawerContainerColor = MaterialTheme.colorScheme.surfaceContainer
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(layout.spacing.medium)
                    ) {
                        // App title
                        Text(
                            text = "CEBS SCADA",
                            style = MaterialTheme.typography.titleLarge,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(bottom = layout.spacing.large)
                        )
                        
                        // Navigation items
                        drawerNavigationItems.forEach { item ->
                            val isSelected = navigationState.currentDestination?.route == item.destination::class.qualifiedName

                            NavigationDrawerItem(
                                icon = {
                                    Icon(
                                        imageVector = item.icon,
                                        contentDescription = item.contentDescription,
                                        modifier = Modifier.size(layout.sizing.iconMedium)
                                    )
                                },
                                label = {
                                    Text(
                                        text = item.label,
                                        style = MaterialTheme.typography.labelLarge
                                    )
                                },
                                selected = isSelected,
                                onClick = {
                                    when (item.destination) {
                                        is AppDestination.WorkspaceHome -> navigationState.navigateToHome()
                                        is AppDestination.Settings -> navigationState.navigateToSettings()
                                        else -> {}
                                    }
                                },
                                modifier = Modifier.padding(vertical = layout.spacing.extraSmall)
                            )
                        }
                    }
                }
            },
            modifier = modifier
        ) {
            content()
        }
    } else {
        content()
    }
}

/**
 * Adaptive scaffold that includes appropriate navigation based on screen size
 */
@Composable
fun AdaptiveScaffold(
    navigationState: NavigationState,
    topBar: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    content: @Composable (PaddingValues) -> Unit
) {
    AdaptiveNavigationDrawer(
        navigationState = navigationState,
        layout = layout,
        modifier = modifier
    ) {
        Row {
            // Navigation rail for medium screens
            AdaptiveNavigationRail(
                navigationState = navigationState,
                layout = layout
            )
            
            // Main content
            Scaffold(
                topBar = topBar,
                bottomBar = {
                    AdaptiveBottomNavigation(
                        navigationState = navigationState,
                        layout = layout
                    )
                },
                floatingActionButton = floatingActionButton,
                modifier = Modifier.weight(1f)
            ) { innerPadding ->
                content(innerPadding)
            }
        }
    }
}

/**
 * Compact navigation for overlay/modal usage
 */
@Composable
fun CompactNavigationMenu(
    navigationState: NavigationState,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(layout.spacing.medium)
        ) {
            Text(
                text = "Navigation",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = layout.spacing.small)
            )
            
            drawerNavigationItems.forEach { item ->
                val isSelected = navigationState.currentDestination?.route == item.destination::class.qualifiedName
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = layout.spacing.extraSmall),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = isSelected,
                        onClick = {
                            when (item.destination) {
                                is AppDestination.WorkspaceHome -> navigationState.navigateToHome()
                                is AppDestination.Settings -> navigationState.navigateToSettings()
                                else -> {}
                            }
                            onDismiss()
                        }
                    )
                    
                    Spacer(modifier = Modifier.width(layout.spacing.small))
                    
                    Icon(
                        imageVector = item.icon,
                        contentDescription = item.contentDescription,
                        modifier = Modifier.size(layout.sizing.iconSmall)
                    )
                    
                    Spacer(modifier = Modifier.width(layout.spacing.small))
                    
                    Text(
                        text = item.label,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}
