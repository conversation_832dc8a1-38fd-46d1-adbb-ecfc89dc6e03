package com.tfkcolin.cebsscada.scada.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.scada.DataBindingManager
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for SCADA runtime screen - executing saved workspaces with live communication
 */
@HiltViewModel
class ScadaRuntimeViewModel @Inject constructor(
    private val workspaceRepository: WorkspaceRepository,
    private val dataBindingManager: DataBindingManager,
    private val communicationManager: CommunicationManager
) : ViewModel() {

    private val _components = MutableStateFlow<List<ScadaComponent>>(emptyList())
    val components: StateFlow<List<ScadaComponent>> = _components.asStateFlow()

    private val _workspaceName = MutableStateFlow<String?>(null)
    val workspaceName: StateFlow<String?> = _workspaceName.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // Communication status
    private val _connectionStates = MutableStateFlow<Map<CommunicationProtocol, ConnectionState>>(emptyMap())
    val connectionStates: StateFlow<Map<CommunicationProtocol, ConnectionState>> = _connectionStates.asStateFlow()

    private val _communicationErrors = MutableStateFlow<List<String>>(emptyList())
    val communicationErrors: StateFlow<List<String>> = _communicationErrors.asStateFlow()

    private val _activeProtocols = MutableStateFlow<Set<CommunicationProtocol>>(emptySet())
    val activeProtocols: StateFlow<Set<CommunicationProtocol>> = _activeProtocols.asStateFlow()

    init {
        // Monitor communication states
        viewModelScope.launch {
            communicationManager.connectionStates.collectLatest { states ->
                _connectionStates.value = states
            }
        }

        // Monitor communication errors
        viewModelScope.launch {
            communicationManager.allErrors.collectLatest { error ->
                val currentErrors = _communicationErrors.value.toMutableList()
                currentErrors.add(error)
                // Keep only last 10 errors
                if (currentErrors.size > 10) {
                    currentErrors.removeAt(0)
                }
                _communicationErrors.value = currentErrors
            }
        }
    }

    /**
     * Load a workspace for runtime execution with live communication
     */
    fun loadWorkspace(workspaceId: Long) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = workspaceRepository.loadWorkspace(workspaceId)
                when (result) {
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Success -> {
                        val workspaceWithComponents = result.data
                        _workspaceName.value = workspaceWithComponents.workspace.name
                        val components = workspaceRepository.componentsFromEntities(
                            workspaceWithComponents.components
                        )
                        _components.value = components

                        // Register components with DataBindingManager for real-time updates
                        setupRuntimeCommunication(components)
                    }
                    is com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult.Error -> {
                        _workspaceName.value = "Error: ${result.message}"
                        _components.value = emptyList()
                    }
                }
            } catch (e: Exception) {
                _workspaceName.value = "Error loading workspace"
                _components.value = emptyList()
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Setup runtime communication for loaded components
     */
    private suspend fun setupRuntimeCommunication(components: List<ScadaComponent>) {
        // Clear previous registrations
        dataBindingManager.clearAllRegistrations()

        // Collect all protocols used by components
        val protocolsUsed = mutableSetOf<CommunicationProtocol>()

        // Register each component and collect protocols
        components.forEach { component ->
            dataBindingManager.registerComponent(component)

            // Extract protocol from component's data binding
            val protocol = when (component.config) {
                is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).dataBinding.protocol
                is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).dataBinding.protocol
                is com.tfkcolin.cebsscada.scada.components.ToggleSwitchConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.ToggleSwitchConfig).dataBinding.protocol
                else -> null
            }

            protocol?.let { protocolsUsed.add(it) }
        }

        _activeProtocols.value = protocolsUsed

        // Start discovery for all protocols used by components
        // Note: In a real implementation, you might want to connect to specific devices
        // rather than just starting discovery. This provides a basic setup.
        protocolsUsed.forEach { protocol ->
            try {
                communicationManager.startDiscovery(protocol)
            } catch (e: Exception) {
                // Log error but don't fail the entire setup
                val currentErrors = _communicationErrors.value.toMutableList()
                currentErrors.add("$protocol discovery failed: ${e.message}")
                _communicationErrors.value = currentErrors
            }
        }
    }

    /**
     * Connect to a device for a specific protocol
     */
    fun connectToDevice(protocol: CommunicationProtocol, deviceId: String) {
        viewModelScope.launch {
            try {
                // Find the device in available devices
                val device = communicationManager.allDevices.value
                    .find { it.protocol == protocol && it.id == deviceId }

                device?.let {
                    communicationManager.connect(it)
                }
            } catch (e: Exception) {
                val currentErrors = _communicationErrors.value.toMutableList()
                currentErrors.add("Failed to connect to $protocol device: ${e.message}")
                _communicationErrors.value = currentErrors
            }
        }
    }

    /**
     * Disconnect from a protocol
     */
    fun disconnectProtocol(protocol: CommunicationProtocol) {
        viewModelScope.launch {
            try {
                communicationManager.disconnect(protocol)
            } catch (e: Exception) {
                val currentErrors = _communicationErrors.value.toMutableList()
                currentErrors.add("Failed to disconnect $protocol: ${e.message}")
                _communicationErrors.value = currentErrors
            }
        }
    }

    /**
     * Clear communication errors
     */
    fun clearCommunicationErrors() {
        _communicationErrors.value = emptyList()
    }

    /**
     * Get connection statistics for a protocol
     */
    fun getConnectionStats(protocol: CommunicationProtocol): Map<String, Any> {
        return communicationManager.getAllConnectionStats()[protocol] ?: emptyMap()
    }

    override fun onCleared() {
        super.onCleared()
        // Clean up communication resources when ViewModel is destroyed
        viewModelScope.launch {
            communicationManager.disconnectAll()
            dataBindingManager.clearAllRegistrations()
        }
    }
}