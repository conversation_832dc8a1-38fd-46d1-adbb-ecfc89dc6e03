package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.tfkcolin.cebsscada.scada.ComponentType
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.ValidationError
import com.tfkcolin.cebsscada.scada.ValidationSeverity

/**
 * Advanced component configuration dialog with organization and validation
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedComponentConfigurationDialog(
    component: ScadaComponent,
    availableGroups: List<com.tfkcolin.cebsscada.scada.ComponentGroup>,
    onSave: (ScadaComponent) -> Unit,
    onCancel: () -> Unit,
    onDelete: () -> Unit
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("Basic", "Organization", "Communication", "Validation")

    // Component state
    var groupId by remember { mutableStateOf(component.groupId) }
    var layer by remember { mutableStateOf(component.layer) }
    var isLocked by remember { mutableStateOf(component.isLocked) }
    var isVisible by remember { mutableStateOf(component.isVisible) }
    var tags by remember { mutableStateOf(component.tags.toMutableSet()) }
    var newTag by remember { mutableStateOf("") }

    // Validation
    val validationErrors by remember { derivedStateOf { component.validate() } }

    AlertDialog(
        onDismissRequest = onCancel,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .fillMaxHeight(0.8f),
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("Configure ${component.type.name.replace("_", " ")}")
                // Component ID badge
                Surface(
                    color = MaterialTheme.colorScheme.secondaryContainer,
                    shape = MaterialTheme.shapes.small
                ) {
                    Text(
                        text = component.id.take(8),
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
        },
        text = {
            Column(modifier = Modifier.fillMaxSize()) {
                TabRow(selectedTabIndex = selectedTab) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            text = { Text(title) }
                        )
                    }
                }

                when (selectedTab) {
                    0 -> BasicConfigurationTab(component)
                    1 -> OrganizationTab(
                        component = component,
                        availableGroups = availableGroups,
                        groupId = groupId,
                        onGroupIdChange = { groupId = it },
                        layer = layer,
                        onLayerChange = { layer = it },
                        isLocked = isLocked,
                        onLockedChange = { isLocked = it },
                        isVisible = isVisible,
                        onVisibleChange = { isVisible = it },
                        tags = tags,
                        newTag = newTag,
                        onNewTagChange = { newTag = it },
                        onAddTag = {
                            if (newTag.isNotBlank() && !tags.contains(newTag)) {
                                tags.add(newTag)
                                newTag = ""
                            }
                        },
                        onRemoveTag = { tag -> tags.remove(tag) }
                    )
                    2 -> CommunicationTab(component)
                    3 -> ValidationTab(validationErrors)
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val updatedComponent = component.updateOrganization(
                        groupId = groupId,
                        layer = layer,
                        isLocked = isLocked,
                        isVisible = isVisible,
                        tags = tags
                    )
                    onSave(updatedComponent)
                },
                enabled = validationErrors.none { it.severity == ValidationSeverity.ERROR }
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            Row {
                TextButton(onClick = onDelete) {
                    Text("Delete", color = MaterialTheme.colorScheme.error)
                }
                Spacer(modifier = Modifier.width(8.dp))
                TextButton(onClick = onCancel) {
                    Text("Cancel")
                }
            }
        }
    )
}

/**
 * Basic configuration tab
 */
@Composable
private fun BasicConfigurationTab(component: ScadaComponent) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Component Information",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    InfoRow("Type", component.type.name.replace("_", " "))
                    InfoRow("ID", component.id)
                    InfoRow("Position", "(${component.position.x.toInt()}, ${component.position.y.toInt()})")
                    InfoRow("Size", "${component.size.width.value.toInt()} × ${component.size.height.value.toInt()}")
                }
            }
        }

        // Component-specific configuration would go here
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Component Settings",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    when (component.type) {
                        ComponentType.PUSH_BUTTON -> PushButtonSettings(component)
                        ComponentType.VALUE_DISPLAY -> ValueDisplaySettings(component)
                        ComponentType.TOGGLE_SWITCH -> ToggleSwitchSettings(component)
                        else -> Text("Advanced settings for ${component.type.name.replace("_", " ")} will be implemented")
                    }
                }
            }
        }
    }
}

/**
 * Organization tab
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun OrganizationTab(
    component: ScadaComponent,
    availableGroups: List<com.tfkcolin.cebsscada.scada.ComponentGroup>,
    groupId: String?,
    onGroupIdChange: (String?) -> Unit,
    layer: Int,
    onLayerChange: (Int) -> Unit,
    isLocked: Boolean,
    onLockedChange: (Boolean) -> Unit,
    isVisible: Boolean,
    onVisibleChange: (Boolean) -> Unit,
    tags: Set<String>,
    newTag: String,
    onNewTagChange: (String) -> Unit,
    onAddTag: () -> Unit,
    onRemoveTag: (String) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        // Grouping
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Grouping",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    var expanded by remember { mutableStateOf(false) }
                    ExposedDropdownMenuBox(
                        expanded = expanded,
                        onExpandedChange = { expanded = it }
                    ) {
                        OutlinedTextField(
                            value = availableGroups.find { it.id == groupId }?.name ?: "No Group",
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("Component Group") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor()
                        )

                        ExposedDropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("No Group") },
                                onClick = {
                                    onGroupIdChange(null)
                                    expanded = false
                                }
                            )
                            availableGroups.forEach { group ->
                                DropdownMenuItem(
                                    text = { Text(group.name) },
                                    onClick = {
                                        onGroupIdChange(group.id)
                                        expanded = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        // Layering
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Layering",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("Layer:", modifier = Modifier.weight(1f))
                        IconButton(onClick = { onLayerChange(layer - 1) }) {
                            Icon(Icons.Default.Remove, "Decrease layer")
                        }
                        Text(
                            text = layer.toString(),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                        IconButton(onClick = { onLayerChange(layer + 1) }) {
                            Icon(Icons.Default.Add, "Increase layer")
                        }
                    }

                    Text(
                        text = "Higher layers appear on top of lower layers",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // Visibility and Locking
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Component State",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.weight(1f)
                        ) {
                            Checkbox(
                                checked = isVisible,
                                onCheckedChange = onVisibleChange
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Visible")
                        }

                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.weight(1f)
                        ) {
                            Checkbox(
                                checked = isLocked,
                                onCheckedChange = onLockedChange
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Locked")
                        }
                    }
                }
            }
        }

        // Tags
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Tags",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Add new tag
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = newTag,
                            onValueChange = onNewTagChange,
                            label = { Text("New Tag") },
                            modifier = Modifier.weight(1f),
                            singleLine = true
                        )
                        Button(
                            onClick = onAddTag,
                            enabled = newTag.isNotBlank() && !tags.contains(newTag)
                        ) {
                            Icon(Icons.Default.Add, "Add tag")
                        }
                    }

                    // Existing tags
                    if (tags.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Current Tags:",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(4.dp))

                        tags.forEach { tag ->
                            AssistChip(
                                onClick = { onRemoveTag(tag) },
                                label = { Text(tag) },
                                trailingIcon = {
                                    Icon(
                                        Icons.Default.Close,
                                        contentDescription = "Remove tag"
                                    )
                                },
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Communication tab
 */
@Composable
private fun CommunicationTab(component: ScadaComponent) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Data Binding",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // This would show the current data binding configuration
                    // For now, just show a placeholder
                    when (component.config) {
                        is com.tfkcolin.cebsscada.scada.components.PushButtonConfig -> {
                            val config = component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig
                            val binding = config.dataBinding
                            DataBindingInfo("Protocol", binding.protocol?.name ?: "None")
                            DataBindingInfo("Address", binding.address.ifEmpty { "Not set" })
                            DataBindingInfo("Payload", binding.payload.ifEmpty { "Default" })
                        }
                        is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig -> {
                            val config = component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig
                            val binding = config.dataBinding
                            DataBindingInfo("Protocol", binding.protocol?.name ?: "None")
                            DataBindingInfo("Address", binding.address.ifEmpty { "Not set" })
                            DataBindingInfo("Format", binding.dataFormat.name)
                        }
                        else -> {
                            Text("Communication settings will be configurable here")
                        }
                    }
                }
            }
        }
    }
}

/**
 * Validation tab
 */
@Composable
private fun ValidationTab(validationErrors: List<ValidationError>) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            if (validationErrors.isEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = "Valid",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Component configuration is valid",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            } else {
                Text(
                    text = "Validation Issues",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }
        }

        items(validationErrors) { error ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = when (error.severity) {
                        ValidationSeverity.ERROR -> MaterialTheme.colorScheme.errorContainer
                        ValidationSeverity.WARNING -> MaterialTheme.colorScheme.tertiaryContainer
                        ValidationSeverity.INFO -> MaterialTheme.colorScheme.secondaryContainer
                    }
                )
            ) {
                Row(modifier = Modifier.padding(12.dp)) {
                    Icon(
                        imageVector = when (error.severity) {
                            ValidationSeverity.ERROR -> Icons.Default.Error
                            ValidationSeverity.WARNING -> Icons.Default.Warning
                            ValidationSeverity.INFO -> Icons.Default.Info
                        },
                        contentDescription = error.severity.name,
                        tint = when (error.severity) {
                            ValidationSeverity.ERROR -> MaterialTheme.colorScheme.error
                            ValidationSeverity.WARNING -> MaterialTheme.colorScheme.tertiary
                            ValidationSeverity.INFO -> MaterialTheme.colorScheme.secondary
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = error.field.replaceFirstChar { it.uppercase() },
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                        )
                        Text(
                            text = error.message,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
    }
}

/**
 * Helper composables
 */
@Composable
private fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun DataBindingInfo(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            style = MaterialTheme.typography.bodyMedium
        )
        Surface(
            color = MaterialTheme.colorScheme.surfaceVariant,
            shape = MaterialTheme.shapes.small
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
    }
}

@Composable
private fun PushButtonSettings(component: ScadaComponent) {
    val config = component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig
    // Basic settings - could be expanded
    Text("Push button specific settings would go here")
}

@Composable
private fun ValueDisplaySettings(component: ScadaComponent) {
    val config = component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig
    // Basic settings - could be expanded
    Text("Value display specific settings would go here")
}

@Composable
private fun ToggleSwitchSettings(component: ScadaComponent) {
    val config = component.config as com.tfkcolin.cebsscada.scada.components.ToggleSwitchConfig
    // Basic settings - could be expanded
    Text("Toggle switch specific settings would go here")
}