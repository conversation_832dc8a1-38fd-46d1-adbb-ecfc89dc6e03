package com.tfkcolin.cebsscada.scada.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.DataBindingManager
import com.tfkcolin.cebsscada.scada.ErrorHandler
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.WorkspaceValidator
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceRepository
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for managing SCADA workspace state
 */
@HiltViewModel
class ScadaWorkspaceViewModel @Inject constructor(
    private val dataBindingManager: DataBindingManager,
    private val workspaceRepository: WorkspaceRepository
) : ViewModel() {

    // Current components in the workspace
    private val _components = MutableStateFlow<List<ScadaComponent>>(emptyList())
    val components: StateFlow<List<ScadaComponent>> = _components.asStateFlow()

    // Currently selected component
    private val _selectedComponent = MutableStateFlow<ScadaComponent?>(null)
    val selectedComponent: StateFlow<ScadaComponent?> = _selectedComponent.asStateFlow()

    // Current workspace information
    private val _currentWorkspaceId = MutableStateFlow<Long?>(null)
    val currentWorkspaceId: StateFlow<Long?> = _currentWorkspaceId.asStateFlow()

    private val _currentWorkspaceName = MutableStateFlow<String?>(null)
    val currentWorkspaceName: StateFlow<String?> = _currentWorkspaceName.asStateFlow()

    // Loading and error states
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // Component groups for organization
    private val _componentGroups = MutableStateFlow<List<com.tfkcolin.cebsscada.scada.ComponentGroup>>(emptyList())
    val componentGroups: StateFlow<List<com.tfkcolin.cebsscada.scada.ComponentGroup>> = _componentGroups.asStateFlow()

    // Undo/Redo manager
    private val historyManager = ComponentHistoryManager()

    init {
        // Initialize component factory with data binding manager
        ComponentFactory.initialize(dataBindingManager)

        // Save initial state for undo
        saveStateForUndo()
    }

    /**
     * Add a new component to the workspace
     */
    fun addComponent(component: ScadaComponent) {
        val currentList = _components.value.toMutableList()
        currentList.add(component)
        _components.value = currentList

        // Register component for data binding
        dataBindingManager.registerComponent(component)
    }

    /**
     * Remove a component from the workspace
     */
    fun removeComponent(component: ScadaComponent) {
        val currentList = _components.value.toMutableList()
        currentList.remove(component)
        _components.value = currentList

        // Clear selection if removed component was selected
        if (_selectedComponent.value == component) {
            _selectedComponent.value = null
        }

        // Unregister component from data binding
        dataBindingManager.unregisterComponent(component.id)
    }

    /**
     * Move a component to a new position
     */
    fun moveComponent(component: ScadaComponent, newPosition: ComponentPosition) {
        component.moveTo(newPosition)
        // Trigger recomposition by updating the list
        _components.value = _components.value.toList()
    }

    /**
     * Select a component
     */
    fun selectComponent(component: ScadaComponent?) {
        _selectedComponent.value = component
    }

    /**
     * Clear component selection
     */
    fun clearSelection() {
        _selectedComponent.value = null
    }

    /**
     * Update component configuration
     */
    fun updateComponentConfig(component: ScadaComponent, newConfig: com.tfkcolin.cebsscada.scada.ComponentConfig) {
        component.updateConfig(newConfig)
        // Trigger recomposition
        _components.value = _components.value.toList()
    }

    /**
     * Clear all components from workspace
     */
    fun clearWorkspace() {
        _components.value = emptyList()
        _selectedComponent.value = null
        dataBindingManager.clearAllRegistrations()
    }

    /**
     * Get component by ID
     */
    fun getComponentById(id: String): ScadaComponent? {
        return _components.value.find { it.id == id }
    }

    /**
     * Duplicate a component
     */
    fun duplicateComponent(component: ScadaComponent) {
        val newPosition = ComponentPosition(
            x = component.position.x + 20f,
            y = component.position.y + 20f
        )

        // Create new component of same type with offset position
        val newComponent = ComponentFactory.createComponent(
            type = component.type,
            position = newPosition
        )

        newComponent?.let { addComponent(it) }
    }

    /**
     * Save current workspace with enhanced error handling
     */
    fun saveWorkspace(name: String, description: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null

            try {
                val components = _components.value
                if (components.isEmpty()) {
                    val error = ErrorHandler.ScadaError(
                        category = ErrorHandler.ErrorCategory.VALIDATION,
                        severity = ErrorHandler.ErrorSeverity.HIGH,
                        code = "EMPTY_WORKSPACE",
                        message = "Cannot save empty workspace",
                        userMessage = "Please add at least one component before saving the workspace.",
                        recoveryActions = listOf(
                            ErrorHandler.RecoveryAction(
                                title = "Add Components",
                                description = "Add components to your workspace from the palette"
                            )
                        )
                    )
                    ErrorHandler.logError(error)
                    _errorMessage.value = error.userMessage
                    return@launch
                }

                val result = workspaceRepository.saveWorkspace(name, description, components)
                when (result) {
                    is WorkspaceResult.Success -> {
                        _currentWorkspaceId.value = result.data
                        _currentWorkspaceName.value = name
                        _errorMessage.value = null
                    }
                    is WorkspaceResult.Error -> {
                        val error = ErrorHandler.handleWorkspaceSaveError(result)
                        ErrorHandler.logError(error)
                        _errorMessage.value = error.userMessage
                    }
                }
            } catch (e: Exception) {
                val error = ErrorHandler.handleRuntimeError(e, "workspace save")
                ErrorHandler.logError(error)
                _errorMessage.value = error.userMessage
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load a workspace by ID with enhanced error handling
     */
    fun loadWorkspace(workspaceId: Long) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null

            try {
                val result = workspaceRepository.loadWorkspace(workspaceId)
                when (result) {
                    is WorkspaceResult.Success -> {
                        val workspaceWithComponents = result.data
                        val components = workspaceRepository.componentsFromEntities(
                            workspaceWithComponents.components
                        )

                        // Clear existing components and data bindings
                        clearWorkspace()

                        // Load new components
                        _components.value = components
                        _currentWorkspaceId.value = workspaceId
                        _currentWorkspaceName.value = workspaceWithComponents.workspace.name

                        // Register components with data binding manager
                        components.forEach { component ->
                            try {
                                dataBindingManager.registerComponent(component)
                            } catch (e: Exception) {
                                val error = ErrorHandler.handleComponentConfigError(
                                    component.type.name,
                                    e
                                )
                                ErrorHandler.logError(error)
                                // Continue loading other components
                            }
                        }

                        _errorMessage.value = null
                    }
                    is WorkspaceResult.Error -> {
                        val error = ErrorHandler.handleWorkspaceLoadError(result)
                        ErrorHandler.logError(error)
                        _errorMessage.value = error.userMessage
                    }
                }
            } catch (e: Exception) {
                val error = ErrorHandler.handleRuntimeError(e, "workspace load")
                ErrorHandler.logError(error)
                _errorMessage.value = error.userMessage
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Create a new workspace (clear current one)
     */
    fun createNewWorkspace() {
        clearWorkspace()
        _currentWorkspaceId.value = null
        _currentWorkspaceName.value = null
        _errorMessage.value = null
    }

    /**
     * Check if workspace has unsaved changes
     */
    fun hasUnsavedChanges(): Boolean {
        return _currentWorkspaceId.value == null && _components.value.isNotEmpty()
    }

    /**
     * Load available workspaces for selection
     */
    suspend fun loadAvailableWorkspaces(): List<com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity> {
        return try {
            workspaceRepository.getAllWorkspaces().first()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    // ===== COMPONENT ORGANIZATION METHODS =====

    /**
     * Save current state for undo functionality
     */
    private fun saveStateForUndo() {
        historyManager.saveState(_components.value)
    }

    /**
     * Undo last operation
     */
    fun undo(): Boolean {
        val previousState = historyManager.undo()
        return if (previousState != null) {
            restoreComponentsFromState(previousState)
            true
        } else {
            false
        }
    }

    /**
     * Redo last undone operation
     */
    fun redo(): Boolean {
        val nextState = historyManager.redo()
        return if (nextState != null) {
            restoreComponentsFromState(nextState)
            true
        } else {
            false
        }
    }

    /**
     * Check if undo is available
     */
    fun canUndo(): Boolean = historyManager.canUndo()

    /**
     * Check if redo is available
     */
    fun canRedo(): Boolean = historyManager.canRedo()

    /**
     * Restore components from saved state
     */
    private fun restoreComponentsFromState(states: List<ComponentState>) {
        val componentMap = _components.value.associateBy { it.id }

        states.forEach { state ->
            componentMap[state.id]?.let { component ->
                // Restore position and size
                component.moveTo(state.position)
                component.resizeTo(state.size)

                // Restore organization properties
                component.updateOrganization(
                    groupId = state.groupId,
                    layer = state.layer,
                    isLocked = state.isLocked,
                    isVisible = state.isVisible,
                    tags = state.tags
                )
            }
        }

        // Trigger recomposition
        _components.value = _components.value.toList()
    }

    /**
     * Create a new component group
     */
    fun createComponentGroup(name: String, color: androidx.compose.ui.graphics.Color = androidx.compose.ui.graphics.Color.Gray): com.tfkcolin.cebsscada.scada.ComponentGroup {
        val group = ComponentOrganizationTools.createComponentGroup(
            components = _selectedComponent.value?.let { listOf(it) } ?: emptyList(),
            groupName = name,
            groupColor = color
        )

        val currentGroups = _componentGroups.value.toMutableList()
        currentGroups.add(group)
        _componentGroups.value = currentGroups

        // Assign selected components to the new group
        _selectedComponent.value?.let { component ->
            updateComponentOrganization(component, groupId = group.id)
        }

        saveStateForUndo()
        return group
    }

    /**
     * Assign components to a group
     */
    fun assignComponentsToGroup(components: List<ScadaComponent>, groupId: String?) {
        components.forEach { component ->
            updateComponentOrganization(component, groupId = groupId)
        }
        saveStateForUndo()
    }

    /**
     * Update component organization properties
     */
    fun updateComponentOrganization(
        component: ScadaComponent,
        groupId: String? = component.groupId,
        layer: Int = component.layer,
        isLocked: Boolean = component.isLocked,
        isVisible: Boolean = component.isVisible,
        tags: Set<String> = component.tags
    ) {
        component.updateOrganization(groupId, layer, isLocked, isVisible, tags)
        _components.value = _components.value.toList() // Trigger recomposition
        saveStateForUndo()
    }

    /**
     * Align selected components
     */
    fun alignComponents(alignment: ComponentOrganizationTools.Alignment, workspaceWidth: Float = 800f, workspaceHeight: Float = 600f) {
        val selectedComponents = getSelectedComponents()
        if (selectedComponents.isEmpty()) return

        val updates = ComponentOrganizationTools.alignComponents(
            components = selectedComponents,
            alignment = alignment,
            workspaceWidth = workspaceWidth,
            workspaceHeight = workspaceHeight
        )

        applyPositionUpdates(updates)
    }

    /**
     * Distribute selected components
     */
    fun distributeComponents(distribution: ComponentOrganizationTools.Distribution, workspaceWidth: Float = 800f, workspaceHeight: Float = 600f) {
        val selectedComponents = getSelectedComponents()
        if (selectedComponents.size < 3) return

        val updates = ComponentOrganizationTools.distributeComponents(
            components = selectedComponents,
            distribution = distribution,
            workspaceWidth = workspaceWidth,
            workspaceHeight = workspaceHeight
        )

        applyPositionUpdates(updates)
    }

    /**
     * Match sizes of selected components
     */
    fun matchComponentSizes(matchWidth: Boolean = true, matchHeight: Boolean = true) {
        val selectedComponents = getSelectedComponents()
        if (selectedComponents.isEmpty()) return

        val updates = ComponentOrganizationTools.matchSizes(
            components = selectedComponents,
            matchWidth = matchWidth,
            matchHeight = matchHeight
        )

        updates.forEach { (component, newSize) ->
            component.resizeTo(newSize)
        }

        _components.value = _components.value.toList()
        saveStateForUndo()
    }

    /**
     * Arrange components in a grid
     */
    fun arrangeComponentsInGrid(columns: Int, spacing: Float = 20f, startX: Float = 50f, startY: Float = 50f) {
        val selectedComponents = getSelectedComponents()
        if (selectedComponents.isEmpty()) return

        val updates = ComponentOrganizationTools.arrangeInGrid(
            components = selectedComponents,
            columns = columns,
            spacing = spacing,
            startX = startX,
            startY = startY
        )

        applyPositionUpdates(updates)
    }

    /**
     * Bring selected components to front
     */
    fun bringToFront() {
        val selectedComponents = getSelectedComponents()
        if (selectedComponents.isEmpty()) return

        val maxLayer = _components.value.maxOfOrNull { it.layer } ?: 0
        val updates = ComponentOrganizationTools.bringToFront(selectedComponents, maxLayer)

        updates.forEach { (component, newLayer) ->
            updateComponentOrganization(component, layer = newLayer)
        }
    }

    /**
     * Send selected components to back
     */
    fun sendToBack() {
        val selectedComponents = getSelectedComponents()
        val updates = ComponentOrganizationTools.sendToBack(selectedComponents)

        updates.forEach { (component, newLayer) ->
            updateComponentOrganization(component, layer = newLayer)
        }
    }

    /**
     * Get selected components (current selection or all if none selected)
     */
    private fun getSelectedComponents(): List<ScadaComponent> {
        return _selectedComponent.value?.let { listOf(it) } ?: _components.value
    }

    /**
     * Apply position updates to components
     */
    private fun applyPositionUpdates(updates: List<Pair<ScadaComponent, ComponentPosition>>) {
        updates.forEach { (component, newPosition) ->
            component.moveTo(newPosition)
        }

        _components.value = _components.value.toList()
        saveStateForUndo()
    }

    /**
     * Get components by group
     */
    fun getComponentsByGroup(groupId: String?): List<ScadaComponent> {
        return _components.value.filter { it.groupId == groupId }
    }

    /**
     * Get components by tags
     */
    fun getComponentsByTags(tags: Set<String>): List<ScadaComponent> {
        return _components.value.filter { component ->
            tags.any { tag -> component.tags.contains(tag) }
        }
    }

    /**
     * Toggle component visibility
     */
    fun toggleComponentVisibility(component: ScadaComponent) {
        updateComponentOrganization(component, isVisible = !component.isVisible)
    }

    /**
     * Toggle component lock state
     */
    fun toggleComponentLock(component: ScadaComponent) {
        updateComponentOrganization(component, isLocked = !component.isLocked)
    }

    /**
     * Delete component group and unassign components
     */
    fun deleteComponentGroup(groupId: String) {
        // Remove group from list
        val currentGroups = _componentGroups.value.toMutableList()
        currentGroups.removeAll { it.id == groupId }
        _componentGroups.value = currentGroups

        // Unassign components from this group
        _components.value.forEach { component ->
            if (component.groupId == groupId) {
                updateComponentOrganization(component, groupId = null)
            }
        }
    }

    /**
     * Get workspace statistics
     */
    fun getWorkspaceStats(): ScadaWorkspaceStats {
        val components = _components.value
        val groups = _componentGroups.value

        return ScadaWorkspaceStats(
            totalComponents = components.size,
            visibleComponents = components.count { it.isVisible },
            lockedComponents = components.count { it.isLocked },
            groupedComponents = components.count { it.groupId != null },
            totalGroups = groups.size,
            maxLayer = components.maxOfOrNull { it.layer } ?: 0,
            uniqueTags = components.flatMap { it.tags }.toSet().size
        )
    }

    /**
     * Validate entire workspace using comprehensive validator
     */
    fun validateWorkspace(): List<WorkspaceValidator.ValidationResult> {
        val workspaceName = _currentWorkspaceName.value ?: "Unnamed Workspace"
        val workspaceDescription = "" // Could be stored separately if needed
        val components = _components.value

        return WorkspaceValidator.validateWorkspace(workspaceName, workspaceDescription, components)
    }

    /**
     * Validate workspace before saving
     */
    fun validateWorkspaceForSave(): WorkspaceValidator.ValidationSummary {
        val results = validateWorkspace()
        return WorkspaceValidator.getValidationSummary(results)
    }

    /**
     * Get validation results for display
     */
    fun getValidationResults(): List<WorkspaceValidator.ValidationResult> {
        return validateWorkspace()
    }

    /**
     * Check if workspace can be saved (basic validation)
     */
    fun canSaveWorkspace(): Boolean {
        val components = _components.value
        return components.isNotEmpty()
    }

    /**
     * Enhanced save workspace with validation
     */
    fun saveWorkspaceWithValidation(name: String, description: String, showValidationDialog: Boolean = true): Boolean {
        // Basic validation first
        if (!canSaveWorkspace()) {
            _errorMessage.value = "Cannot save empty workspace"
            return false
        }

        // Name validation
        val nameValidation = WorkspaceValidator.validateWorkspaceName(name)
        if (!nameValidation.isValid && nameValidation.severity == WorkspaceValidator.Severity.ERROR) {
            _errorMessage.value = nameValidation.message
            return false
        }

        // Full validation
        val validationSummary = validateWorkspaceForSave()

        if (!validationSummary.isValid) {
            if (showValidationDialog) {
                // In a real implementation, this would trigger a dialog
                // For now, just set error message
                _errorMessage.value = "Workspace validation failed. ${validationSummary.errorCount} errors found."
            }
            return false
        }

        // If there are warnings but no errors, proceed with save
        if (validationSummary.canProceedWithWarnings && showValidationDialog) {
            // In a real implementation, show warning dialog
            // For now, proceed with save
        }

        // Proceed with save
        saveWorkspace(name, description)
        return true
    }

    /**
     * Legacy validate method for backward compatibility
     */
    fun validateWorkspaceLegacy(): List<WorkspaceValidationError> {
        val errors = mutableListOf<WorkspaceValidationError>()

        // Component validation
        _components.value.forEach { component ->
            val componentErrors = component.validate()
            if (componentErrors.isNotEmpty()) {
                errors.add(WorkspaceValidationError.ComponentError(
                    componentId = component.id,
                    componentType = component.type,
                    errors = componentErrors
                ))
            }
        }

        // Group validation
        val usedGroupIds = _components.value.mapNotNull { it.groupId }.toSet()
        val definedGroupIds = _componentGroups.value.map { it.id }.toSet()
        val orphanedGroups = usedGroupIds - definedGroupIds
        val unusedGroups = definedGroupIds - usedGroupIds

        if (orphanedGroups.isNotEmpty()) {
            errors.add(WorkspaceValidationError.GroupError(
                message = "Components reference undefined groups: ${orphanedGroups.joinToString()}"
            ))
        }

        if (unusedGroups.isNotEmpty()) {
            errors.add(WorkspaceValidationError.GroupWarning(
                message = "Unused groups: ${unusedGroups.joinToString()}"
            ))
        }

        return errors
    }
}

/**
 * Workspace statistics
 */
data class ScadaWorkspaceStats(
    val totalComponents: Int,
    val visibleComponents: Int,
    val lockedComponents: Int,
    val groupedComponents: Int,
    val totalGroups: Int,
    val maxLayer: Int,
    val uniqueTags: Int
)

/**
 * Workspace validation error types
 */
sealed class WorkspaceValidationError {
    data class ComponentError(
        val componentId: String,
        val componentType: com.tfkcolin.cebsscada.scada.ComponentType,
        val errors: List<com.tfkcolin.cebsscada.scada.ValidationError>
    ) : WorkspaceValidationError()

    data class GroupError(val message: String) : WorkspaceValidationError()
    data class GroupWarning(val message: String) : WorkspaceValidationError()
}