package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for device discovery operations
 */
@HiltViewModel
class DeviceDiscoveryViewModel @Inject constructor(
    private val communicationManager: CommunicationManager
) : ViewModel() {

    private val _discoveredDevices = mutableStateOf<List<CommunicationDevice>>(emptyList())
    val discoveredDevices: List<CommunicationDevice> = _discoveredDevices.value

    private val _isScanning = mutableStateOf(false)
    val isScanning: Boolean = _isScanning.value

    private val _connectionStates = mutableStateOf<Map<CommunicationProtocol, ConnectionState>>(emptyMap())
    val connectionStates: Map<CommunicationProtocol, ConnectionState> = _connectionStates.value

    private val _connectedDevices = mutableStateOf<Map<CommunicationProtocol, CommunicationDevice>>(emptyMap())
    val connectedDevices: Map<CommunicationProtocol, CommunicationDevice> = _connectedDevices.value

    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: String? = _errorMessage.value

    init {
        // Collect device discovery updates
        viewModelScope.launch {
            communicationManager.allDevices.collectLatest { devices ->
                _discoveredDevices.value = devices
            }
        }

        // Collect connection state updates
        viewModelScope.launch {
            communicationManager.connectionStates.collectLatest { states ->
                _connectionStates.value = states
            }
        }

        // Collect connected devices updates
        viewModelScope.launch {
            communicationManager.connectedDevices.collectLatest { devices ->
                _connectedDevices.value = devices
            }
        }

        // Collect error messages
        viewModelScope.launch {
            communicationManager.allErrors.collectLatest { error ->
                _errorMessage.value = error
            }
        }
    }

    /**
     * Start scanning for devices of a specific protocol
     */
    fun startScanning(protocol: CommunicationProtocol) {
        viewModelScope.launch {
            try {
                _isScanning.value = true
                _errorMessage.value = null
                communicationManager.startDiscovery(protocol)
            } catch (e: Exception) {
                _errorMessage.value = "Failed to start scanning: ${e.message}"
                _isScanning.value = false
            }
        }
    }

    /**
     * Stop scanning for devices
     */
    fun stopScanning(protocol: CommunicationProtocol) {
        viewModelScope.launch {
            try {
                communicationManager.stopDiscovery(protocol)
                _isScanning.value = false
            } catch (e: Exception) {
                _errorMessage.value = "Failed to stop scanning: ${e.message}"
            }
        }
    }

    /**
     * Connect to a device
     */
    fun connectToDevice(device: CommunicationDevice, onSuccess: (CommunicationDevice) -> Unit) {
        viewModelScope.launch {
            try {
                _errorMessage.value = null
                val success = communicationManager.connect(device)
                if (success) {
                    onSuccess(device)
                } else {
                    _errorMessage.value = "Failed to connect to ${device.name}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Connection error: ${e.message}"
            }
        }
    }

    /**
     * Disconnect from a protocol
     */
    fun disconnect(protocol: CommunicationProtocol) {
        viewModelScope.launch {
            try {
                communicationManager.disconnect(protocol)
            } catch (e: Exception) {
                _errorMessage.value = "Disconnection error: ${e.message}"
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Get devices for a specific protocol
     */
    fun getDevicesForProtocol(protocol: CommunicationProtocol): List<CommunicationDevice> {
        return discoveredDevices.filter { it.protocol == protocol }
    }

    /**
     * Check if a protocol is supported
     */
    fun isProtocolSupported(protocol: CommunicationProtocol): Boolean {
        return communicationManager.isProtocolSupported(protocol)
    }

    /**
     * Get supported protocols
     */
    fun getSupportedProtocols(): List<CommunicationProtocol> {
        return communicationManager.getSupportedProtocols()
    }
}

/**
 * Dialog for discovering and connecting to communication devices
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceDiscoveryDialog(
    selectedProtocol: CommunicationProtocol? = null,
    onDeviceSelected: (CommunicationDevice) -> Unit,
    onDismiss: () -> Unit,
    viewModel: DeviceDiscoveryViewModel = hiltViewModel()
) {
    var currentProtocol by remember { mutableStateOf(selectedProtocol) }
    var expanded by remember { mutableStateOf(false) }

    val discoveredDevices = viewModel.discoveredDevices
    val isScanning = viewModel.isScanning
    val connectionStates = viewModel.connectionStates
    val connectedDevices = viewModel.connectedDevices
    val errorMessage = viewModel.errorMessage

    // Auto-start scanning if protocol is pre-selected
    LaunchedEffect(selectedProtocol) {
        selectedProtocol?.let { protocol ->
            if (viewModel.isProtocolSupported(protocol)) {
                viewModel.startScanning(protocol)
            }
        }
    }

    // Show error message as snackbar
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            // In a real implementation, you'd show this as a snackbar
            // For now, we'll just clear it after a delay
            kotlinx.coroutines.delay(3000)
            viewModel.clearError()
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = androidx.compose.ui.window.DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .fillMaxHeight(0.8f),
        title = {
            Text("Device Discovery")
        },
        text = {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Protocol Selection
                Text(
                    text = "Communication Protocol",
                    style = MaterialTheme.typography.titleSmall
                )

                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = it }
                ) {
                    OutlinedTextField(
                        value = currentProtocol?.name?.replace("_", " ") ?: "Select Protocol",
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Protocol") },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                        },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        viewModel.getSupportedProtocols()
                            .filter { it != CommunicationProtocol.UNKNOWN }
                            .forEach { protocol ->
                                DropdownMenuItem(
                                    text = { Text(protocol.name.replace("_", " ")) },
                                    onClick = {
                                        currentProtocol = protocol
                                        expanded = false
                                        viewModel.startScanning(protocol)
                                    }
                                )
                            }
                    }
                }

                // Scanning Controls
                currentProtocol?.let { protocol ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = if (isScanning) "Scanning for devices..." else "Scan complete",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = "${viewModel.getDevicesForProtocol(protocol).size} devices found",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                            if (isScanning) {
                                OutlinedButton(
                                    onClick = { viewModel.stopScanning(protocol) }
                                ) {
                                    Text("Stop")
                                }
                            } else {
                                Button(
                                    onClick = { viewModel.startScanning(protocol) }
                                ) {
                                    Icon(Icons.Default.Search, contentDescription = "Scan")
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text("Scan")
                                }
                            }
                        }
                    }

                    // Connection Status
                    val connectionState = connectionStates[protocol]
                    val connectedDevice = connectedDevices[protocol]

                    if (connectionState != null && connectionState != ConnectionState.DISCONNECTED) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = when (connectionState) {
                                    ConnectionState.CONNECTED -> Color.Green.copy(alpha = 0.1f)
                                    ConnectionState.CONNECTING -> Color.Yellow.copy(alpha = 0.1f)
                                    ConnectionState.ERROR -> Color.Red.copy(alpha = 0.1f)
                                    else -> MaterialTheme.colorScheme.surfaceVariant
                                }
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(modifier = Modifier.weight(1f)) {
                                    Text(
                                        text = "Connection: ${connectionState.name}",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    connectedDevice?.let {
                                        Text(
                                            text = "Connected to: ${it.name}",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }

                                if (connectionState == ConnectionState.CONNECTED) {
                                    OutlinedButton(
                                        onClick = { viewModel.disconnect(protocol) }
                                    ) {
                                        Text("Disconnect")
                                    }
                                }
                            }
                        }
                    }
                }

                // Device List
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    Column(modifier = Modifier.fillMaxSize()) {
                        Text(
                            text = "Available Devices",
                            style = MaterialTheme.typography.titleSmall,
                            modifier = Modifier.padding(16.dp)
                        )

                        if (currentProtocol == null) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Select a protocol to start device discovery",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        } else {
                            val protocolDevices = viewModel.getDevicesForProtocol(currentProtocol!!)

                            if (protocolDevices.isEmpty()) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                        if (isScanning) {
                                            CircularProgressIndicator()
                                            Spacer(modifier = Modifier.height(16.dp))
                                        }
                                        Text(
                                            text = if (isScanning) "Scanning..." else "No devices found",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        if (!isScanning) {
                                            Spacer(modifier = Modifier.height(8.dp))
                                            Text(
                                                text = "Try scanning again or check device settings",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }
                                }
                            } else {
                                LazyColumn(
                                    modifier = Modifier.fillMaxSize(),
                                    contentPadding = PaddingValues(16.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    items(protocolDevices) { device ->
                                        DeviceListItem(
                                            device = device,
                                            connectionState = connectionStates[device.protocol],
                                            onConnect = {
                                                viewModel.connectToDevice(device) { connectedDevice ->
                                                    onDeviceSelected(connectedDevice)
                                                }
                                            },
                                            onSelect = { onDeviceSelected(device) }
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                // Error Message
                errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

/**
 * Individual device item in the discovery list
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceListItem(
    device: CommunicationDevice,
    connectionState: ConnectionState?,
    onConnect: () -> Unit,
    onSelect: () -> Unit
) {
    val isConnected = connectionState == ConnectionState.CONNECTED
    val isConnecting = connectionState == ConnectionState.CONNECTING

    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onSelect,
        colors = CardDefaults.cardColors(
            containerColor = if (isConnected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                CardDefaults.cardColors().containerColor
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // Device-specific properties
                when (device) {
                    is com.tfkcolin.cebsscada.communication.models.BluetoothDevice -> {
                        device.rssi?.let { rssi ->
                            Text(
                                text = "Signal: ${rssi}dBm",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    is com.tfkcolin.cebsscada.communication.models.MqttBroker -> {
                        Text(
                            text = "Port: ${device.port}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    is com.tfkcolin.cebsscada.communication.models.TcpServer -> {
                        Text(
                            text = "Port: ${device.port}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    is com.tfkcolin.cebsscada.communication.models.UdpServer -> {
                        Text(
                            text = "Port: ${device.port}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Connection status indicator
                when (connectionState) {
                    ConnectionState.CONNECTED -> {
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = "Connected",
                            tint = Color.Green,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    ConnectionState.CONNECTING -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    ConnectionState.ERROR -> {
                        Icon(
                            Icons.Default.Error,
                            contentDescription = "Connection Error",
                            tint = Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    else -> {
                        // Not connected
                    }
                }

                // Connect/Select button
                if (isConnected) {
                    Button(
                        onClick = onSelect,
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Text("Use", style = MaterialTheme.typography.bodySmall)
                    }
                } else {
                    OutlinedButton(
                        onClick = onConnect,
                        enabled = !isConnecting,
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        if (isConnecting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("Connect", style = MaterialTheme.typography.bodySmall)
                        }
                    }
                }
            }
        }
    }
}