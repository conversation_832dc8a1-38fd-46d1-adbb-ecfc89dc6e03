package com.tfkcolin.cebsscada.scada

import android.content.Context
import android.util.Log
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceResult

/**
 * Centralized error handling system for SCADA operations
 */
object ErrorHandler {

    private const val TAG = "ScadaErrorHandler"

    /**
     * Error categories for better error classification
     */
    enum class ErrorCategory {
        VALIDATION,
        COMMUNICATION,
        PERSISTENCE,
        CONFIGURATION,
        RUNTIME,
        NETWORK,
        PERMISSION,
        UNKNOWN
    }

    /**
     * Error severity levels
     */
    enum class ErrorSeverity {
        LOW,      // Minor issues, operation can continue
        MEDIUM,   // Significant issues, user attention needed
        HIGH,     // Critical issues, operation blocked
        CRITICAL  // System-level failures
    }

    /**
     * Structured error information
     */
    data class ScadaError(
        val category: ErrorCategory,
        val severity: ErrorSeverity,
        val code: String,
        val message: String,
        val userMessage: String,
        val technicalDetails: String? = null,
        val recoveryActions: List<RecoveryAction> = emptyList(),
        val timestamp: Long = System.currentTimeMillis()
    )

    /**
     * Recovery action suggestions
     */
    data class RecoveryAction(
        val title: String,
        val description: String,
        val action: (() -> Unit)? = null
    )

    /**
     * Handle workspace save errors
     */
    fun handleWorkspaceSaveError(result: WorkspaceResult<*>): ScadaError {
        return when (result) {
            is WorkspaceResult.Error -> {
                Log.e(TAG, "Workspace save error: ${result.message}", result.cause)

                val category = determineErrorCategory(result.cause)
                val severity = determineErrorSeverity(result.cause)

                ScadaError(
                    category = category,
                    severity = severity,
                    code = "WORKSPACE_SAVE_FAILED",
                    message = result.message ?: "Unknown save error",
                    userMessage = when (category) {
                        ErrorCategory.PERSISTENCE -> "Failed to save workspace to storage. Please check available space and try again."
                        ErrorCategory.VALIDATION -> "Workspace validation failed. Please review and fix any issues."
                        else -> "Unable to save workspace. Please try again."
                    },
                    technicalDetails = result.cause?.message,
                    recoveryActions = listOf(
                        RecoveryAction(
                            title = "Retry Save",
                            description = "Attempt to save the workspace again"
                        ),
                        RecoveryAction(
                            title = "Save As New",
                            description = "Save the workspace with a different name"
                        ),
                        RecoveryAction(
                            title = "Export Backup",
                            description = "Export workspace data for safekeeping"
                        )
                    )
                )
            }
            is WorkspaceResult.Success -> {
                // This should not happen if called correctly, but handle gracefully
                ScadaError(
                    category = ErrorCategory.UNKNOWN,
                    severity = ErrorSeverity.LOW,
                    code = "UNEXPECTED_SUCCESS",
                    message = "Unexpected success result in error handler",
                    userMessage = "Operation completed successfully.",
                    recoveryActions = emptyList()
                )
            }
        }
    }

    /**
     * Handle workspace load errors
     */
    fun handleWorkspaceLoadError(result: WorkspaceResult<*>): ScadaError {
        return when (result) {
            is WorkspaceResult.Error -> {
                Log.e(TAG, "Workspace load error: ${result.message}", result.cause)

                ScadaError(
                    category = ErrorCategory.PERSISTENCE,
                    severity = ErrorSeverity.HIGH,
                    code = "WORKSPACE_LOAD_FAILED",
                    message = result.message ?: "Unknown load error",
                    userMessage = "Failed to load workspace. The file may be corrupted or inaccessible.",
                    technicalDetails = result.cause?.message,
                    recoveryActions = listOf(
                        RecoveryAction(
                            title = "Try Different Workspace",
                            description = "Load a different workspace"
                        ),
                        RecoveryAction(
                            title = "Create New Workspace",
                            description = "Start with a fresh workspace"
                        ),
                        RecoveryAction(
                            title = "Contact Support",
                            description = "Report this issue for assistance"
                        )
                    )
                )
            }
            is WorkspaceResult.Success -> {
                // This should not happen if called correctly, but handle gracefully
                ScadaError(
                    category = ErrorCategory.UNKNOWN,
                    severity = ErrorSeverity.LOW,
                    code = "UNEXPECTED_SUCCESS",
                    message = "Unexpected success result in error handler",
                    userMessage = "Operation completed successfully.",
                    recoveryActions = emptyList()
                )
            }
        }
    }

    /**
     * Handle communication errors
     */
    fun handleCommunicationError(
        protocol: CommunicationProtocol,
        error: Throwable,
        operation: String
    ): ScadaError {
        Log.e(TAG, "Communication error in $operation: ${error.message}", error)

        val category = ErrorCategory.COMMUNICATION
        val severity = when {
            error.message?.contains("timeout", ignoreCase = true) == true -> ErrorSeverity.MEDIUM
            error.message?.contains("connection", ignoreCase = true) == true -> ErrorSeverity.HIGH
            else -> ErrorSeverity.MEDIUM
        }

        return ScadaError(
            category = category,
            severity = severity,
            code = "COMMUNICATION_${protocol.name}_FAILED",
            message = "${protocol.name} communication error during $operation",
            userMessage = when (protocol) {
                CommunicationProtocol.BLUETOOTH_CLASSIC,
                CommunicationProtocol.BLUETOOTH_BLE ->
                    "Bluetooth connection failed. Please check device pairing and proximity."
                CommunicationProtocol.MQTT ->
                    "MQTT connection failed. Please check network connection and broker settings."
                CommunicationProtocol.TCP ->
                    "TCP connection failed. Please check network settings and server availability."
                else -> "Communication failed. Please check connection settings."
            },
            technicalDetails = error.message,
            recoveryActions = when (protocol) {
                CommunicationProtocol.BLUETOOTH_CLASSIC,
                CommunicationProtocol.BLUETOOTH_BLE -> listOf(
                    RecoveryAction(
                        title = "Check Bluetooth",
                        description = "Ensure Bluetooth is enabled and device is paired"
                    ),
                    RecoveryAction(
                        title = "Retry Connection",
                        description = "Attempt to reconnect to the device"
                    )
                )
                CommunicationProtocol.MQTT -> listOf(
                    RecoveryAction(
                        title = "Check Network",
                        description = "Verify internet connection and MQTT broker availability"
                    ),
                    RecoveryAction(
                        title = "Verify Settings",
                        description = "Check MQTT broker address and credentials"
                    )
                )
                else -> listOf(
                    RecoveryAction(
                        title = "Retry Operation",
                        description = "Attempt the operation again"
                    )
                )
            }
        )
    }

    /**
     * Handle validation errors
     */
    fun handleValidationError(validationResults: List<WorkspaceValidator.ValidationResult>): ScadaError {
        val errorCount = validationResults.count { !it.isValid && it.severity == WorkspaceValidator.Severity.ERROR }
        val warningCount = validationResults.count { it.severity == WorkspaceValidator.Severity.WARNING }

        return ScadaError(
            category = ErrorCategory.VALIDATION,
            severity = if (errorCount > 0) ErrorSeverity.HIGH else ErrorSeverity.MEDIUM,
            code = "VALIDATION_FAILED",
            message = "Workspace validation found $errorCount errors and $warningCount warnings",
            userMessage = if (errorCount > 0) {
                "Workspace contains errors that must be fixed before saving."
            } else {
                "Workspace has warnings that should be reviewed."
            },
            recoveryActions = listOf(
                RecoveryAction(
                    title = "Review Issues",
                    description = "Check the validation details for specific problems"
                ),
                RecoveryAction(
                    title = "Fix Errors",
                    description = "Address the validation errors listed"
                ),
                RecoveryAction(
                    title = "Save Anyway",
                    description = "Proceed with saving despite warnings",
                    action = null // This would be set by the caller
                )
            )
        )
    }

    /**
     * Handle component configuration errors
     */
    fun handleComponentConfigError(componentType: String, error: Throwable): ScadaError {
        Log.e(TAG, "Component configuration error for $componentType: ${error.message}", error)

        return ScadaError(
            category = ErrorCategory.CONFIGURATION,
            severity = ErrorSeverity.MEDIUM,
            code = "COMPONENT_CONFIG_FAILED",
            message = "Failed to configure $componentType component",
            userMessage = "Component configuration is invalid or incomplete.",
            technicalDetails = error.message,
            recoveryActions = listOf(
                RecoveryAction(
                    title = "Check Settings",
                    description = "Review component configuration settings"
                ),
                RecoveryAction(
                    title = "Reset Component",
                    description = "Reset component to default configuration"
                ),
                RecoveryAction(
                    title = "Delete Component",
                    description = "Remove the problematic component"
                )
            )
        )
    }

    /**
     * Handle runtime errors
     */
    fun handleRuntimeError(error: Throwable, context: String = ""): ScadaError {
        Log.e(TAG, "Runtime error in $context: ${error.message}", error)

        val category = determineErrorCategory(error)
        val severity = determineErrorSeverity(error)

        return ScadaError(
            category = category,
            severity = severity,
            code = "RUNTIME_ERROR",
            message = "Runtime error occurred${if (context.isNotEmpty()) " in $context" else ""}",
            userMessage = "An unexpected error occurred. The application may be in an inconsistent state.",
            technicalDetails = error.message,
            recoveryActions = listOf(
                RecoveryAction(
                    title = "Restart Application",
                    description = "Close and reopen the application"
                ),
                RecoveryAction(
                    title = "Reload Workspace",
                    description = "Reload the current workspace"
                ),
                RecoveryAction(
                    title = "Report Issue",
                    description = "Send error details for investigation"
                )
            )
        )
    }

    /**
     * Determine error category from exception
     */
    private fun determineErrorCategory(error: Throwable?): ErrorCategory {
        if (error == null) return ErrorCategory.UNKNOWN

        val message = error.message?.lowercase() ?: ""
        return when {
            message.contains("validation") || message.contains("invalid") -> ErrorCategory.VALIDATION
            message.contains("connection") || message.contains("network") || message.contains("timeout") -> ErrorCategory.NETWORK
            message.contains("permission") || message.contains("access") -> ErrorCategory.PERMISSION
            message.contains("database") || message.contains("storage") || message.contains("file") -> ErrorCategory.PERSISTENCE
            message.contains("communication") || message.contains("protocol") -> ErrorCategory.COMMUNICATION
            message.contains("config") -> ErrorCategory.CONFIGURATION
            else -> ErrorCategory.UNKNOWN
        }
    }

    /**
     * Determine error severity from exception
     */
    private fun determineErrorSeverity(error: Throwable?): ErrorSeverity {
        if (error == null) return ErrorSeverity.MEDIUM

        val message = error.message?.lowercase() ?: ""
        return when {
            message.contains("critical") || message.contains("fatal") -> ErrorSeverity.CRITICAL
            message.contains("permission") || message.contains("access denied") -> ErrorSeverity.HIGH
            message.contains("timeout") || message.contains("connection") -> ErrorSeverity.MEDIUM
            else -> ErrorSeverity.MEDIUM
        }
    }

    /**
     * Get user-friendly error message
     */
    fun getUserFriendlyMessage(error: ScadaError): String {
        return error.userMessage.ifEmpty {
            when (error.category) {
                ErrorCategory.VALIDATION -> "Please check your input and try again."
                ErrorCategory.COMMUNICATION -> "Communication error occurred. Please check your connection."
                ErrorCategory.PERSISTENCE -> "Storage operation failed. Please check available space."
                ErrorCategory.CONFIGURATION -> "Configuration error. Please review settings."
                ErrorCategory.RUNTIME -> "An unexpected error occurred."
                ErrorCategory.NETWORK -> "Network error. Please check your connection."
                ErrorCategory.PERMISSION -> "Permission error. Please grant required permissions."
                ErrorCategory.UNKNOWN -> "An unknown error occurred."
            }
        }
    }

    /**
     * Log error for debugging
     */
    fun logError(error: ScadaError) {
        val logLevel = when (error.severity) {
            ErrorSeverity.CRITICAL, ErrorSeverity.HIGH -> Log.ERROR
            ErrorSeverity.MEDIUM -> Log.WARN
            ErrorSeverity.LOW -> Log.INFO
        }

        Log.println(logLevel, TAG, """
            Error: ${error.code}
            Category: ${error.category}
            Severity: ${error.severity}
            Message: ${error.message}
            User Message: ${error.userMessage}
            Technical Details: ${error.technicalDetails}
            Recovery Actions: ${error.recoveryActions.size}
        """.trimIndent())
    }

    /**
     * Create error dialog content
     */
    fun createErrorDialogContent(error: ScadaError): ErrorDialogContent {
        return ErrorDialogContent(
            title = when (error.severity) {
                ErrorSeverity.CRITICAL -> "Critical Error"
                ErrorSeverity.HIGH -> "Error"
                ErrorSeverity.MEDIUM -> "Warning"
                ErrorSeverity.LOW -> "Notice"
            },
            message = getUserFriendlyMessage(error),
            technicalDetails = error.technicalDetails,
            recoveryActions = error.recoveryActions,
            canContinue = error.severity != ErrorSeverity.CRITICAL
        )
    }

    /**
     * Error dialog content structure
     */
    data class ErrorDialogContent(
        val title: String,
        val message: String,
        val technicalDetails: String? = null,
        val recoveryActions: List<RecoveryAction> = emptyList(),
        val canContinue: Boolean = true
    )
}