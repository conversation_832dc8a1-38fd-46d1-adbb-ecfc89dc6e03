package com.tfkcolin.cebsscada.scada.ui

/**
 * Validation utilities for workspace management
 */
object ValidationUtils {

    /**
     * Validate workspace name
     */
    fun validateWorkspaceName(name: String): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult.Error("Workspace name cannot be empty")
            name.length < 2 -> ValidationResult.Error("Workspace name must be at least 2 characters")
            name.length > 100 -> ValidationResult.Error("Workspace name cannot exceed 100 characters")
            !name.matches(Regex("^[a-zA-Z0-9\\s\\-_]+$")) ->
                ValidationResult.Error("Workspace name can only contain letters, numbers, spaces, hyphens, and underscores")
            else -> ValidationResult.Success
        }
    }

    /**
     * Validate workspace description
     */
    fun validateWorkspaceDescription(description: String): ValidationResult {
        return when {
            description.length > 500 -> ValidationResult.Error("Description cannot exceed 500 characters")
            else -> ValidationResult.Success
        }
    }

    /**
     * Validate component configuration
     */
    fun validateComponentConfig(config: Any?): ValidationResult {
        return when (config) {
            null -> ValidationResult.Error("Component configuration is required")
            else -> ValidationResult.Success // Add specific validation as needed
        }
    }
}

/**
 * Result of validation operation
 */
sealed class ValidationResult {
    object Success : ValidationResult()
    data class Error(val message: String) : ValidationResult()

    val isValid: Boolean get() = this is Success
    val errorMessage: String? get() = (this as? Error)?.message
}