package com.tfkcolin.cebsscada.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.ui.theme.*

/**
 * Adaptive layout components that respond to screen size and device characteristics
 */

/**
 * Adaptive container that provides appropriate padding and max width based on screen size
 */
@Composable
fun AdaptiveContainer(
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(layout.spacing.contentPadding),
        content = content
    )
}

/**
 * Adaptive grid that adjusts column count based on screen size
 */
@Composable
fun <T> AdaptiveGrid(
    items: List<T>,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    minItemWidth: androidx.compose.ui.unit.Dp = layout.sizing.cardMinWidth,
    itemContent: @Composable (T) -> Unit
) {
    LazyVerticalGrid(
        columns = GridCells.Adaptive(minItemWidth),
        modifier = modifier,
        contentPadding = layout.spacing.contentPadding,
        horizontalArrangement = Arrangement.spacedBy(layout.spacing.gridSpacing),
        verticalArrangement = Arrangement.spacedBy(layout.spacing.gridSpacing)
    ) {
        items(items) { item ->
            itemContent(item)
        }
    }
}

/**
 * Adaptive two-pane layout that switches between single and dual pane based on screen size
 */
@Composable
fun AdaptiveTwoPane(
    primaryContent: @Composable () -> Unit,
    secondaryContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    primaryWeight: Float = 1f,
    secondaryWeight: Float = 1f
) {
    if (layout.shouldUseSinglePane) {
        // Single pane layout for compact screens
        Column(modifier = modifier) {
            primaryContent()
        }
    } else {
        // Two pane layout for medium and expanded screens
        Row(modifier = modifier) {
            Box(
                modifier = Modifier.weight(primaryWeight)
            ) {
                primaryContent()
            }
            
            Spacer(modifier = Modifier.width(layout.spacing.medium))
            
            Box(
                modifier = Modifier.weight(secondaryWeight)
            ) {
                secondaryContent()
            }
        }
    }
}

/**
 * Adaptive three-pane layout for SCADA workspace
 */
@Composable
fun AdaptiveThreePane(
    leftContent: @Composable () -> Unit,
    centerContent: @Composable () -> Unit,
    rightContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    when {
        layout.shouldUseSinglePane -> {
            // Single pane - only show center content
            Box(modifier = modifier) {
                centerContent()
            }
        }
        layout.shouldUseTwoPane -> {
            // Two pane - left and center
            Row(modifier = modifier) {
                if (layout.sizing.paletteWidth > 0.dp) {
                    Box(
                        modifier = Modifier.width(layout.sizing.paletteWidth)
                    ) {
                        leftContent()
                    }
                    Spacer(modifier = Modifier.width(layout.spacing.medium))
                }
                
                Box(
                    modifier = Modifier.weight(1f)
                ) {
                    centerContent()
                }
            }
        }
        layout.shouldUseThreePane -> {
            // Three pane - left, center, right
            Row(modifier = modifier) {
                Box(
                    modifier = Modifier.width(layout.sizing.paletteWidth)
                ) {
                    leftContent()
                }
                
                Spacer(modifier = Modifier.width(layout.spacing.medium))
                
                Box(
                    modifier = Modifier.weight(1f)
                ) {
                    centerContent()
                }
                
                Spacer(modifier = Modifier.width(layout.spacing.medium))
                
                Box(
                    modifier = Modifier.width(layout.sizing.propertiesWidth)
                ) {
                    rightContent()
                }
            }
        }
    }
}

/**
 * Adaptive card that adjusts size and padding based on screen size
 */
@Composable
fun AdaptiveCard(
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    val cardModifier = if (onClick != null) {
        modifier.then(
            Modifier.widthIn(
                min = layout.sizing.cardMinWidth,
                max = layout.sizing.cardMaxWidth
            )
        )
    } else {
        modifier.then(
            Modifier.widthIn(
                min = layout.sizing.cardMinWidth,
                max = layout.sizing.cardMaxWidth
            )
        )
    }
    
    if (onClick != null) {
        Card(
            onClick = onClick,
            modifier = cardModifier,
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(layout.spacing.medium),
                content = content
            )
        }
    } else {
        Card(
            modifier = cardModifier,
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(layout.spacing.medium),
                content = content
            )
        }
    }
}

/**
 * Adaptive dialog that adjusts size based on screen size
 */
@Composable
fun AdaptiveDialog(
    onDismissRequest: () -> Unit,
    title: @Composable (() -> Unit)? = null,
    text: @Composable (() -> Unit)? = null,
    confirmButton: @Composable () -> Unit,
    dismissButton: @Composable (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout()
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = title,
        text = text,
        confirmButton = confirmButton,
        dismissButton = dismissButton,
        modifier = modifier.widthIn(max = layout.sizing.dialogMaxWidth)
    )
}

/**
 * Adaptive button that adjusts size based on screen size
 */
@Composable
fun AdaptiveButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    enabled: Boolean = true,
    colors: ButtonColors = ButtonDefaults.buttonColors(),
    content: @Composable RowScope.() -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(layout.sizing.buttonHeight),
        enabled = enabled,
        colors = colors,
        content = content
    )
}

/**
 * Adaptive icon button with proper touch target size
 */
@Composable
fun AdaptiveIconButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    enabled: Boolean = true,
    colors: IconButtonColors = IconButtonDefaults.iconButtonColors(),
    content: @Composable () -> Unit
) {
    IconButton(
        onClick = onClick,
        modifier = modifier.size(layout.sizing.minTouchTarget),
        enabled = enabled,
        colors = colors,
        content = content
    )
}

/**
 * Adaptive floating action button
 */
@Composable
fun AdaptiveFAB(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    layout: ResponsiveLayout = rememberResponsiveLayout(),
    containerColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.primaryContainer,
    contentColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.onPrimaryContainer,
    content: @Composable () -> Unit
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier.size(layout.sizing.fabSize),
        containerColor = containerColor,
        contentColor = contentColor,
        content = content
    )
}
