package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.ComponentPosition
import kotlinx.coroutines.launch

/**
 * Main SCADA Workspace Canvas with drag-and-drop functionality
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScadaWorkspace(
    components: List<ScadaComponent>,
    onComponentMoved: (ScadaComponent, ComponentPosition) -> Unit,
    onComponentSelected: (ScadaComponent?) -> Unit,
    selectedComponent: ScadaComponent? = null,
    modifier: Modifier = Modifier
) {
    val scope = rememberCoroutineScope()

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { offset ->
                        // Find component under drag start position
                        val component = components.find { component ->
                            component.containsPoint(offset.x, offset.y)
                        }
                        onComponentSelected(component)
                    },
                    onDrag = { change, dragAmount ->
                        selectedComponent?.let { component ->
                            val newX = (component.position.x + dragAmount.x).coerceAtLeast(0f)
                            val newY = (component.position.y + dragAmount.y).coerceAtLeast(0f)
                            val newPosition = ComponentPosition(newX, newY)
                            onComponentMoved(component, newPosition)
                        }
                    },
                    onDragEnd = {
                        // Drag ended
                    }
                )
            }
    ) {
        // Draw grid background
        Canvas(modifier = Modifier.fillMaxSize()) {
            val gridSize = 20f
            val width = size.width
            val height = size.height

            // Draw vertical lines
            for (x in 0..(width / gridSize).toInt()) {
                drawLine(
                    color = Color.LightGray,
                    start = Offset(x * gridSize, 0f),
                    end = Offset(x * gridSize, height),
                    strokeWidth = 1f
                )
            }

            // Draw horizontal lines
            for (y in 0..(height / gridSize).toInt()) {
                drawLine(
                    color = Color.LightGray,
                    start = Offset(0f, y * gridSize),
                    end = Offset(width, y * gridSize),
                    strokeWidth = 1f
                )
            }
        }

        // Render all components sorted by layer (lower layers first)
        components.sortedBy { it.layer }.forEach { component ->
            key(component.id) {
                ScadaComponentRenderer(
                    component = component,
                    isSelected = component == selectedComponent,
                    onComponentClick = { onComponentSelected(component) }
                )
            }
        }
    }
}

/**
 * Component factory for creating new components
 */
object ComponentFactory {
    private lateinit var dataBindingManager: com.tfkcolin.cebsscada.scada.DataBindingManager

    /**
     * Initialize the factory with required dependencies
     */
    fun initialize(dataBindingManager: com.tfkcolin.cebsscada.scada.DataBindingManager) {
        this.dataBindingManager = dataBindingManager
    }

    fun createComponent(
        type: com.tfkcolin.cebsscada.scada.ComponentType,
        position: ComponentPosition = ComponentPosition(100f, 100f)
    ): ScadaComponent? {
        return when (type) {
            com.tfkcolin.cebsscada.scada.ComponentType.PUSH_BUTTON ->
                com.tfkcolin.cebsscada.scada.components.PushButton(
                    dataBindingManager = dataBindingManager,
                    position = position,
                    size = com.tfkcolin.cebsscada.scada.ComponentSize(width = 120.dp, height = 40.dp)
                )
            com.tfkcolin.cebsscada.scada.ComponentType.VALUE_DISPLAY ->
                com.tfkcolin.cebsscada.scada.components.ValueDisplay(
                    position = position,
                    size = com.tfkcolin.cebsscada.scada.ComponentSize(width = 150.dp, height = 50.dp)
                )
            com.tfkcolin.cebsscada.scada.ComponentType.TOGGLE_SWITCH ->
                com.tfkcolin.cebsscada.scada.components.ToggleSwitch(
                    dataBindingManager = dataBindingManager,
                    position = position,
                    size = com.tfkcolin.cebsscada.scada.ComponentSize(width = 100.dp, height = 60.dp)
                )
            // TODO: Add other component types
            else -> null
        }
    }
}