package com.tfkcolin.cebsscada.ui.shared

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Shared Bluetooth scanner manager that preserves existing scanning functionality
 * Handles both Classical Bluetooth and BLE device discovery
 */
@Singleton
class BluetoothScannerManager @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) {
    companion object {
        private const val TAG = "BluetoothScannerManager"
        private const val CLASSIC_SCAN_TIMEOUT_MS = 30000L
        private const val BLE_SCAN_TIMEOUT_MS = 30000L
    }

    // Scanner components
    private var bluetoothLeScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    private var bleScanCallback: ScanCallback? = null
    private var classicBroadcastReceiver: BroadcastReceiver? = null

    // Coroutine scope for scanner operations
    private val scannerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // State flows
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()

    private val _scanType = MutableStateFlow(ScanType.NONE)
    val scanType: StateFlow<ScanType> = _scanType.asStateFlow()

    private val _discoveredDevices = MutableStateFlow<List<ScannedBluetoothDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<ScannedBluetoothDevice>> = _discoveredDevices.asStateFlow()

    private val _scanErrors = MutableSharedFlow<String>()
    val scanErrors: SharedFlow<String> = _scanErrors.asSharedFlow()

    // Device tracking
    private val deviceMap = mutableMapOf<String, ScannedBluetoothDevice>()

    /**
     * Start scanning for both Classical Bluetooth and BLE devices
     */
    suspend fun startScan(scanType: ScanType = ScanType.BOTH): Boolean {
        if (_isScanning.value) {
            Log.w(TAG, "Scan already in progress")
            return false
        }

        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
            _scanErrors.emit("Bluetooth is not enabled")
            return false
        }

        _isScanning.value = true
        _scanType.value = scanType
        deviceMap.clear()
        _discoveredDevices.value = emptyList()

        Log.d(TAG, "Starting Bluetooth scan: $scanType")

        var success = true

        try {
            when (scanType) {
                ScanType.CLASSIC -> success = startClassicScan()
                ScanType.BLE -> success = startBleScan()
                ScanType.BOTH -> {
                    val classicSuccess = startClassicScan()
                    val bleSuccess = startBleScan()
                    success = classicSuccess || bleSuccess
                }
                ScanType.NONE -> success = false
            }

            if (success) {
                // Set timeout for scan
                val timeoutMs = when (scanType) {
                    ScanType.CLASSIC -> CLASSIC_SCAN_TIMEOUT_MS
                    ScanType.BLE -> BLE_SCAN_TIMEOUT_MS
                    ScanType.BOTH -> maxOf(CLASSIC_SCAN_TIMEOUT_MS, BLE_SCAN_TIMEOUT_MS)
                    ScanType.NONE -> 0L
                }

                scannerScope.launch {
                    delay(timeoutMs)
                    if (_isScanning.value) {
                        Log.d(TAG, "Scan timeout reached, stopping scan")
                        stopScan()
                    }
                }
            } else {
                _isScanning.value = false
                _scanType.value = ScanType.NONE
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start scan: ${e.message}", e)
            _scanErrors.emit("Failed to start scan: ${e.message}")
            _isScanning.value = false
            _scanType.value = ScanType.NONE
            success = false
        }

        return success
    }

    /**
     * Stop all scanning operations
     */
    suspend fun stopScan() {
        if (!_isScanning.value) return

        Log.d(TAG, "Stopping Bluetooth scan")

        try {
            // Stop Classic Bluetooth discovery
            bluetoothAdapter?.cancelDiscovery()
            
            // Unregister broadcast receiver
            classicBroadcastReceiver?.let { receiver ->
                try {
                    context.unregisterReceiver(receiver)
                } catch (e: Exception) {
                    Log.w(TAG, "Error unregistering broadcast receiver: ${e.message}")
                }
                classicBroadcastReceiver = null
            }

            // Stop BLE scan
            bleScanCallback?.let { callback ->
                bluetoothLeScanner?.stopScan(callback)
                bleScanCallback = null
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping scan: ${e.message}", e)
        }

        _isScanning.value = false
        _scanType.value = ScanType.NONE
        
        Log.d(TAG, "Bluetooth scan stopped. Found ${deviceMap.size} devices")
    }

    /**
     * Clear discovered devices
     */
    fun clearDevices() {
        deviceMap.clear()
        _discoveredDevices.value = emptyList()
    }

    /**
     * Get bonded (paired) devices
     */
    fun getBondedDevices(): List<ScannedBluetoothDevice> {
        return bluetoothAdapter?.bondedDevices?.map { device ->
            ScannedBluetoothDevice(
                device = device,
                name = device.name ?: "Unknown",
                address = device.address,
                deviceType = BluetoothDeviceType.CLASSIC,
                rssi = null,
                isBonded = true,
                scanTime = System.currentTimeMillis()
            )
        } ?: emptyList()
    }

    /**
     * Start Classic Bluetooth discovery
     */
    private suspend fun startClassicScan(): Boolean = withContext(Dispatchers.Main) {
        try {
            Log.d(TAG, "Starting Classic Bluetooth discovery")

            // Register broadcast receiver for device discovery
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    when (intent.action) {
                        BluetoothDevice.ACTION_FOUND -> {
                            val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                            } else {
                                @Suppress("DEPRECATION")
                                intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                            }
                            
                            val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)
                            
                            device?.let { addDiscoveredDevice(it, BluetoothDeviceType.CLASSIC, rssi.toInt()) }
                        }
                        BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                            Log.d(TAG, "Classic Bluetooth discovery finished")
                        }
                    }
                }
            }

            val filter = IntentFilter().apply {
                addAction(BluetoothDevice.ACTION_FOUND)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
            }

            context.registerReceiver(receiver, filter)
            classicBroadcastReceiver = receiver

            // Start discovery
            val discoveryStarted = bluetoothAdapter?.startDiscovery() ?: false
            
            if (discoveryStarted) {
                Log.d(TAG, "Classic Bluetooth discovery started successfully")
            } else {
                Log.w(TAG, "Failed to start Classic Bluetooth discovery")
                context.unregisterReceiver(receiver)
                classicBroadcastReceiver = null
            }

            discoveryStarted

        } catch (e: Exception) {
            Log.e(TAG, "Error starting Classic Bluetooth scan: ${e.message}", e)
            false
        }
    }

    /**
     * Start BLE scan
     */
    private suspend fun startBleScan(): Boolean = withContext(Dispatchers.Main) {
        try {
            val scanner = bluetoothLeScanner
            if (scanner == null) {
                Log.w(TAG, "BLE scanner not available")
                return@withContext false
            }

            Log.d(TAG, "Starting BLE scan")

            val callback = object : ScanCallback() {
                override fun onScanResult(callbackType: Int, result: ScanResult) {
                    super.onScanResult(callbackType, result)
                    addDiscoveredDevice(result.device, BluetoothDeviceType.BLE, result.rssi)
                }

                override fun onBatchScanResults(results: MutableList<ScanResult>) {
                    super.onBatchScanResults(results)
                    results.forEach { result ->
                        addDiscoveredDevice(result.device, BluetoothDeviceType.BLE, result.rssi)
                    }
                }

                override fun onScanFailed(errorCode: Int) {
                    super.onScanFailed(errorCode)
                    Log.e(TAG, "BLE scan failed with error code: $errorCode")
                    scannerScope.launch {
                        _scanErrors.emit("BLE scan failed with error code: $errorCode")
                    }
                }
            }

            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()

            scanner.startScan(emptyList(), scanSettings, callback)
            bleScanCallback = callback

            Log.d(TAG, "BLE scan started successfully")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Error starting BLE scan: ${e.message}", e)
            false
        }
    }

    /**
     * Add discovered device to the list
     */
    private fun addDiscoveredDevice(device: BluetoothDevice, deviceType: BluetoothDeviceType, rssi: Int) {
        val scannedDevice = ScannedBluetoothDevice(
            device = device,
            name = device.name ?: "Unknown",
            address = device.address,
            deviceType = deviceType,
            rssi = rssi,
            isBonded = device.bondState == BluetoothDevice.BOND_BONDED,
            scanTime = System.currentTimeMillis()
        )

        // Update or add device
        val existingDevice = deviceMap[device.address]
        if (existingDevice == null || existingDevice.rssi == null || 
            (scannedDevice.rssi != null && scannedDevice.rssi > existingDevice.rssi)) {
            deviceMap[device.address] = scannedDevice
            _discoveredDevices.value = deviceMap.values.sortedByDescending { it.rssi ?: Int.MIN_VALUE }
            
            Log.d(TAG, "Added ${deviceType.name} device: ${scannedDevice.name} (${scannedDevice.address}) RSSI: ${scannedDevice.rssi}")
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        scannerScope.cancel()
        runBlocking {
            stopScan()
        }
    }
}

/**
 * Scan types
 */
enum class ScanType {
    NONE,
    CLASSIC,
    BLE,
    BOTH
}

/**
 * Bluetooth device types
 */
enum class BluetoothDeviceType {
    CLASSIC,
    BLE
}

/**
 * Scanned Bluetooth device information
 */
data class ScannedBluetoothDevice(
    val device: BluetoothDevice,
    val name: String,
    val address: String,
    val deviceType: BluetoothDeviceType,
    val rssi: Int?,
    val isBonded: Boolean,
    val scanTime: Long
)
