package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties

/**
 * Dialog for creating a new SCADA workspace
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceCreationDialog(
    onDismiss: () -> Unit,
    onCreateWorkspace: (name: String, description: String) -> Unit,
    isLoading: Boolean = false
) {
    var workspaceName by remember { mutableStateOf("") }
    var workspaceDescription by remember { mutableStateOf("") }
    var nameError by remember { mutableStateOf<String?>(null) }

    // Validate name when it changes
    LaunchedEffect(workspaceName) {
        nameError = when {
            workspaceName.isBlank() -> "Workspace name is required"
            workspaceName.length < 3 -> "Workspace name must be at least 3 characters"
            workspaceName.length > 50 -> "Workspace name must be less than 50 characters"
            else -> null
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = !isLoading,
            dismissOnClickOutside = !isLoading
        ),
        title = {
            Text("Create New Workspace")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Workspace name input
                OutlinedTextField(
                    value = workspaceName,
                    onValueChange = { workspaceName = it },
                    label = { Text("Workspace Name") },
                    placeholder = { Text("Enter workspace name") },
                    supportingText = nameError?.let { { Text(it) } },
                    isError = nameError != null,
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.Words,
                        imeAction = ImeAction.Next
                    ),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )

                // Workspace description input
                OutlinedTextField(
                    value = workspaceDescription,
                    onValueChange = { workspaceDescription = it },
                    label = { Text("Description (Optional)") },
                    placeholder = { Text("Enter workspace description") },
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Done
                    ),
                    minLines = 2,
                    maxLines = 4,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )

                // Info text
                Text(
                    text = "You can add SCADA components to your workspace and configure them to communicate with external devices.",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (nameError == null && workspaceName.isNotBlank()) {
                        onCreateWorkspace(workspaceName.trim(), workspaceDescription.trim())
                    }
                },
                enabled = nameError == null && workspaceName.isNotBlank() && !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("Create Workspace")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Dialog for loading an existing workspace
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceLoadDialog(
    workspaces: List<com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity>,
    onDismiss: () -> Unit,
    onLoadWorkspace: (workspaceId: Long) -> Unit,
    isLoading: Boolean = false
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = !isLoading,
            dismissOnClickOutside = !isLoading
        ),
        title = {
            Text("Load Workspace")
        },
        text = {
            if (workspaces.isEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "No workspaces found",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Create a new workspace to get started",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                ) {
                    Text(
                        text = "Select a workspace to load:",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Workspace list
                    workspaces.forEach { workspace ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            onClick = { onLoadWorkspace(workspace.id) },
                            enabled = !isLoading
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp)
                            ) {
                                Text(
                                    text = workspace.name,
                                    style = MaterialTheme.typography.titleSmall
                                )
                                if (workspace.description.isNotEmpty()) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = workspace.description,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        maxLines = 2
                                    )
                                }
                                Spacer(modifier = Modifier.height(4.dp))
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${workspace.componentCount} components",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Text(
                                        text = formatDate(workspace.modifiedAt),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Close")
            }
        }
    )
}

/**
 * Dialog for saving current workspace
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkspaceSaveDialog(
    currentWorkspaceId: Long? = null,
    onDismiss: () -> Unit,
    onSaveWorkspace: (name: String, description: String) -> Unit,
    onSaveAsNew: (name: String, description: String) -> Unit,
    isLoading: Boolean = false
) {
    var workspaceName by remember { mutableStateOf("") }
    var workspaceDescription by remember { mutableStateOf("") }
    var nameError by remember { mutableStateOf<String?>(null) }

    // Validate name when it changes
    LaunchedEffect(workspaceName) {
        nameError = when {
            workspaceName.isBlank() -> "Workspace name is required"
            workspaceName.length < 3 -> "Workspace name must be at least 3 characters"
            workspaceName.length > 50 -> "Workspace name must be less than 50 characters"
            else -> null
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = !isLoading,
            dismissOnClickOutside = !isLoading
        ),
        title = {
            Text(if (currentWorkspaceId != null) "Save Workspace" else "Save As New Workspace")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Workspace name input
                OutlinedTextField(
                    value = workspaceName,
                    onValueChange = { workspaceName = it },
                    label = { Text("Workspace Name") },
                    placeholder = { Text("Enter workspace name") },
                    supportingText = nameError?.let { { Text(it) } },
                    isError = nameError != null,
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.Words,
                        imeAction = ImeAction.Next
                    ),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )

                // Workspace description input
                OutlinedTextField(
                    value = workspaceDescription,
                    onValueChange = { workspaceDescription = it },
                    label = { Text("Description (Optional)") },
                    placeholder = { Text("Enter workspace description") },
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Done
                    ),
                    minLines = 2,
                    maxLines = 4,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (nameError == null && workspaceName.isNotBlank()) {
                        onSaveWorkspace(workspaceName.trim(), workspaceDescription.trim())
                    }
                },
                enabled = nameError == null && workspaceName.isNotBlank() && !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(if (currentWorkspaceId != null) "Save" else "Save As New")
            }
        },
        dismissButton = {
            Row {
                if (currentWorkspaceId != null) {
                    TextButton(
                        onClick = {
                            if (nameError == null && workspaceName.isNotBlank()) {
                                onSaveAsNew(workspaceName.trim(), workspaceDescription.trim())
                            }
                        },
                        enabled = nameError == null && workspaceName.isNotBlank() && !isLoading
                    ) {
                        Text("Save As New")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                }
                TextButton(
                    onClick = onDismiss,
                    enabled = !isLoading
                ) {
                    Text("Cancel")
                }
            }
        }
    )
}