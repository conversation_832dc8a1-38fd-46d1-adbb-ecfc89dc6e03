package com.tfkcolin.cebsscada.scada.persistence

import androidx.room.Database
import androidx.room.RoomDatabase

/**
 * Room database for SCADA workspace persistence
 */
@Database(
    entities = [WorkspaceEntity::class, ComponentEntity::class],
    version = 1,
    exportSchema = false
)
abstract class ScadaDatabase : RoomDatabase() {

    abstract fun workspaceDao(): WorkspaceDao
    abstract fun componentDao(): ComponentDao

    companion object {
        const val DATABASE_NAME = "scada_database"
    }
}