package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun MessageTestingTab(
    viewModel: MqttTestingViewModel,
    connectionState: ConnectionState,
    messages: List<CommunicationMessage>,
    subscriptions: List<TopicSubscription>
) {
    var messageText by remember { mutableStateOf("") }
    var topicText by remember { mutableStateOf("test/topic") }
    var subscriptionTopic by remember { mutableStateOf("") }
    
    Column {
        if (connectionState == ConnectionState.CONNECTED) {
            // Message sending section
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Send Test Message",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    OutlinedTextField(
                        value = topicText,
                        onValueChange = { topicText = it },
                        label = { Text("Topic") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        placeholder = { Text("test/topic") }
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    OutlinedTextField(
                        value = messageText,
                        onValueChange = { messageText = it },
                        label = { Text("Message") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3,
                        placeholder = { Text("Enter your test message...") }
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                if (messageText.isNotBlank() && topicText.isNotBlank()) {
                                    viewModel.sendText(messageText, topicText)
                                    messageText = ""
                                }
                            },
                            enabled = messageText.isNotBlank() && topicText.isNotBlank(),
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.AutoMirrored.Filled.Send, contentDescription = null)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Send")
                        }
                        
                        // Quick test messages dropdown
                        var showTestMessages by remember { mutableStateOf(false) }

                        OutlinedButton(
                            onClick = { showTestMessages = true },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Test Messages")
                        }

                        // Test messages dropdown menu
                        DropdownMenu(
                            expanded = showTestMessages,
                            onDismissRequest = { showTestMessages = false }
                        ) {
                            MqttTestScenarios.testMessages.forEach { testMsg ->
                                DropdownMenuItem(
                                    text = {
                                        Column {
                                            Text(
                                                text = testMsg.name,
                                                style = MaterialTheme.typography.bodyMedium,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Text(
                                                text = testMsg.description,
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                            )
                                        }
                                    },
                                    onClick = {
                                        messageText = testMsg.payload
                                        topicText = testMsg.topic
                                        showTestMessages = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Enhanced subscription management
            AdvancedSubscriptionManager(
                subscriptions = subscriptions,
                onSubscribe = { topic -> viewModel.subscribe(topic) },
                onUnsubscribe = { topic -> viewModel.unsubscribe(topic) },
                onBulkSubscribe = { topics ->
                    topics.forEach { topic -> viewModel.subscribe(topic) }
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Enhanced message history
        EnhancedMessageHistory(
            messages = messages,
            onClearMessages = { viewModel.clearMessages() },
            onExportMessages = { /* TODO: Implement export functionality */ }
        )
    }
}

@Composable
fun MessageItem(message: CommunicationMessage) {
    val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val timeString = dateFormat.format(Date(message.timestamp))
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (message.direction.name == "OUTGOING") {
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            } else {
                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = message.topic ?: "No topic",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = timeString,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Text(
                text = message.contentAsString,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

@Composable
fun DiagnosticsTab(
    viewModel: MqttTestingViewModel,
    connectionState: ConnectionState,
    connectedDevice: CommunicationDevice?
) {
    val connectionStats = viewModel.getConnectionStats()
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Enhanced broker status display
        EnhancedBrokerStatusCard(
            broker = connectedDevice as? com.tfkcolin.cebsscada.communication.models.MqttBroker,
            connectionState = connectionState,
            connectionStats = mapOf(
                "messagesSent" to connectionStats.messagesSent,
                "messagesReceived" to connectionStats.messagesReceived,
                "totalMessages" to connectionStats.totalMessages,
                "subscriptions" to connectionStats.subscriptions
            ),
            onReconnect = { /* TODO: Implement reconnect */ },
            onDisconnect = { /* TODO: Implement disconnect */ }
        )

        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Connection Diagnostics",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Network information
                DiagnosticItem("Network Status", "Connected")
                DiagnosticItem("Last Ping", "< 50ms")
                DiagnosticItem("Connection Quality", "Excellent")

                Spacer(modifier = Modifier.height(8.dp))

                // Connection statistics
                DiagnosticItem("Messages Sent", connectionStats.messagesSent.toString())
                DiagnosticItem("Messages Received", connectionStats.messagesReceived.toString())
                DiagnosticItem("Total Messages", connectionStats.totalMessages.toString())
                DiagnosticItem("Active Subscriptions", connectionStats.subscriptions.toString())
            }
        }
    }
}

@Composable
fun DiagnosticItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun TestScenariosTab(
    viewModel: MqttTestingViewModel,
    connectionState: ConnectionState
) {
    var selectedScenario by remember { mutableStateOf<TestSequence?>(null) }
    var currentStepIndex by remember { mutableIntStateOf(0) }
    var isRunningTest by remember { mutableStateOf(false) }

    Column {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Automated Test Scenarios",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Run predefined test sequences to validate MQTT functionality",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (selectedScenario == null) {
            // Scenario selection
            Text(
                text = "Available Test Scenarios",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                MqttTestScenarios.testSequences.forEach { scenario ->
                    TestScenarioCard(
                        scenario = scenario,
                        enabled = connectionState == ConnectionState.CONNECTED,
                        onSelect = {
                            selectedScenario = scenario
                            currentStepIndex = 0
                        }
                    )
                }
            }
        } else {
            // Running scenario
            selectedScenario?.let { scenario ->
                TestScenarioRunner(
                    scenario = scenario,
                currentStepIndex = currentStepIndex,
                isRunning = isRunningTest,
                onStepComplete = {
                    selectedScenario?.let { scenario ->
                        if (currentStepIndex < scenario.steps.size - 1) {
                            currentStepIndex++
                        } else {
                            isRunningTest = false
                        }
                    }
                },
                onStart = { isRunningTest = true },
                onStop = {
                    isRunningTest = false
                    currentStepIndex = 0
                },
                onBack = {
                    selectedScenario = null
                    currentStepIndex = 0
                    isRunningTest = false
                },
                viewModel = viewModel
            )
            }
        }
    }
}
