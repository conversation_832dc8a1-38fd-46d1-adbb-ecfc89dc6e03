package com.tfkcolin.cebsscada.scada.components

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentSize
import com.tfkcolin.cebsscada.scada.ComponentType
import java.util.UUID

/**
 * Value Display SCADA Component
 * Shows sensor readings or data from communication protocols
 */
class ValueDisplay(
    id: String = UUID.randomUUID().toString(),
    position: ComponentPosition = ComponentPosition(),
    size: ComponentSize = ComponentSize(),
    config: ValueDisplayConfig = ValueDisplayConfig()
) : BaseScadaComponent<ValueDisplayConfig>(
    id = id,
    type = ComponentType.VALUE_DISPLAY,
    initialPosition = position,
    initialSize = size,
    initialConfig = config
) {

    // Current displayed value
    private val _currentValue: MutableState<String> = mutableStateOf(getTypedConfig().defaultText)

    val currentValue: String
        get() = _currentValue.value

    /**
     * Update the displayed value
     * This would be called by the data binding system when new data arrives
     */
    fun updateValue(newValue: String) {
        _currentValue.value = formatValue(newValue)
    }

    /**
     * Update the displayed value with a number
     */
    fun updateValue(newValue: Number) {
        val config = getTypedConfig()
        var formatted = when (config.dataBinding.dataFormat) {
            com.tfkcolin.cebsscada.scada.DataFormat.INTEGER -> newValue.toInt().toString()
            com.tfkcolin.cebsscada.scada.DataFormat.FLOAT -> String.format("%.${config.dataBinding.decimalPlaces}f", newValue.toDouble())
            else -> newValue.toString()
        }

        // Add prefix and suffix
        formatted = "${config.dataBinding.prefix}$formatted${config.dataBinding.suffix}"

        _currentValue.value = formatted
    }

    /**
     * Format raw string value according to configuration
     */
    private fun formatValue(rawValue: String): String {
        val config = getTypedConfig()
        var formatted = rawValue

        // Add prefix and suffix
        formatted = "${config.dataBinding.prefix}$formatted${config.dataBinding.suffix}"

        return formatted
    }

    /**
     * Reset to default text
     */
    fun resetToDefault() {
        _currentValue.value = getTypedConfig().defaultText
    }
}