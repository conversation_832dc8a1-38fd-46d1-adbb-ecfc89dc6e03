package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.tfkcolin.cebsscada.scada.WorkspaceValidator

/**
 * Dialog showing workspace validation results
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ValidationResultsDialog(
    validationResults: List<WorkspaceValidator.ValidationResult>,
    onDismiss: () -> Unit,
    onProceed: () -> Unit,
    onFixIssues: (() -> Unit)? = null,
    title: String = "Validation Results"
) {
    val summary = WorkspaceValidator.getValidationSummary(validationResults)

    AlertDialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .fillMaxHeight(0.8f),
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(title)
                ValidationStatusIcon(summary)
            }
        },
        text = {
            Column(modifier = Modifier.fillMaxSize()) {
                // Summary header
                ValidationSummaryHeader(summary)

                Spacer(modifier = Modifier.height(16.dp))

                // Results list
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(validationResults) { result ->
                        ValidationResultItem(result)
                    }
                }
            }
        },
        confirmButton = {
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                if (summary.canProceedWithWarnings || summary.isValid) {
                    TextButton(onClick = onProceed) {
                        Text("Proceed")
                    }
                }

                if (onFixIssues != null && !summary.isValid) {
                    TextButton(onClick = onFixIssues) {
                        Text("Fix Issues")
                    }
                }

                TextButton(onClick = onDismiss) {
                    Text("Close")
                }
            }
        }
    )
}

/**
 * Validation status icon
 */
@Composable
private fun ValidationStatusIcon(summary: WorkspaceValidator.ValidationSummary) {
    when {
        summary.errorCount > 0 -> {
            Icon(
                Icons.Default.Error,
                contentDescription = "Errors found",
                tint = MaterialTheme.colorScheme.error
            )
        }
        summary.warningCount > 0 -> {
            Icon(
                Icons.Default.Warning,
                contentDescription = "Warnings found",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        else -> {
            Icon(
                Icons.Default.CheckCircle,
                contentDescription = "All valid",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * Validation summary header
 */
@Composable
private fun ValidationSummaryHeader(summary: WorkspaceValidator.ValidationSummary) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                summary.errorCount > 0 -> MaterialTheme.colorScheme.errorContainer
                summary.warningCount > 0 -> MaterialTheme.colorScheme.primaryContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = when {
                    summary.errorCount > 0 -> "Validation Failed"
                    summary.warningCount > 0 -> "Validation Passed with Warnings"
                    else -> "Validation Passed"
                },
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                if (summary.errorCount > 0) {
                    ValidationCountItem(
                        count = summary.errorCount,
                        label = "Error${if (summary.errorCount != 1) "s" else ""}",
                        color = MaterialTheme.colorScheme.error
                    )
                }

                if (summary.warningCount > 0) {
                    ValidationCountItem(
                        count = summary.warningCount,
                        label = "Warning${if (summary.warningCount != 1) "s" else ""}",
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                if (summary.infoCount > 0) {
                    ValidationCountItem(
                        count = summary.infoCount,
                        label = "Info",
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            if (summary.errorCount > 0) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Please fix the errors before proceeding.",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            } else if (summary.warningCount > 0) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "You can proceed with warnings, but consider addressing them for better results.",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Validation count item
 */
@Composable
private fun ValidationCountItem(count: Int, label: String, color: Color) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color, shape = MaterialTheme.shapes.small)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = "$count $label",
            style = MaterialTheme.typography.bodySmall,
            color = color
        )
    }
}

/**
 * Individual validation result item
 */
@Composable
private fun ValidationResultItem(result: WorkspaceValidator.ValidationResult) {
    val (icon, iconTint) = when (result.severity) {
        WorkspaceValidator.Severity.ERROR -> Icons.Default.Error to MaterialTheme.colorScheme.error
        WorkspaceValidator.Severity.WARNING -> Icons.Default.Warning to MaterialTheme.colorScheme.primary
        WorkspaceValidator.Severity.INFO -> Icons.Default.Info to MaterialTheme.colorScheme.onSurfaceVariant
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (result.severity) {
                WorkspaceValidator.Severity.ERROR -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                WorkspaceValidator.Severity.WARNING -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                WorkspaceValidator.Severity.INFO -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            }
        ),
        border = CardDefaults.outlinedCardBorder(
            enabled = !result.isValid
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                icon,
                contentDescription = result.severity.name,
                tint = iconTint,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = result.message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = when (result.severity) {
                        WorkspaceValidator.Severity.ERROR -> MaterialTheme.colorScheme.error
                        WorkspaceValidator.Severity.WARNING -> MaterialTheme.colorScheme.primary
                        WorkspaceValidator.Severity.INFO -> MaterialTheme.colorScheme.onSurface
                    }
                )

                if (result.field != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Field: ${result.field}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (result.suggestion != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Suggestion: ${result.suggestion}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * Quick validation dialog for simple validation feedback
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickValidationDialog(
    title: String,
    message: String,
    severity: WorkspaceValidator.Severity,
    onDismiss: () -> Unit,
    onProceed: (() -> Unit)? = null
) {
    val (icon, iconTint) = when (severity) {
        WorkspaceValidator.Severity.ERROR -> Icons.Default.Error to MaterialTheme.colorScheme.error
        WorkspaceValidator.Severity.WARNING -> Icons.Default.Warning to MaterialTheme.colorScheme.primary
        WorkspaceValidator.Severity.INFO -> Icons.Default.Info to MaterialTheme.colorScheme.primary
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                icon,
                contentDescription = severity.name,
                tint = iconTint
            )
        },
        title = { Text(title) },
        text = { Text(message) },
        confirmButton = {
            if (onProceed != null) {
                TextButton(onClick = onProceed) {
                    Text("Proceed")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(if (onProceed != null) "Cancel" else "OK")
            }
        }
    )
}