package com.tfkcolin.cebsscada.ui.classical

import android.bluetooth.BluetoothDevice
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.services.ClassicalBluetoothService
import com.tfkcolin.cebsscada.services.ClassicalBluetoothConnectionState
import com.tfkcolin.cebsscada.services.ClassicalBluetoothData
import com.tfkcolin.cebsscada.services.ClassicalBluetoothStats
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager
import com.tfkcolin.cebsscada.ui.shared.BluetoothScannerManager
import com.tfkcolin.cebsscada.ui.shared.ScannedBluetoothDevice
import com.tfkcolin.cebsscada.ui.shared.ScanType
import com.tfkcolin.cebsscada.ui.shared.BluetoothDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Classical Bluetooth screen
 * Manages Classical Bluetooth connections, scanning, and communication
 */
@HiltViewModel
class ClassicalBluetoothViewModel @Inject constructor(
    private val classicalBluetoothService: ClassicalBluetoothService,
    private val bluetoothScannerManager: BluetoothScannerManager,
    private val bluetoothPermissionManager: BluetoothPermissionManager
) : ViewModel() {

    // Permission state
    val hasPermissions = bluetoothPermissionManager.hasAllPermissions
    val permissionState = bluetoothPermissionManager.permissionState

    // Connection state
    val connectionState = classicalBluetoothService.connectionState
    val connectedDevice = classicalBluetoothService.connectedDeviceFlow
    val receivedData = classicalBluetoothService.receivedData
    val connectionErrors = classicalBluetoothService.connectionErrors
    val reconnectAttempts = classicalBluetoothService.reconnectAttempts

    private val _connectingDevice = MutableStateFlow<BluetoothDevice?>(null)
    val connectingDevice: StateFlow<BluetoothDevice?> = _connectingDevice.asStateFlow()

    // Scanning state
    val isScanning = bluetoothScannerManager.isScanning
    val discoveredDevices = bluetoothScannerManager.discoveredDevices
        .map { devices -> devices.filter { it.deviceType == BluetoothDeviceType.CLASSIC } }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    val scanErrors = bluetoothScannerManager.scanErrors

    // UI state
    private val _messages = MutableStateFlow<List<ClassicalBluetoothMessage>>(emptyList())
    val messages: StateFlow<List<ClassicalBluetoothMessage>> = _messages.asStateFlow()

    private val _selectedTab = MutableStateFlow(0)
    val selectedTab: StateFlow<Int> = _selectedTab.asStateFlow()

    init {
        // Check permissions on initialization
        bluetoothPermissionManager.checkPermissions()

        // Collect connection state changes
        viewModelScope.launch {
            classicalBluetoothService.connectionState.collect { state ->
                if (state == ClassicalBluetoothConnectionState.CONNECTED ||
                    state == ClassicalBluetoothConnectionState.DISCONNECTED ||
                    state == ClassicalBluetoothConnectionState.ERROR) {
                    _connectingDevice.value = null
                }
            }
        }

        // Collect received data and convert to messages
        viewModelScope.launch {
            receivedData.collect { data ->
                addMessage(
                    ClassicalBluetoothMessage(
                        content = data.text,
                        timestamp = data.timestamp,
                        isOutgoing = false,
                        deviceAddress = data.deviceAddress
                    )
                )
            }
        }

        // Collect connection errors
        viewModelScope.launch {
            connectionErrors.collect { error ->
                // Handle connection errors (could show snackbar, etc.)
            }
        }
    }

    /**
     * Start scanning for Classical Bluetooth devices
     */
    fun startScan() {
        if (!bluetoothPermissionManager.checkPermissions()) {
            return
        }

        viewModelScope.launch {
            bluetoothScannerManager.startScan(ScanType.CLASSIC)
        }
    }

    /**
     * Stop scanning
     */
    fun stopScan() {
        viewModelScope.launch {
            bluetoothScannerManager.stopScan()
        }
    }

    /**
     * Connect to a device
     */
    fun connect(scannedDevice: ScannedBluetoothDevice) {
        viewModelScope.launch {
            _connectingDevice.value = scannedDevice.device
            classicalBluetoothService.connect(scannedDevice.device)
        }
    }

    /**
     * Disconnect from current device
     */
    fun disconnect() {
        viewModelScope.launch {
            classicalBluetoothService.disconnect()
        }
    }

    fun cancelReconnect() {
        classicalBluetoothService.cancelReconnect()
    }

    /**
     * Send text message
     */
    fun sendText(text: String) {
        viewModelScope.launch {
            val success = classicalBluetoothService.sendText(text)
            if (success) {
                addMessage(
                    ClassicalBluetoothMessage(
                        content = text,
                        timestamp = System.currentTimeMillis(),
                        isOutgoing = true,
                        deviceAddress = connectedDevice.value?.address ?: "Unknown"
                    )
                )
            }
        }
    }

    /**
     * Send AT command
     */
    fun sendATCommand(command: String) {
        viewModelScope.launch {
            val success = classicalBluetoothService.sendATCommand(command)
            if (success) {
                val fullCommand = if (command.startsWith("AT")) command else "AT+$command"
                addMessage(
                    ClassicalBluetoothMessage(
                        content = "$fullCommand\r\n",
                        timestamp = System.currentTimeMillis(),
                        isOutgoing = true,
                        deviceAddress = connectedDevice.value?.address ?: "Unknown"
                    )
                )
            }
        }
    }

    /**
     * Send SCADA command
     */
    fun sendSCADACommand(command: String) {
        viewModelScope.launch {
            val success = classicalBluetoothService.sendSCADACommand(command)
            if (success) {
                addMessage(
                    ClassicalBluetoothMessage(
                        content = "$command\n",
                        timestamp = System.currentTimeMillis(),
                        isOutgoing = true,
                        deviceAddress = connectedDevice.value?.address ?: "Unknown"
                    )
                )
            }
        }
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): ClassicalBluetoothStats {
        return classicalBluetoothService.getConnectionStats()
    }

    /**
     * Set auto-reconnect enabled
     */
    fun setAutoReconnectEnabled(enabled: Boolean) {
        classicalBluetoothService.setAutoReconnectEnabled(enabled)
    }

    /**
     * Clear messages
     */
    fun clearMessages() {
        _messages.value = emptyList()
    }

    /**
     * Set selected tab
     */
    fun setSelectedTab(tabIndex: Int) {
        _selectedTab.value = tabIndex
    }

    /**
     * Get bonded devices
     */
    fun getBondedDevices(): List<ScannedBluetoothDevice> {
        return bluetoothScannerManager.getBondedDevices()
            .filter { it.deviceType == BluetoothDeviceType.CLASSIC }
    }

    /**
     * Clear discovered devices
     */
    fun clearDevices() {
        bluetoothScannerManager.clearDevices()
    }

    /**
     * Add message to the list
     */
    private fun addMessage(message: ClassicalBluetoothMessage) {
        val currentMessages = _messages.value.toMutableList()
        currentMessages.add(message)
        
        // Keep only last 1000 messages to prevent memory issues
        if (currentMessages.size > 1000) {
            currentMessages.removeAt(0)
        }
        
        _messages.value = currentMessages
    }

    /**
     * Predefined SCADA commands
     */
    fun getCommonSCADACommands(): List<SCADACommand> {
        return listOf(
            SCADACommand("READ_TEMP", "Read Temperature", "Get current temperature reading"),
            SCADACommand("READ_PRES", "Read Pressure", "Get current pressure reading"),
            SCADACommand("READ_FLOW", "Read Flow Rate", "Get current flow rate reading"),
            SCADACommand("READ_LEVL", "Read Level", "Get current level reading"),
            SCADACommand("VALVE_OPEN", "Open Valve", "Open the control valve"),
            SCADACommand("VALVE_CLOSE", "Close Valve", "Close the control valve"),
            SCADACommand("PUMP_START", "Start Pump", "Start the pump motor"),
            SCADACommand("PUMP_STOP", "Stop Pump", "Stop the pump motor"),
            SCADACommand("ALARM_ACK", "Acknowledge Alarm", "Acknowledge active alarms"),
            SCADACommand("STATUS", "Get Status", "Get system status")
        )
    }

    /**
     * Predefined AT commands for HC-05/HC-06
     */
    fun getCommonATCommands(): List<ATCommand> {
        return listOf(
            ATCommand("AT", "Test Connection", "Test if module responds"),
            ATCommand("AT+VERSION?", "Get Version", "Get firmware version"),
            ATCommand("AT+NAME?", "Get Name", "Get device name"),
            ATCommand("AT+ADDR?", "Get Address", "Get device address"),
            ATCommand("AT+ROLE?", "Get Role", "Get device role (master/slave)"),
            ATCommand("AT+PSWD?", "Get Password", "Get pairing password"),
            ATCommand("AT+UART?", "Get UART", "Get UART configuration"),
            ATCommand("AT+STATE?", "Get State", "Get connection state"),
            ATCommand("AT+RESET", "Reset", "Reset the module")
        )
    }

    override fun onCleared() {
        super.onCleared()
        // Service cleanup is handled by dependency injection
    }
}

/**
 * Classical Bluetooth message for UI display
 */
data class ClassicalBluetoothMessage(
    val content: String,
    val timestamp: Long,
    val isOutgoing: Boolean,
    val deviceAddress: String
)

/**
 * SCADA command definition
 */
data class SCADACommand(
    val command: String,
    val name: String,
    val description: String
)

/**
 * AT command definition
 */
data class ATCommand(
    val command: String,
    val name: String,
    val description: String
)
