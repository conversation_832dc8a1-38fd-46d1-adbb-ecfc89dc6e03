package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresPermission
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class BluetoothViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : AndroidViewModel(context.applicationContext as Application), BLEScanner.BLEScanCallback {
    private val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private val bleScanner: BLEScanner = BLEScanner(context) { debugLog ->
        addDebugLog(debugLog)
    }.apply {
        bleScanCallback = this@BluetoothViewModel
    }
    private var bluetoothService: BluetoothService? = null

    private val _bluetoothState = mutableStateOf<BluetoothState>(BluetoothState.Idle)
    val bluetoothState: State<BluetoothState> = _bluetoothState

    private val _discoveredDevices = mutableStateListOf<BluetoothDeviceWrapper>()
    val discoveredDevices: List<BluetoothDeviceWrapper> = _discoveredDevices

    private val _gattServices = mutableStateListOf<BluetoothGattService>()
    val gattServices: List<BluetoothGattService> = _gattServices

    private val _messages = mutableStateListOf<String>()
    val messages: List<String> = _messages

    // HC-05/HC-06 data reception handler
    private val dataReceivedCallback = { data: ByteArray ->
        // Update connection health timestamp for industrial monitoring
        // Note: This tracks data reception in ViewModel for UI purposes

        // For SCADA systems, handle both text and binary data appropriately
        val hexString = data.joinToString(" ") { "%02X".format(it) }

        // Attempt to decode as UTF-8, but handle invalid sequences gracefully
        val dataString = try {
            // Check if data contains only printable ASCII characters (common for SCADA text)
            val isPrintableAscii = data.all { byte ->
                byte.toInt() >= 32 || byte.toInt() == 9 || byte.toInt() == 10 || byte.toInt() == 13 // printable chars, tab, LF, CR
            }
            if (isPrintableAscii && data.isNotEmpty()) {
                String(data, Charsets.UTF_8)
            } else {
                "[Binary data: ${data.size} bytes]"
            }
        } catch (e: Exception) {
            // Fallback for any encoding issues
            "[Binary data: ${data.size} bytes]"
        }

        addDebugLog("HC-05/HC-06 data received: '$dataString' (hex: $hexString)")

        // Display both string and hex for comprehensive SCADA monitoring
        val displayText = if (dataString.startsWith("[Binary data:")) {
            "← $dataString | HEX: $hexString"
        } else {
            "← $dataString"
        }

        _messages.add(displayText)
        if (_messages.size > 100) { // Keep only last 100 messages for SCADA
            _messages.removeAt(0)
        }
    }

    private val _debugLogs = mutableStateListOf<String>()
    val debugLogs: List<String> = _debugLogs

    private val _isScanning = mutableStateOf(false)
    val isScanning: State<Boolean> = _isScanning

    private val _scanType = mutableStateOf(ScanType.NONE)
    val scanType: State<ScanType> = _scanType

    private val _isBluetoothEnabled = mutableStateOf(false)
    val isBluetoothEnabled: State<Boolean> = _isBluetoothEnabled

    // Handler for scan timeout
    private val scanTimeoutHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private val scanTimeoutRunnable = Runnable {
        if (_isScanning.value) {
            addDebugLog("Scan timeout reached, stopping scan")
            stopScan()
        }
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as BluetoothService.LocalBinder
            bluetoothService = binder.getService()
            bluetoothService?.initialize()
            // Set up data reception callback for HC-05/HC-06 communication
            bluetoothService?.onDataReceived = dataReceivedCallback
            // Set up BLE services discovered callback
            bluetoothService?.onBleServicesDiscovered = { services ->
                _gattServices.clear()
                _gattServices.addAll(services)
                addDebugLog("BLE services discovered and populated: ${services.size} services")
            }
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            bluetoothService = null
        }
    }

    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            addDebugLog("Received system broadcast: ${intent?.action}")
            when (intent?.action) {
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                    addDebugLog("Bluetooth state changed (system): $state")
                    when (state) {
                        BluetoothAdapter.STATE_ON -> {
                            _isBluetoothEnabled.value = true
                            _bluetoothState.value = BluetoothState.Idle
                            addDebugLog("Bluetooth enabled via system broadcast - status updated to 'Ready to connect'")
                        }
                        BluetoothAdapter.STATE_OFF -> {
                            _isBluetoothEnabled.value = false
                            _bluetoothState.value = BluetoothState.Idle
                            addDebugLog("Bluetooth disabled via system broadcast")
                        }
                        BluetoothAdapter.STATE_TURNING_ON -> {
                            addDebugLog("Bluetooth turning on (system broadcast)")
                        }
                        BluetoothAdapter.STATE_TURNING_OFF -> {
                            addDebugLog("Bluetooth turning off (system broadcast)")
                        }
                    }
                }
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    addDebugLog("RECEIVED: Classic Bluetooth discovery finished (system broadcast)")
                    addDebugLog("Discovery completion details:")
                    addDebugLog("- Was scanning: ${_isScanning.value}")
                    addDebugLog("- Scan type: ${_scanType.value}")
                    addDebugLog("- Devices found: ${_discoveredDevices.size}")

                    _isScanning.value = false
                    _scanType.value = ScanType.NONE

                    // Cancel timeout since discovery finished normally
                    scanTimeoutHandler.removeCallbacks(scanTimeoutRunnable)

                    addDebugLog("Classic Bluetooth discovery completed successfully")
                }
                BluetoothDevice.ACTION_FOUND -> {
                    addDebugLog("RECEIVED: Classic Bluetooth device found (system broadcast)")

                    val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE).toInt()
                    val deviceClass = intent.getIntExtra(BluetoothDevice.EXTRA_CLASS, 0)
                    val deviceName = intent.getStringExtra(BluetoothDevice.EXTRA_NAME)

                    addDebugLog("Device discovery details:")
                    addDebugLog("- Device: ${device?.address}")
                    addDebugLog("- Name from intent: $deviceName")
                    addDebugLog("- Name from device: ${device?.name}")
                    addDebugLog("- RSSI: $rssi")
                    addDebugLog("- Device class: $deviceClass")

                    device?.let {
                        val finalName = deviceName ?: it.name ?: "Unknown"
                        addDebugLog("Processing discovered device: $finalName (${it.address})")

                        val wrapper = BluetoothDeviceWrapper(
                            device = it,
                            name = finalName,
                            address = it.address,
                            type = DeviceType.CLASSIC,
                            rssi = if (rssi == Short.MIN_VALUE.toInt()) null else rssi,
                            isBonded = it.bondState == BluetoothDevice.BOND_BONDED
                        )

                        if (!_discoveredDevices.any { d -> d.address == wrapper.address }) {
                            _discoveredDevices.add(wrapper)
                            addDebugLog("✅ Added Classic Bluetooth device to list: ${wrapper.name} (${wrapper.address})")
                            addDebugLog("Total devices in list: ${_discoveredDevices.size}")
                        } else {
                            addDebugLog("⚠️ Classic Bluetooth device already in list, skipping duplicate: ${wrapper.address}")
                        }
                    } ?: addDebugLog("❌ ERROR: Device parcelable is null in ACTION_FOUND")
                }
            }
        }
    }

    init {
        _isBluetoothEnabled.value = bluetoothAdapter?.isEnabled == true
        val filter = IntentFilter().apply {
            // System Bluetooth broadcasts only
            addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothDevice.ACTION_FOUND)
        }
        ContextCompat.registerReceiver(context, bluetoothReceiver, filter, ContextCompat.RECEIVER_NOT_EXPORTED)
        Intent(context, BluetoothService::class.java).also { intent ->
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }

    override fun onCleared() {
        super.onCleared()
        context.unregisterReceiver(bluetoothReceiver)
        context.unbindService(serviceConnection)
        stopScan()
    }

    fun startScan(scanType: ScanType) {
        addDebugLog("Starting scan with type: $scanType")

        // Check if Bluetooth is enabled first
        if (!isBluetoothEnabled.value) {
            addDebugLog("Cannot start scan - Bluetooth is not enabled")
            return
        }

        if (_isScanning.value) {
            addDebugLog("Scan already in progress, ignoring request")
            return
        }

        _discoveredDevices.clear()
        _isScanning.value = true
        _scanType.value = scanType

        when (scanType) {
            ScanType.BLE -> {
                addDebugLog("Starting BLE scan")

                // Check permissions required for BLE scanning based on API level
                val blePermissionsGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // Android 12+: BLUETOOTH_SCAN required
                    val hasScanPermission = ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.BLUETOOTH_SCAN
                    ) == PackageManager.PERMISSION_GRANTED
                    val hasLocationPermission = ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED

                    if (!hasScanPermission) {
                        addDebugLog("ERROR: Missing BLUETOOTH_SCAN permission (required for BLE on Android 12+)")
                        _isScanning.value = false
                        _scanType.value = ScanType.NONE
                        return
                    }
                    if (!hasLocationPermission) {
                        addDebugLog("ERROR: Missing ACCESS_FINE_LOCATION permission (required for BLE)")
                        _isScanning.value = false
                        _scanType.value = ScanType.NONE
                        return
                    }
                    true
                } else {
                    // Android 6-11: ACCESS_FINE_LOCATION required
                    val hasLocationPermission = ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED

                    if (!hasLocationPermission) {
                        addDebugLog("ERROR: Missing ACCESS_FINE_LOCATION permission (required for BLE on Android 6-11)")
                        _isScanning.value = false
                        _scanType.value = ScanType.NONE
                        return
                    }
                    true
                }

                if (blePermissionsGranted) {
                    try {
                        bleScanner.startScan()
                        addDebugLog("BLE scan started successfully")
                    } catch (e: Exception) {
                        addDebugLog("Failed to start BLE scan: ${e.message}")
                        _isScanning.value = false
                        _scanType.value = ScanType.NONE
                    }
                }
            }
            ScanType.CLASSIC -> {
                addDebugLog("Starting Classic Bluetooth discovery")

                // Comprehensive checks before starting discovery
                if (bluetoothAdapter == null) {
                    addDebugLog("ERROR: BluetoothAdapter is null")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                    return
                }

                if (!bluetoothAdapter.isEnabled) {
                    addDebugLog("ERROR: Bluetooth is not enabled")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                    return
                }

                // Check if discovery is already in progress
                if (bluetoothAdapter.isDiscovering) {
                    addDebugLog("WARNING: Discovery already in progress, canceling first")
                    bluetoothAdapter.cancelDiscovery()
                    // Wait a moment for cancellation
                    try {
                        Thread.sleep(100)
                    } catch (e: InterruptedException) {
                        addDebugLog("Sleep interrupted: ${e.message}")
                    }
                }

                // Runtime permission checks for Classic Bluetooth scanning
                // Classic scanning requires ACCESS_FINE_LOCATION on Android 6+
                // BLUETOOTH_SCAN and BLUETOOTH_CONNECT are NOT required for Classic scanning
                val hasLocationPermission = ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED

                val hasCoarseLocationPermission = ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED

                if (!hasLocationPermission && !hasCoarseLocationPermission) {
                    addDebugLog("❌ CRITICAL: Missing location permission")
                    addDebugLog("❌ ACCESS_FINE_LOCATION or ACCESS_COARSE_LOCATION is REQUIRED for Bluetooth scanning on Android 6.0+")
                    addDebugLog("❌ Please grant location permission in app settings")
                    addDebugLog("❌ Note: Location data is NOT used - this is an Android privacy requirement")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                    return
                }

                addDebugLog("All prerequisites met - starting Classic Bluetooth discovery")
                addDebugLog("Bluetooth enabled: ${bluetoothAdapter.isEnabled}")
                addDebugLog("Discovery in progress: ${bluetoothAdapter.isDiscovering}")
                addDebugLog("ACCESS_FINE_LOCATION permission: $hasLocationPermission")
                addDebugLog("ACCESS_COARSE_LOCATION permission: $hasCoarseLocationPermission")

                try {
                    val discoveryResult = bluetoothAdapter.startDiscovery()
                    addDebugLog("startDiscovery() returned: $discoveryResult")

                    if (discoveryResult) {
                        addDebugLog("SUCCESS: Classic Bluetooth discovery started")
                        // Set timeout for Classic discovery (Android doesn't have built-in timeout)
                        scanTimeoutHandler.postDelayed(scanTimeoutRunnable, 30000) // 30 seconds
                    } else {
                        addDebugLog("FAILURE: startDiscovery() returned false")
                        addDebugLog("Possible reasons:")
                        addDebugLog("- Another discovery already in progress")
                        addDebugLog("- Bluetooth not in ready state")
                        addDebugLog("- Insufficient permissions")
                        addDebugLog("- System Bluetooth service issue")
                        _isScanning.value = false
                        _scanType.value = ScanType.NONE
                    }
                } catch (e: SecurityException) {
                    addDebugLog("SECURITY EXCEPTION during discovery: ${e.message}")
                    addDebugLog("This usually means missing BLUETOOTH_SCAN permission")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                } catch (e: Exception) {
                    addDebugLog("UNEXPECTED EXCEPTION during discovery: ${e.message}")
                    addDebugLog("Exception type: ${e.javaClass.simpleName}")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                }
            }
            ScanType.NONE -> {
                addDebugLog("Scan type set to NONE")
                _isScanning.value = false
            }
        }
    }

    fun stopScan() {
        if (!_isScanning.value) return

        addDebugLog("Stopping scan of type: ${_scanType.value}")

        // Cancel any pending timeout
        scanTimeoutHandler.removeCallbacks(scanTimeoutRunnable)

        _isScanning.value = false
        when (_scanType.value) {
            ScanType.BLE -> bleScanner.stopScan()
            ScanType.CLASSIC -> {
                bluetoothAdapter?.cancelDiscovery()
                addDebugLog("Classic Bluetooth discovery cancelled")
            }
            ScanType.NONE -> {}
        }
        _scanType.value = ScanType.NONE
        addDebugLog("Scan stopped successfully")
    }

    fun connect(device: BluetoothDeviceWrapper) {
        addDebugLog("Connecting to ${device.type} device: ${device.name} (${device.address})")
        stopScan()

        if (device.type == DeviceType.BLE) {
            addDebugLog("Clearing GATT services for BLE connection")
            _gattServices.clear()
        } else {
            // Classic Bluetooth device (HC-05/HC-06)
            addDebugLog("Attempting to connect to Classic Bluetooth device (HC-05/HC-06) using SPP")
            _bluetoothState.value = BluetoothState.Connecting
        }

        addDebugLog("Calling bluetoothService.connect()")
        bluetoothService?.connect(device.device, device.type == DeviceType.BLE)
        addDebugLog("Connection request sent to service")
    }

    fun disconnect() {
        bluetoothService?.disconnect()
        _gattServices.clear()
        addDebugLog("Disconnected and cleared GATT services")
    }

    fun sendMessage(message: String, serviceUuid: UUID? = null, characteristicUuid: UUID? = null) {
        addDebugLog("Sending message: '$message' (length: ${message.length})")
        if (serviceUuid != null && characteristicUuid != null) {
            addDebugLog("Using BLE service: $serviceUuid, characteristic: $characteristicUuid")
        }
        bluetoothService?.write(message.toByteArray(), serviceUuid, characteristicUuid)
        _messages.add("→ $message") // Use arrow to indicate outgoing message
        addDebugLog("Message added to UI list")
    }

    // SCADA-specific methods for HC-05/HC-06 communication
    fun sendSCADACommand(command: String) {
        // Add SCADA protocol formatting if needed
        val formattedCommand = if (command.startsWith("*") || command.endsWith("#")) {
            command // Already formatted
        } else {
            "*$command#" // Standard SCADA protocol format
        }
        addDebugLog("Sending SCADA command to HC-05/HC-06: '$formattedCommand'")
        sendMessage(formattedCommand)
    }

    fun sendRawCommand(command: String) {
        addDebugLog("Sending raw command to HC-05/HC-06: '$command'")
        sendMessage(command)
    }

    fun sendATCommand(command: String) {
        val atCommand = "AT+$command\r\n" // HC-05/HC-06 AT commands
        addDebugLog("Sending AT command to HC-05/HC-06: '$atCommand'")
        sendMessage(atCommand)
    }

    fun queryDeviceInfo() {
        sendATCommand("VERSION?") // Get HC-05/HC-06 version
    }

    fun getDeviceName() {
        sendATCommand("NAME?") // Get device name
    }

    fun getConnectionStatus() {
        sendATCommand("STATE?") // Get connection state
    }

    // Common SCADA sensor reading commands
    fun readTemperature() {
        sendSCADACommand("READ_TEMP")
    }

    fun readPressure() {
        sendSCADACommand("READ_PRES")
    }

    fun readFlowRate() {
        sendSCADACommand("READ_FLOW")
    }

    fun readLevel() {
        sendSCADACommand("READ_LEVL")
    }

    // Control commands
    fun openValve() {
        sendSCADACommand("VALVE_OPEN")
    }

    fun closeValve() {
        sendSCADACommand("VALVE_CLOSE")
    }

    fun startPump() {
        sendSCADACommand("PUMP_START")
    }

    fun stopPump() {
        sendSCADACommand("PUMP_STOP")
    }

    fun setAlarmThreshold(threshold: String) {
        sendSCADACommand("ALARM_SET:$threshold")
    }

    // System commands
    fun resetDevice() {
        sendSCADACommand("SYSTEM_RESET")
    }

    fun getSystemStatus() {
        sendSCADACommand("STATUS")
    }

    fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.readCharacteristic(characteristic)
    }

    fun writeCharacteristic(characteristic: BluetoothGattCharacteristic, value: String) {
        bluetoothService?.write(value.toByteArray(), characteristic.service.uuid, characteristic.uuid)
    }

    fun enableNotifications(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.enableNotifications(characteristic.service.uuid, characteristic.uuid)
    }

    fun disableNotifications(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.disableNotifications(characteristic.service.uuid, characteristic.uuid)
    }

    fun clearMessages() {
        _messages.clear()
    }

    fun addDebugLog(message: String) {
        val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
        _debugLogs.add("[$timestamp] $message")
        if (_debugLogs.size > 50) {
            _debugLogs.removeAt(0)
        }
    }

    fun clearDebugLogs() {
        _debugLogs.clear()
    }

    fun enableBluetooth() {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBtIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(enableBtIntent)
    }

    // Diagnostic method to check Bluetooth system state
    fun diagnoseBluetoothState() {
        addDebugLog("=== BLUETOOTH SYSTEM DIAGNOSTICS ===")

        if (bluetoothAdapter == null) {
            addDebugLog("❌ CRITICAL: BluetoothAdapter is null - Bluetooth not supported on this device")
            return
        }

        addDebugLog("✅ BluetoothAdapter available")

        val isEnabled = bluetoothAdapter.isEnabled
        addDebugLog("Bluetooth enabled: $isEnabled")

        val isDiscovering = bluetoothAdapter.isDiscovering
        addDebugLog("Currently discovering: $isDiscovering")

        val name = bluetoothAdapter.name
        addDebugLog("Adapter name: $name")

        val address = bluetoothAdapter.address
        addDebugLog("Adapter address: $address")

        val state = bluetoothAdapter.state
        val stateName = when (state) {
            BluetoothAdapter.STATE_OFF -> "OFF"
            BluetoothAdapter.STATE_TURNING_ON -> "TURNING_ON"
            BluetoothAdapter.STATE_ON -> "ON"
            BluetoothAdapter.STATE_TURNING_OFF -> "TURNING_OFF"
            else -> "UNKNOWN ($state)"
        }
        addDebugLog("Adapter state: $stateName")

        // Check permissions
        val scanPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.BLUETOOTH_SCAN
        ) == PackageManager.PERMISSION_GRANTED
        addDebugLog("BLUETOOTH_SCAN permission: $scanPermission")

        val connectPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.BLUETOOTH_CONNECT
        ) == PackageManager.PERMISSION_GRANTED
        addDebugLog("BLUETOOTH_CONNECT permission: $connectPermission")

        val adminPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.BLUETOOTH_ADMIN
        ) == PackageManager.PERMISSION_GRANTED
        addDebugLog("BLUETOOTH_ADMIN permission: $adminPermission")

        val locationPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        addDebugLog("ACCESS_FINE_LOCATION permission: $locationPermission")

        val coarseLocationPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        addDebugLog("ACCESS_COARSE_LOCATION permission: $coarseLocationPermission")

        // Check bonded devices
        val bondedDevices = bluetoothAdapter.bondedDevices
        addDebugLog("Bonded devices count: ${bondedDevices?.size ?: 0}")
        bondedDevices?.forEach { device ->
            addDebugLog("Bonded device: ${device.name} (${device.address})")
        }

        addDebugLog("=== END DIAGNOSTICS ===")
    }

    // BLEScanCallback implementation
    override fun onDeviceFound(device: android.bluetooth.BluetoothDevice, rssi: Int, name: String?) {
        addDebugLog("BLE device found: ${device.address}, name=${name ?: "Unknown"}, rssi=$rssi")

        val wrapper = BluetoothDeviceWrapper(
            device = device,
            name = name ?: device.name ?: "Unknown",
            address = device.address,
            type = DeviceType.BLE,
            rssi = rssi,
            isBonded = device.bondState == android.bluetooth.BluetoothDevice.BOND_BONDED
        )
        if (!_discoveredDevices.any { d -> d.address == wrapper.address }) {
            _discoveredDevices.add(wrapper)
            addDebugLog("Added BLE device to list. Total devices: ${_discoveredDevices.size}")
        } else {
            addDebugLog("BLE device already in list, skipping duplicate")
        }
    }

    override fun onScanFinished() {
        _isScanning.value = false
        _scanType.value = ScanType.NONE
        addDebugLog("BLE scan finished")
    }

    override fun onScanFailed(errorCode: Int) {
        _isScanning.value = false
        _scanType.value = ScanType.NONE
        addDebugLog("BLE scan failed with error code: $errorCode")
        // Could show error message to user here
    }
}
