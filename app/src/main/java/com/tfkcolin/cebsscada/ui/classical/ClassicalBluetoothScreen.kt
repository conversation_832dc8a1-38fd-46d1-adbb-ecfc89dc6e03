package com.tfkcolin.cebsscada.ui.classical

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.BluetoothSearching
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.services.ClassicalBluetoothConnectionState
import com.tfkcolin.cebsscada.services.ClassicalBluetoothService
import com.tfkcolin.cebsscada.ui.shared.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * Classical Bluetooth screen with dedicated UI for HC-05/HC-06 communication
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassicalBluetoothScreen(
    viewModel: ClassicalBluetoothViewModel = hiltViewModel(),
    permissionManager: BluetoothPermissionManager
) {
    val hasPermissions by viewModel.hasPermissions.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()
    val connectedDevice by viewModel.connectedDevice.collectAsState()
    val selectedTab by viewModel.selectedTab.collectAsState()
    val reconnectAttempts by viewModel.reconnectAttempts.collectAsState()

    if (!hasPermissions) {
        BluetoothPermissionHandler(
            permissionManager = permissionManager,
            onPermissionsGranted = { /* Permissions granted, UI will update */ },
            onPermissionsDenied = { /* Show permission denied screen */ }
        )
        return
    }

    LazyColumn (
        modifier = Modifier
            .fillMaxSize()
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
    ) {
        item {
            // Header with connection status
            ClassicalBluetoothHeader(
                connectionState = connectionState,
                connectedDevice = connectedDevice,
                reconnectAttempts = reconnectAttempts,
                onDisconnect = { viewModel.disconnect() },
                onCancelReconnect = { viewModel.cancelReconnect() }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Tab row
            val tabs = listOf("Devices", "SCADA Commands", "AT Commands", "Data Monitor")
            ScrollableTabRow(
                selectedTabIndex = selectedTab,
                edgePadding = 4.dp,
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { viewModel.setSelectedTab(index) },
                        text = { Text(title) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
        item {
            // Tab content
            when (selectedTab) {
                0 -> ClassicalDevicesTab(viewModel = viewModel)
                1 -> SCADACommandsTab(viewModel = viewModel, connectionState = connectionState)
                2 -> ATCommandsTab(viewModel = viewModel, connectionState = connectionState)
                3 -> DataMonitorTab(viewModel = viewModel)
            }
        }
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun ClassicalBluetoothHeader(
    connectionState: ClassicalBluetoothConnectionState,
    connectedDevice: android.bluetooth.BluetoothDevice?,
    reconnectAttempts: Int,
    onDisconnect: () -> Unit,
    onCancelReconnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (connectionState) {
                ClassicalBluetoothConnectionState.CONNECTED -> MaterialTheme.colorScheme.primaryContainer
                ClassicalBluetoothConnectionState.CONNECTING -> MaterialTheme.colorScheme.secondaryContainer
                ClassicalBluetoothConnectionState.RECONNECTING -> MaterialTheme.colorScheme.secondaryContainer
                ClassicalBluetoothConnectionState.ERROR -> MaterialTheme.colorScheme.errorContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Classical Bluetooth",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = when (connectionState) {
                            ClassicalBluetoothConnectionState.DISCONNECTED -> "Disconnected"
                            ClassicalBluetoothConnectionState.CONNECTING -> "Connecting..."
                            ClassicalBluetoothConnectionState.CONNECTED -> "Connected to ${connectedDevice?.name ?: "Unknown"}"
                            ClassicalBluetoothConnectionState.DISCONNECTING -> "Disconnecting..."
                            ClassicalBluetoothConnectionState.ERROR -> "Connection Error"
                            ClassicalBluetoothConnectionState.RECONNECTING -> 
                                "Reconnecting... (attempt $reconnectAttempts/${ClassicalBluetoothService.MAX_RECONNECT_ATTEMPTS})"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                if (connectionState == ClassicalBluetoothConnectionState.CONNECTED) {
                    OutlinedButton(onClick = onDisconnect) {
                        Icon(Icons.Default.BluetoothDisabled, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Disconnect")
                    }
                }

                if (connectionState == ClassicalBluetoothConnectionState.RECONNECTING) {
                    OutlinedButton(onClick = onCancelReconnect) {
                        Icon(
                            Icons.Default.Cancel,
                            tint = Color.Red,
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ClassicalDevicesTab(viewModel: ClassicalBluetoothViewModel) {
    val isScanning by viewModel.isScanning.collectAsState()
    val discoveredDevices by viewModel.discoveredDevices.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()
    val connectingDevice by viewModel.connectingDevice.collectAsState()

    Column {
        // Scan controls
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            if (isScanning) {
                Button(
                    onClick = { viewModel.stopScan() },
                    modifier = Modifier.weight(1f)
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Stop Scan")
                }
            } else {
                Button(
                    onClick = { viewModel.startScan() },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Search, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Start Scan")
                }
            }

            OutlinedButton(
                onClick = { viewModel.clearDevices() },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Clear")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Bonded devices section
        val bondedDevices = viewModel.getBondedDevices()
        if (bondedDevices.isNotEmpty()) {
            Text(
                text = "Paired Devices",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            bondedDevices.forEach { device ->
                ClassicalDeviceCard(
                    device = device,
                    connectionState = connectionState,
                    connectingDevice = connectingDevice,
                    onConnect = { viewModel.connect(device) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Discovered devices
        Text(
            text = "Discovered Devices",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        if (discoveredDevices.isEmpty() && !isScanning) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.BluetoothSearching,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "No devices found",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Start scanning to discover Classical Bluetooth devices",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                discoveredDevices.forEach { device ->
                    ClassicalDeviceCard(
                        device = device,
                        connectionState = connectionState,
                        connectingDevice = connectingDevice,
                        onConnect = { viewModel.connect(device) }
                    )
                }
            }
        }
    }
}

@Composable
fun ClassicalDeviceCard(
    device: ScannedBluetoothDevice,
    connectionState: ClassicalBluetoothConnectionState,
    connectingDevice: android.bluetooth.BluetoothDevice?,
    onConnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (device.isBonded) {
                        Icon(
                            Icons.Default.Link,
                            contentDescription = "Paired",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = "Paired",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    device.rssi?.let { rssi ->
                        Text(
                            text = "RSSI: ${rssi}dBm",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }

            val isConnecting = connectionState == ClassicalBluetoothConnectionState.CONNECTING &&
                connectingDevice?.address == device.address

            Button(
                onClick = onConnect,
                enabled = connectionState == ClassicalBluetoothConnectionState.DISCONNECTED || connectionState == ClassicalBluetoothConnectionState.ERROR
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Icon(Icons.Default.Bluetooth, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Connect")
                }
            }
        }
    }
}

@Composable
fun SCADACommandsTab(
    viewModel: ClassicalBluetoothViewModel,
    connectionState: ClassicalBluetoothConnectionState
) {
    var customCommand by remember { mutableStateOf("") }
    val scadaCommands = viewModel.getCommonSCADACommands()

    Column {
        // Custom command input
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Send Custom SCADA Command",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(12.dp))

                OutlinedTextField(
                    value = customCommand,
                    onValueChange = { customCommand = it },
                    label = { Text("Command") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("Enter SCADA command...") },
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED
                )

                Spacer(modifier = Modifier.height(8.dp))

                Button(
                    onClick = {
                        if (customCommand.isNotBlank()) {
                            viewModel.sendSCADACommand(customCommand)
                            customCommand = ""
                        }
                    },
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED && customCommand.isNotBlank(),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.AutoMirrored.Filled.Send, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Send Command")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Predefined SCADA commands
        Text(
            text = "Common SCADA Commands",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            scadaCommands.forEach { command ->
                SCADACommandCard(
                    command = command,
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED,
                    onExecute = { viewModel.sendSCADACommand(command.command) }
                )
            }
        }
    }
}

@Composable
fun SCADACommandCard(
    command: SCADACommand,
    enabled: Boolean,
    onExecute: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = command.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = command.command,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = command.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }

            Button(
                onClick = onExecute,
                enabled = enabled
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Execute")
            }
        }
    }
}

@Composable
fun ATCommandsTab(
    viewModel: ClassicalBluetoothViewModel,
    connectionState: ClassicalBluetoothConnectionState
) {
    var customATCommand by remember { mutableStateOf("") }
    val atCommands = viewModel.getCommonATCommands()

    Column {
        // Custom AT command input
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Send Custom AT Command",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(12.dp))

                OutlinedTextField(
                    value = customATCommand,
                    onValueChange = { customATCommand = it },
                    label = { Text("AT Command") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("Enter AT command (without AT prefix)...") },
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED
                )

                Spacer(modifier = Modifier.height(8.dp))

                Button(
                    onClick = {
                        if (customATCommand.isNotBlank()) {
                            viewModel.sendATCommand(customATCommand)
                            customATCommand = ""
                        }
                    },
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED && customATCommand.isNotBlank(),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.AutoMirrored.Filled.Send, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Send AT Command")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Predefined AT commands
        Text(
            text = "Common AT Commands (HC-05/HC-06)",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            atCommands.forEach { command ->
                ATCommandCard(
                    command = command,
                    enabled = connectionState == ClassicalBluetoothConnectionState.CONNECTED,
                    onExecute = { viewModel.sendATCommand(command.command) }
                )
            }
        }
    }
}

@Composable
fun ATCommandCard(
    command: ATCommand,
    enabled: Boolean,
    onExecute: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = command.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = command.command,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = command.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }

            Button(
                onClick = onExecute,
                enabled = enabled
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Send")
            }
        }
    }
}

@Composable
fun DataMonitorTab(viewModel: ClassicalBluetoothViewModel) {
    val messages by viewModel.messages.collectAsState()
    val connectionStats = viewModel.getConnectionStats()

    Column {
        // Connection statistics
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Connection Statistics",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(12.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Text(
                            text = "Messages Sent",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = connectionStats.messagesSent.toString(),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Column {
                        Text(
                            text = "Messages Received",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = connectionStats.messagesReceived.toString(),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Column {
                        Text(
                            text = "Bytes Transferred",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = "${connectionStats.bytesSent + connectionStats.bytesReceived}",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Message controls
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Message History (${messages.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            OutlinedButton(
                onClick = { viewModel.clearMessages() },
                enabled = messages.isNotEmpty()
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Clear")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Messages list
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            messages.reversed().forEach { message ->
                ClassicalMessageCard(message = message)
            }
        }
    }
}

@Composable
fun ClassicalMessageCard(message: ClassicalBluetoothMessage) {
    val dateFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    val timeString = dateFormat.format(Date(message.timestamp))

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (message.isOutgoing) {
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            } else {
                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = if (message.isOutgoing) "SENT" else "RECEIVED",
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold,
                    color = if (message.isOutgoing) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.secondary
                    }
                )

                Text(
                    text = timeString,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Text(
                text = message.content,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}