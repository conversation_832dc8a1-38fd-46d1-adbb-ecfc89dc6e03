package com.tfkcolin.cebsscada.ui.permissions

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat

@Composable
fun BluetoothPermissionHandler(
    onPermissionsGranted: () -> Unit,
    onPermissionsDenied: () -> Unit
) {
    val context = LocalContext.current
    
    val bluetoothPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        listOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE,
            Manifest.permission.ACCESS_FINE_LOCATION // Still required for Bluetooth scanning on Android 12+
        )
    } else {
        listOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        android.util.Log.d("BluetoothPermissionHandler", "=== PERMISSION REQUEST RESULTS ===")
        permissions.forEach { (permission, granted) ->
            android.util.Log.d("BluetoothPermissionHandler", "Permission $permission: ${if (granted) "GRANTED ✅" else "DENIED ❌"}")
        }
        val allGranted = permissions.all { it.value }
        android.util.Log.d("BluetoothPermissionHandler", "All permissions granted: $allGranted")
        android.util.Log.d("BluetoothPermissionHandler", "=== PERMISSION REQUEST END ===")

        if (allGranted) {
            android.util.Log.d("BluetoothPermissionHandler", "✅ All permissions granted - proceeding to Bluetooth screen")
            onPermissionsGranted()
        } else {
            android.util.Log.d("BluetoothPermissionHandler", "❌ Some permissions denied - showing permission denied screen")
            onPermissionsDenied()
        }
    }
    
    LaunchedEffect(Unit) {
        android.util.Log.d("BluetoothPermissionHandler", "=== PERMISSION CHECK START ===")
        android.util.Log.d("BluetoothPermissionHandler", "Android Version: ${Build.VERSION.SDK_INT} (${Build.VERSION.RELEASE})")
        android.util.Log.d("BluetoothPermissionHandler", "Required permissions: $bluetoothPermissions")

        val hasAllPermissions = bluetoothPermissions.all {
            val granted = ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
            android.util.Log.d("BluetoothPermissionHandler", "Permission $it: ${if (granted) "GRANTED ✅" else "DENIED ❌"}")
            granted
        }

        android.util.Log.d("BluetoothPermissionHandler", "All permissions granted: $hasAllPermissions")
        android.util.Log.d("BluetoothPermissionHandler", "=== PERMISSION CHECK END ===")

        if (hasAllPermissions) {
            onPermissionsGranted()
        } else {
            permissionLauncher.launch(bluetoothPermissions.toTypedArray())
        }
    }
}

@Composable
fun PermissionDeniedScreen(
    onRetry: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "Warning",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Bluetooth Permissions Required",
            style = MaterialTheme.typography.headlineSmall
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "This app needs Bluetooth and Location permissions to discover and connect with devices. Location permission is required by Android for Bluetooth scanning (even though location data is not used).",
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row {
            Button(onClick = onRetry) {
                Text("Retry")
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            OutlinedButton(onClick = onOpenSettings) {
                Text("Open Settings")
            }
        }
    }
}
