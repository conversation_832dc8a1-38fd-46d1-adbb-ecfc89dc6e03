package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Redo
import androidx.compose.material.icons.automirrored.filled.Undo
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.cebsscada.ui.components.AdaptiveThreePane
import com.tfkcolin.cebsscada.ui.components.AdaptiveDialog
import com.tfkcolin.cebsscada.ui.components.AdaptiveIconButton
import com.tfkcolin.cebsscada.ui.components.ScreenConfig
import com.tfkcolin.cebsscada.ui.theme.rememberResponsiveLayout
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentType
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.WorkspaceValidator
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceEntity
import kotlinx.coroutines.launch

/**
 * Main SCADA screen with workspace and component palette
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScadaScreen(
    workspaceId: Long? = null,
    onBackToHome: () -> Unit = {},
    onScreenConfigChange: (ScreenConfig) -> Unit,
    viewModel: ScadaWorkspaceViewModel = hiltViewModel()
) {
    val components by viewModel.components.collectAsStateWithLifecycle()
    val selectedComponent by viewModel.selectedComponent.collectAsStateWithLifecycle()
    val currentWorkspaceId by viewModel.currentWorkspaceId.collectAsStateWithLifecycle()
    val currentWorkspaceName by viewModel.currentWorkspaceName.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val errorMessage by viewModel.errorMessage.collectAsStateWithLifecycle()

    var showConfigDialog by remember { mutableStateOf(false) }
    var showComponentPalette by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }
    var showImportDialog by remember { mutableStateOf(false) }
    var showCreateWorkspaceDialog by remember { mutableStateOf(false) }
    var showLoadWorkspaceDialog by remember { mutableStateOf(false) }
    var showSaveWorkspaceDialog by remember { mutableStateOf(false) }
    var showDataBindingDialog by remember { mutableStateOf<ScadaComponent?>(null) }
    var showOrganizationTools by remember { mutableStateOf(false) }
    var showValidationDialog by remember { mutableStateOf<List<WorkspaceValidator.ValidationResult>?>(null) }
    var availableWorkspaces by remember { mutableStateOf<List<WorkspaceEntity>>(emptyList()) }

    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    // Load workspace if ID provided
    LaunchedEffect(workspaceId) {
        workspaceId?.let { viewModel.loadWorkspace(it) }
    }

    // Show error messages
    LaunchedEffect(errorMessage) {
        errorMessage?.let { message ->
            scope.launch {
                snackbarHostState.showSnackbar(message)
                viewModel.clearErrorMessage()
            }
        }
    }

    // Load available workspaces when load dialog is shown
    LaunchedEffect(showLoadWorkspaceDialog) {
        if (showLoadWorkspaceDialog) {
            availableWorkspaces = viewModel.loadAvailableWorkspaces()
        }
    }

    // Get responsive layout configuration
    val layout = rememberResponsiveLayout()

    // Configure the central scaffold
    LaunchedEffect(currentWorkspaceName, viewModel.hasUnsavedChanges()) {
        onScreenConfigChange(
            ScreenConfig(
                title = (currentWorkspaceName ?: "SCADA Workspace") +
                        if (viewModel.hasUnsavedChanges()) " *" else "",
                showBackButton = true,
                showSettingsButton = false,
                actions = buildList {
                    // Component palette button (for compact screens)
                    if (layout.shouldUseSinglePane) {
                        add {
                            AdaptiveIconButton(
                                onClick = { showComponentPalette = true },
                                layout = layout
                            ) {
                                Icon(Icons.Default.Add, contentDescription = "Add components")
                            }
                        }
                    }

                    // New workspace
                    add {
                        AdaptiveIconButton(
                            onClick = { showCreateWorkspaceDialog = true },
                            layout = layout
                        ) {
                            Icon(Icons.Default.AddCircle, contentDescription = "New workspace")
                        }
                    }

                    // Validate workspace
                    add {
                        AdaptiveIconButton(
                            onClick = {
                                val validationResults = viewModel.getValidationResults()
                                if (validationResults.isNotEmpty()) {
                                    showValidationDialog = validationResults
                                } else {
                                    showSaveWorkspaceDialog = true
                                }
                            },
                            enabled = !isLoading,
                            layout = layout
                        ) {
                            Icon(Icons.Default.CheckCircle, contentDescription = "Validate workspace")
                        }
                    }

                    // Save workspace
                    add {
                        AdaptiveIconButton(
                            onClick = { showSaveWorkspaceDialog = true },
                            enabled = !isLoading,
                            layout = layout
                        ) {
                            Icon(Icons.Default.Save, contentDescription = "Save workspace")
                        }
                    }

                    // Load workspace
                    add {
                        AdaptiveIconButton(
                            onClick = { showLoadWorkspaceDialog = true },
                            enabled = !isLoading,
                            layout = layout
                        ) {
                            Icon(Icons.Default.FolderOpen, contentDescription = "Load workspace")
                        }
                    }

                    // Export workspace
                    add {
                        AdaptiveIconButton(
                            onClick = { showExportDialog = true },
                            enabled = !isLoading,
                            layout = layout
                        ) {
                            Icon(Icons.Default.FileDownload, contentDescription = "Export workspace")
                        }
                    }

                    // Import workspace
                    add {
                        AdaptiveIconButton(
                            onClick = { showImportDialog = true },
                            enabled = !isLoading,
                            layout = layout
                        ) {
                            Icon(Icons.Default.FileUpload, contentDescription = "Import workspace")
                        }
                    }

                    // Organization tools
                    add {
                        IconButton(
                            onClick = { showOrganizationTools = true },
                            enabled = !isLoading
                        ) {
                            Icon(Icons.Default.Build, contentDescription = "Organization tools")
                        }
                    }

                    // Undo/Redo
                    add {
                        IconButton(
                            onClick = { viewModel.undo() },
                            enabled = viewModel.canUndo() && !isLoading
                        ) {
                            Icon(Icons.AutoMirrored.Filled.Undo, contentDescription = "Undo")
                        }
                    }

                    add {
                        IconButton(
                            onClick = { viewModel.redo() },
                            enabled = viewModel.canRedo() && !isLoading
                        ) {
                            Icon(Icons.AutoMirrored.Filled.Redo, contentDescription = "Redo")
                        }
                    }

                    // Clear workspace
                    add {
                        IconButton(
                            onClick = { viewModel.clearWorkspace() },
                            enabled = !isLoading
                        ) {
                            Icon(Icons.Default.Clear, contentDescription = "Clear workspace")
                        }
                    }
                },
                snackbarHostState = snackbarHostState
            )
        )
    }

    // Content without Scaffold
        AdaptiveThreePane(
            leftContent = {
                // Component palette - only shown on medium and expanded screens
                ComponentPalette(
                    onComponentSelected = { type, position ->
                        val newComponent = ComponentFactory.createComponent(type, position)
                        newComponent?.let { viewModel.addComponent(it) }
                    },
                    modifier = Modifier.fillMaxHeight()
                )
            },
            centerContent = {
                // Main workspace canvas
                ScadaWorkspace(
                    components = components,
                    onComponentMoved = viewModel::moveComponent,
                    onComponentSelected = { component ->
                        viewModel.selectComponent(component)
                        showConfigDialog = (component != null)
                    },
                    selectedComponent = selectedComponent,
                    modifier = Modifier.fillMaxSize()
                )
            },
            rightContent = {
                // Component properties panel - only shown on expanded screens when component selected
                if (selectedComponent != null) {
                    ComponentPropertiesPanel(
                        component = selectedComponent!!,
                        onConfigChanged = { component, config ->
                            viewModel.updateComponentConfig(component, config)
                        },
                        onDeleteComponent = { component ->
                            viewModel.removeComponent(component)
                            showConfigDialog = false
                        },
                        onDuplicateComponent = { component ->
                            viewModel.duplicateComponent(component)
                        },
                        onConfigureDataBinding = { component ->
                            showDataBindingDialog = component
                        },
                        modifier = Modifier.fillMaxHeight()
                    )
                }
            },
            modifier = Modifier.fillMaxSize(),
            layout = layout
        )

    // Component palette dialog for small screens
    if (showComponentPalette) {
        ComponentPaletteDialog(
            onComponentSelected = { type, position ->
                val newComponent = ComponentFactory.createComponent(type, position)
                newComponent?.let { viewModel.addComponent(it) }
                showComponentPalette = false
            },
            onDismiss = { showComponentPalette = false }
        )
    }

    // Advanced configuration dialog
    if (showConfigDialog && selectedComponent != null) {
        val componentGroups by viewModel.componentGroups.collectAsStateWithLifecycle()
        AdvancedComponentConfigurationDialog(
            component = selectedComponent!!,
            availableGroups = componentGroups,
            onSave = { updatedComponent ->
                // The component is already updated, just trigger recomposition
                viewModel.selectComponent(updatedComponent)
                showConfigDialog = false
            },
            onCancel = { showConfigDialog = false },
            onDelete = {
                viewModel.removeComponent(selectedComponent!!)
                showConfigDialog = false
            }
        )
    }

    // Workspace creation dialog
    if (showCreateWorkspaceDialog) {
        WorkspaceCreationDialog(
            onDismiss = { showCreateWorkspaceDialog = false },
            onCreateWorkspace = { name, description ->
                viewModel.createNewWorkspace()
                showCreateWorkspaceDialog = false
            },
            isLoading = false
        )
    }

    // Workspace save dialog
    if (showSaveWorkspaceDialog) {
        WorkspaceSaveDialog(
            currentWorkspaceId = currentWorkspaceId,
            onDismiss = { showSaveWorkspaceDialog = false },
            onSaveWorkspace = { name, description ->
                val success = viewModel.saveWorkspaceWithValidation(name, description, showValidationDialog = false)
                if (success) {
                    showSaveWorkspaceDialog = false
                }
                // Error message will be shown via snackbar
            },
            onSaveAsNew = { name, description ->
                // For now, just save as new workspace
                val success = viewModel.saveWorkspaceWithValidation(name, description, showValidationDialog = false)
                if (success) {
                    showSaveWorkspaceDialog = false
                }
            },
            isLoading = isLoading
        )
    }

    // Workspace load dialog
    if (showLoadWorkspaceDialog) {
        // TODO: Load workspaces from repository
        // For now, show empty dialog
        WorkspaceLoadDialog(
            workspaces = availableWorkspaces,
            onDismiss = { showLoadWorkspaceDialog = false },
            onLoadWorkspace = { workspaceId ->
                viewModel.loadWorkspace(workspaceId)
                showLoadWorkspaceDialog = false
            },
            isLoading = isLoading
        )
    }

    // Data binding configuration dialog
    showDataBindingDialog?.let { component ->
        val currentBinding = when (component.config) {
            is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).dataBinding
            is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).dataBinding
            else -> com.tfkcolin.cebsscada.scada.DataBinding()
        }

        DataBindingConfigurationDialog(
            currentBinding = currentBinding,
            onDismiss = { showDataBindingDialog = null },
            onSave = { newBinding ->
                updateComponentDataBinding(component, newBinding)
                showDataBindingDialog = null
            },
            componentName = component.type.name.replace("_", " ")
        )
    }

    // Export workspace dialog
    if (showExportDialog && currentWorkspaceId != null) {
        // Create a mock workspace entity for export (in a real app, you'd load this from repository)
        val mockWorkspace = WorkspaceEntity(
            id = currentWorkspaceId!!,
            name = currentWorkspaceName ?: "Unnamed Workspace",
            description = "",
            componentCount = components.size
        )

        WorkspaceExportDialog(
            workspace = mockWorkspace,
            onDismiss = { showExportDialog = false }
        )
    }

    // Import workspace dialog
    if (showImportDialog) {
        WorkspaceImportDialog(
            onDismiss = { showImportDialog = false },
            onWorkspaceImported = { workspaceId ->
                viewModel.loadWorkspace(workspaceId)
                showImportDialog = false
            }
        )
    }

    // Organization tools dialog
    if (showOrganizationTools) {
        OrganizationToolsDialog(
            onDismiss = { showOrganizationTools = false },
            onAlignLeft = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.LEFT) },
            onAlignCenter = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.CENTER) },
            onAlignRight = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.RIGHT) },
            onAlignTop = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.TOP) },
            onAlignMiddle = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.MIDDLE) },
            onAlignBottom = { viewModel.alignComponents(ComponentOrganizationTools.Alignment.BOTTOM) },
            onDistributeHorizontal = { viewModel.distributeComponents(ComponentOrganizationTools.Distribution.HORIZONTAL) },
            onDistributeVertical = { viewModel.distributeComponents(ComponentOrganizationTools.Distribution.VERTICAL) },
            onMatchSizes = { matchWidth, matchHeight -> viewModel.matchComponentSizes(matchWidth, matchHeight) },
            onArrangeInGrid = { columns -> viewModel.arrangeComponentsInGrid(columns) },
            onBringToFront = { viewModel.bringToFront() },
            onSendToBack = { viewModel.sendToBack() },
            onCreateGroup = { name, color ->
                viewModel.createComponentGroup(name, color)
            },
            onToggleVisibility = { component -> viewModel.toggleComponentVisibility(component) },
            onToggleLock = { component -> viewModel.toggleComponentLock(component) },
            selectedComponents = (if (selectedComponent != null) listOf(selectedComponent) else components).filterNotNull(),
            componentGroups = viewModel.componentGroups.collectAsStateWithLifecycle().value
        )
    }

    // Validation results dialog
    showValidationDialog?.let { validationResults ->
        ValidationResultsDialog(
            validationResults = validationResults,
            onDismiss = { showValidationDialog = null },
            onProceed = {
                showValidationDialog = null
                showSaveWorkspaceDialog = true
            },
            onFixIssues = {
                showValidationDialog = null
                // Could navigate to specific component or show help
            },
            title = "Workspace Validation Results"
        )
    }
}

/**
 * Update component data binding configuration
 */
private fun updateComponentDataBinding(component: ScadaComponent, newBinding: com.tfkcolin.cebsscada.scada.DataBinding) {
    val updatedConfig = when (component.config) {
        is com.tfkcolin.cebsscada.scada.components.PushButtonConfig -> {
            (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).copy(
                dataBinding = newBinding
            )
        }
        is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig -> {
            (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).copy(
                dataBinding = newBinding
            )
        }
        else -> component.config // No data binding support for other component types yet
    }

    // Update the component configuration
    component.updateConfig(updatedConfig)
}

/**
 * Component properties panel for quick configuration
 */
@Composable
fun ComponentPropertiesPanel(
    component: ScadaComponent,
    onConfigChanged: (ScadaComponent, com.tfkcolin.cebsscada.scada.ComponentConfig) -> Unit,
    onDeleteComponent: (ScadaComponent) -> Unit,
    onDuplicateComponent: (ScadaComponent) -> Unit,
    onConfigureDataBinding: (ScadaComponent) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .widthIn(min = 250.dp, max = 350.dp)
            .fillMaxHeight(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "Properties",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Component type and basic info
            Text(
                text = "${component.type.name.replace("_", " ")} - ${component.id.take(8)}",
                style = MaterialTheme.typography.titleSmall,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "Position: (${component.position.x.toInt()}, ${component.position.y.toInt()})",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Data Binding Status
            val dataBinding = when (component.config) {
                is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig).dataBinding
                is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                    (component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig).dataBinding
                else -> null
            }

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(modifier = Modifier.padding(12.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Data Binding",
                            style = MaterialTheme.typography.titleSmall
                        )
                        val statusText = when {
                            dataBinding?.protocol == null -> "Static"
                            dataBinding.address.isBlank() -> "Incomplete"
                            else -> "Connected"
                        }
                        val statusColor = when {
                            dataBinding?.protocol == null -> MaterialTheme.colorScheme.onSurfaceVariant
                            dataBinding.address.isBlank() -> MaterialTheme.colorScheme.error
                            else -> MaterialTheme.colorScheme.primary
                        }
                        Text(
                            text = statusText,
                            style = MaterialTheme.typography.bodySmall,
                            color = statusColor
                        )
                    }

                    if (dataBinding?.protocol != null) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "${dataBinding.protocol.name.replace("_", " ")}: ${dataBinding.address}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = { onConfigureDataBinding(component) },
                        modifier = Modifier.fillMaxWidth(),
                        contentPadding = PaddingValues(vertical = 8.dp)
                    ) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = "Configure Data Binding",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Configure Data Binding")
                    }
                }
            }

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { onDuplicateComponent(component) },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.ContentCopy, contentDescription = "Duplicate")
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Duplicate")
                }

                OutlinedButton(
                    onClick = { onDeleteComponent(component) },
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Delete, contentDescription = "Delete")
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Delete")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Component-specific configuration hint
            Text(
                text = "Additional configuration options will be available based on component type.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Component configuration dialog for detailed settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ComponentConfigurationDialog(
    component: ScadaComponent,
    onDismiss: () -> Unit,
    onConfigSaved: (ScadaComponent, com.tfkcolin.cebsscada.scada.ComponentConfig) -> Unit
) {
    // TODO: Implement detailed configuration dialog based on component type
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Configure ${component.type.name.replace("_", " ")}") },
        text = {
            Text("Detailed configuration options will be implemented here.")
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Component palette dialog for small screens
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ComponentPaletteDialog(
    onComponentSelected: (com.tfkcolin.cebsscada.scada.ComponentType, ComponentPosition) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add Component") },
        text = {
            Column {
                Text("Choose a component to add to your workspace:")
                Spacer(modifier = Modifier.height(16.dp))

                availableComponents.forEach { componentInfo ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        onClick = {
                            val position = ComponentPosition(200f, 200f)
                            onComponentSelected(componentInfo.type, position)
                        }
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = componentInfo.icon,
                                contentDescription = componentInfo.name,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = componentInfo.name,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = componentInfo.description,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}