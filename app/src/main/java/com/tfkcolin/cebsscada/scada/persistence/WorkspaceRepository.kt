package com.tfkcolin.cebsscada.scada.persistence

import com.tfkcolin.cebsscada.scada.ScadaComponent
import androidx.compose.ui.unit.*
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.scada.ComponentSize
import com.tfkcolin.cebsscada.scada.DataBindingManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.Exception

/**
 * Repository for workspace management operations
 */
@Singleton
class WorkspaceRepository @Inject constructor(
    private val database: ScadaDatabase,
    private val dataBindingManager: DataBindingManager
) {
    private val workspaceDao = database.workspaceDao()
    val componentDao = database.componentDao()
    private val json = Json { ignoreUnknownKeys = true }

    /**
     * Get all workspaces
     */
    fun getAllWorkspaces(): Flow<List<WorkspaceEntity>> {
        return workspaceDao.getAllWorkspaces()
    }

    /**
     * Search workspaces by name
     */
    fun searchWorkspaces(query: String): Flow<List<WorkspaceEntity>> {
        return workspaceDao.searchWorkspaces(query)
    }

    /**
     * Save a workspace with its components
     */
    suspend fun saveWorkspace(
        name: String,
        description: String = "",
        components: List<ScadaComponent>
    ): WorkspaceResult<Long> {
        return try {
            val workspaceId = workspaceDao.insertWorkspace(
                WorkspaceEntity(
                    name = name,
                    description = description,
                    componentCount = components.size
                )
            )

            // Save components
            val componentEntities = components.map { component ->
                ComponentEntity(
                    workspaceId = workspaceId,
                    componentId = component.id,
                    type = component.type,
                    positionX = component.position.x,
                    positionY = component.position.y,
                    sizeWidth = component.size.width.value,
                    sizeHeight = component.size.height.value,
                    configJson = serializeComponentConfig(component)
                )
            }

            componentDao.insertComponents(componentEntities)
            WorkspaceResult.Success(workspaceId)
        } catch (e: Exception) {
            WorkspaceResult.Error("Failed to save workspace: ${e.message}", e)
        }
    }

    /**
     * Load a workspace with its components
     */
    suspend fun loadWorkspace(workspaceId: Long): WorkspaceResult<WorkspaceWithComponents> {
        return try {
            val workspace = workspaceDao.getWorkspaceById(workspaceId)
                ?: return WorkspaceResult.Error("Workspace with ID $workspaceId not found")

            val components = componentDao.getComponentsForWorkspace(workspaceId)
            WorkspaceResult.Success(WorkspaceWithComponents(workspace, components))
        } catch (e: Exception) {
            WorkspaceResult.Error("Failed to load workspace: ${e.message}", e)
        }
    }

    /**
     * Update an existing workspace
     */
    suspend fun updateWorkspace(
        workspaceId: Long,
        name: String? = null,
        description: String? = null,
        components: List<ScadaComponent>? = null
    ): WorkspaceOperationResult {
        return try {
            val existingWorkspace = workspaceDao.getWorkspaceById(workspaceId)
                ?: return WorkspaceOperationResult.Error("Workspace with ID $workspaceId not found")

            val updatedWorkspace = existingWorkspace.copy(
                name = name ?: existingWorkspace.name,
                description = description ?: existingWorkspace.description,
                modifiedAt = System.currentTimeMillis(),
                componentCount = components?.size ?: existingWorkspace.componentCount
            )

            workspaceDao.updateWorkspace(updatedWorkspace)

            // Update components if provided
            if (components != null) {
                componentDao.deleteComponentsForWorkspace(workspaceId)

                val componentEntities = components.map { component ->
                    ComponentEntity(
                        workspaceId = workspaceId,
                        componentId = component.id,
                        type = component.type,
                        positionX = component.position.x,
                        positionY = component.position.y,
                        sizeWidth = component.size.width.value,
                        sizeHeight = component.size.height.value,
                        configJson = serializeComponentConfig(component)
                    )
                }

                componentDao.insertComponents(componentEntities)
            }

            WorkspaceOperationResult.Success
        } catch (e: Exception) {
            WorkspaceOperationResult.Error("Failed to update workspace: ${e.message}", e)
        }
    }

    /**
     * Delete a workspace and all its components
     */
    suspend fun deleteWorkspace(workspaceId: Long): WorkspaceOperationResult {
        return try {
            componentDao.deleteComponentsForWorkspace(workspaceId)
            workspaceDao.deleteWorkspaceById(workspaceId)
            WorkspaceOperationResult.Success
        } catch (e: Exception) {
            WorkspaceOperationResult.Error("Failed to delete workspace: ${e.message}", e)
        }
    }

    /**
     * Convert components to SCADA components
     */
    suspend fun componentsFromEntities(componentEntities: List<ComponentEntity>): List<ScadaComponent> {
        return componentEntities.mapNotNull { entity ->
            try {
                deserializeComponent(entity)
            } catch (e: Exception) {
                null // Skip invalid components
            }
        }
    }

    /**
     * Serialize component configuration to JSON
     */
    private fun serializeComponentConfig(component: ScadaComponent): String {
        return when (component.config) {
            is com.tfkcolin.cebsscada.scada.components.PushButtonConfig ->
                json.encodeToString(
                    com.tfkcolin.cebsscada.scada.components.SerializablePushButtonConfig.serializer(),
                    com.tfkcolin.cebsscada.scada.components.SerializablePushButtonConfig.fromPushButtonConfig(
                        component.config as com.tfkcolin.cebsscada.scada.components.PushButtonConfig
                    )
                )
            is com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig ->
                json.encodeToString(
                    com.tfkcolin.cebsscada.scada.components.SerializableValueDisplayConfig.serializer(),
                    com.tfkcolin.cebsscada.scada.components.SerializableValueDisplayConfig.fromValueDisplayConfig(
                        component.config as com.tfkcolin.cebsscada.scada.components.ValueDisplayConfig
                    )
                )
            is com.tfkcolin.cebsscada.scada.components.ToggleSwitchConfig ->
                json.encodeToString(
                    com.tfkcolin.cebsscada.scada.components.SerializableToggleSwitchConfig.serializer(),
                    com.tfkcolin.cebsscada.scada.components.SerializableToggleSwitchConfig.fromToggleSwitchConfig(
                        component.config as com.tfkcolin.cebsscada.scada.components.ToggleSwitchConfig
                    )
                )
            else -> "{}" // Empty config for unsupported types
        }
    }

    /**
     * Deserialize component from entity
     */
    private fun deserializeComponent(entity: ComponentEntity): ScadaComponent? {
        val position = ComponentPosition(entity.positionX, entity.positionY)
        val size = ComponentSize(
            width = entity.sizeWidth.dp,
            height = entity.sizeHeight.dp
        )

        return when (entity.type) {
            com.tfkcolin.cebsscada.scada.ComponentType.PUSH_BUTTON -> {
                try {
                    val serializableConfig = json.decodeFromString(
                        com.tfkcolin.cebsscada.scada.components.SerializablePushButtonConfig.serializer(),
                        entity.configJson
                    )
                    val config = serializableConfig.toPushButtonConfig()
                    com.tfkcolin.cebsscada.scada.components.PushButton(
                        dataBindingManager = dataBindingManager,
                        id = entity.componentId,
                        position = position,
                        size = size,
                        config = config
                    )
                } catch (e: Exception) {
                    // Create with default config if deserialization fails
                    com.tfkcolin.cebsscada.scada.components.PushButton(
                        dataBindingManager = dataBindingManager,
                        id = entity.componentId,
                        position = position,
                        size = size
                    )
                }
            }
            com.tfkcolin.cebsscada.scada.ComponentType.VALUE_DISPLAY -> {
                try {
                    val serializableConfig = json.decodeFromString(
                        com.tfkcolin.cebsscada.scada.components.SerializableValueDisplayConfig.serializer(),
                        entity.configJson
                    )
                    val config = serializableConfig.toValueDisplayConfig()
                    com.tfkcolin.cebsscada.scada.components.ValueDisplay(
                        id = entity.componentId,
                        position = position,
                        size = size,
                        config = config
                    )
                } catch (e: Exception) {
                    com.tfkcolin.cebsscada.scada.components.ValueDisplay(
                        id = entity.componentId,
                        position = position,
                        size = size
                    )
                }
            }
            com.tfkcolin.cebsscada.scada.ComponentType.TOGGLE_SWITCH -> {
                try {
                    val serializableConfig = json.decodeFromString(
                        com.tfkcolin.cebsscada.scada.components.SerializableToggleSwitchConfig.serializer(),
                        entity.configJson
                    )
                    val config = serializableConfig.toToggleSwitchConfig()
                    com.tfkcolin.cebsscada.scada.components.ToggleSwitch(
                        dataBindingManager = dataBindingManager,
                        id = entity.componentId,
                        position = position,
                        size = size,
                        config = config
                    )
                } catch (e: Exception) {
                    com.tfkcolin.cebsscada.scada.components.ToggleSwitch(
                        dataBindingManager = dataBindingManager,
                        id = entity.componentId,
                        position = position,
                        size = size
                    )
                }
            }
            else -> null // Unsupported component type
        }
    }
}