package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.MqttBroker
import com.tfkcolin.cebsscada.communication.models.TopicSubscription

/**
 * Enhanced broker status display with detailed connection information
 */
@Composable
fun EnhancedBrokerStatusCard(
    broker: MqttBroker?,
    connectionState: ConnectionState,
    connectionStats: Map<String, Any> = emptyMap(),
    onReconnect: () -> Unit = {},
    onDisconnect: () -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (connectionState) {
                ConnectionState.CONNECTED -> MaterialTheme.colorScheme.primaryContainer
                ConnectionState.ERROR -> MaterialTheme.colorScheme.errorContainer
                ConnectionState.CONNECTING -> MaterialTheme.colorScheme.secondaryContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "MQTT Broker Status",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (broker != null) {
                        Text(
                            text = "${broker.name} (${broker.address}:${broker.port})",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                        )
                    }
                }
                
                // Connection status indicator
                ConnectionStatusBadge(connectionState = connectionState)
            }
            
            if (broker != null) {
                Spacer(modifier = Modifier.height(12.dp))
                
                // Broker details
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        BrokerDetailItem("Protocol", if (broker.useTls) "MQTTS" else "MQTT")
                        BrokerDetailItem("Client ID", broker.clientId ?: "Auto-generated")
                        if (broker.username != null) {
                            BrokerDetailItem("Username", broker.username)
                        }
                    }
                    
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        connectionStats["uptime"]?.let { uptime ->
                            BrokerDetailItem("Uptime", formatUptime(uptime as Long))
                        }
                        connectionStats["messagesSent"]?.let { sent ->
                            BrokerDetailItem("Messages Sent", sent.toString())
                        }
                        connectionStats["messagesReceived"]?.let { received ->
                            BrokerDetailItem("Messages Received", received.toString())
                        }
                    }
                }
                
                if (connectionState == ConnectionState.CONNECTED) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = onReconnect,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Refresh, contentDescription = null)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Reconnect")
                        }
                        
                        Button(
                            onClick = onDisconnect,
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Disconnect")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun BrokerDetailItem(label: String, value: String) {
    Column(
        modifier = Modifier.padding(vertical = 2.dp)
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun ConnectionStatusBadge(connectionState: ConnectionState) {
    val (color, text, icon) = when (connectionState) {
        ConnectionState.CONNECTED -> Triple(Color.Green, "Connected", Icons.Default.CheckCircle)
        ConnectionState.CONNECTING -> Triple(Color.Blue, "Connecting", Icons.Default.Sync)
        ConnectionState.DISCONNECTING -> Triple(Color(0xFFFF9800), "Disconnecting", Icons.Default.Sync)
        ConnectionState.ERROR -> Triple(Color.Red, "Error", Icons.Default.Error)
        ConnectionState.RECONNECTING -> Triple(Color.Blue, "Reconnecting", Icons.Default.Sync)
        ConnectionState.DISCONNECTED -> Triple(Color.Gray, "Disconnected", Icons.Default.Cancel)
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = MaterialTheme.shapes.small,
        modifier = Modifier.padding(4.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = color,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall,
                color = color,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Advanced subscription management component
 */
@Composable
fun AdvancedSubscriptionManager(
    subscriptions: List<TopicSubscription>,
    onSubscribe: (String) -> Unit,
    onUnsubscribe: (String) -> Unit,
    onBulkSubscribe: (List<String>) -> Unit
) {
    var newTopic by remember { mutableStateOf("") }
    var showBulkDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Topic Subscriptions",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Row {
                    IconButton(onClick = { showBulkDialog = true }) {
                        Icon(Icons.AutoMirrored.Filled.List, contentDescription = "Bulk Subscribe")
                    }
                    
                    Text(
                        text = "${subscriptions.size}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.align(Alignment.CenterVertically)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // New subscription input
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = newTopic,
                    onValueChange = { newTopic = it },
                    label = { Text("Topic Pattern") },
                    placeholder = { Text("sensors/+/temperature") },
                    modifier = Modifier.weight(1f),
                    singleLine = true
                )
                
                Button(
                    onClick = {
                        if (newTopic.isNotBlank()) {
                            onSubscribe(newTopic)
                            newTopic = ""
                        }
                    },
                    enabled = newTopic.isNotBlank()
                ) {
                    Text("Subscribe")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick subscription patterns
            Text(
                text = "Quick Patterns:",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                listOf("test/+", "sensors/#", "devices/+/status").forEach { pattern ->
                    FilterChip(
                        onClick = { onSubscribe(pattern) },
                        label = { Text(pattern, style = MaterialTheme.typography.bodySmall) },
                        selected = subscriptions.any { it.topic == pattern }
                    )
                }
            }
            
            if (subscriptions.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                // Active subscriptions list
                Column(
                    modifier = Modifier.heightIn(max = 200.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    subscriptions.forEach { subscription ->
                        SubscriptionItem(
                            subscription = subscription,
                            onUnsubscribe = { onUnsubscribe(subscription.topic) }
                        )
                    }
                }
            }
        }
    }
    
    // Bulk subscription dialog
    if (showBulkDialog) {
        BulkSubscriptionDialog(
            onDismiss = { showBulkDialog = false },
            onSubscribe = { topics ->
                onBulkSubscribe(topics)
                showBulkDialog = false
            }
        )
    }
}

@Composable
fun SubscriptionItem(
    subscription: TopicSubscription,
    onUnsubscribe: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f),
        shape = MaterialTheme.shapes.small
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = subscription.topic,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "QoS: ${subscription.qosLevel.name}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            IconButton(
                onClick = onUnsubscribe,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "Unsubscribe",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
fun BulkSubscriptionDialog(
    onDismiss: () -> Unit,
    onSubscribe: (List<String>) -> Unit
) {
    var topicsText by remember { mutableStateOf("") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Bulk Subscribe") },
        text = {
            Column {
                Text(
                    text = "Enter topic patterns, one per line:",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = topicsText,
                    onValueChange = { topicsText = it },
                    placeholder = { Text("sensors/+/temperature\ndevices/#\nalarms/+/critical") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 10
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val topics = topicsText.lines()
                        .map { it.trim() }
                        .filter { it.isNotBlank() }
                    if (topics.isNotEmpty()) {
                        onSubscribe(topics)
                    }
                },
                enabled = topicsText.isNotBlank()
            ) {
                Text("Subscribe All")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

private fun formatUptime(uptimeMs: Long): String {
    val seconds = uptimeMs / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    val days = hours / 24
    
    return when {
        days > 0 -> "${days}d ${hours % 24}h"
        hours > 0 -> "${hours}h ${minutes % 60}m"
        minutes > 0 -> "${minutes}m ${seconds % 60}s"
        else -> "${seconds}s"
    }
}
