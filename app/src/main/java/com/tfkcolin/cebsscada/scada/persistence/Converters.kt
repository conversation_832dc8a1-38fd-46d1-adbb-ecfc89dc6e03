package com.tfkcolin.cebsscada.scada.persistence

import androidx.room.TypeConverter
import com.tfkcolin.cebsscada.scada.ComponentType

/**
 * Type converters for Room database
 */
class Converters {

    @TypeConverter
    fun fromComponentType(value: ComponentType): String {
        return value.name
    }

    @TypeConverter
    fun toComponentType(value: String): ComponentType {
        return try {
            ComponentType.valueOf(value)
        } catch (e: IllegalArgumentException) {
            ComponentType.PUSH_BUTTON // Default fallback
        }
    }
}