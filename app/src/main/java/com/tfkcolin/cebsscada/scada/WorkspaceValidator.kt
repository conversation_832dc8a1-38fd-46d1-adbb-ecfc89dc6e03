package com.tfkcolin.cebsscada.scada

import com.tfkcolin.cebsscada.scada.components.*
import com.tfkcolin.cebsscada.communication.CommunicationProtocol

/**
 * Comprehensive validation system for SCADA workspaces and components
 */
object WorkspaceValidator {

    /**
     * Validation result with severity levels
     */
    data class ValidationResult(
        val isValid: Boolean,
        val severity: Severity,
        val message: String,
        val field: String? = null,
        val suggestion: String? = null
    )

    enum class Severity {
        ERROR,    // Prevents operation
        WARNING,  // Allows operation but shows warning
        INFO      // Informational only
    }

    /**
     * Validate workspace name
     */
    fun validateWorkspaceName(name: String): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Workspace name cannot be empty",
                field = "name",
                suggestion = "Enter a descriptive name for your workspace"
            )
            name.length < 3 -> ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Workspace name must be at least 3 characters long",
                field = "name",
                suggestion = "Use a more descriptive name"
            )
            name.length > 50 -> ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Workspace name cannot exceed 50 characters",
                field = "name",
                suggestion = "Shorten the workspace name"
            )
            !name.matches(Regex("^[a-zA-Z0-9\\s\\-_]+$")) -> ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Workspace name contains invalid characters",
                field = "name",
                suggestion = "Use only letters, numbers, spaces, hyphens, and underscores"
            )
            else -> ValidationResult(
                isValid = true,
                severity = Severity.INFO,
                message = "Workspace name is valid"
            )
        }
    }

    /**
     * Validate workspace description
     */
    fun validateWorkspaceDescription(description: String): ValidationResult {
        return when {
            description.length > 500 -> ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Description cannot exceed 500 characters",
                field = "description",
                suggestion = "Shorten the description"
            )
            else -> ValidationResult(
                isValid = true,
                severity = Severity.INFO,
                message = "Description is valid"
            )
        }
    }

    /**
     * Validate entire workspace
     */
    fun validateWorkspace(
        name: String,
        description: String,
        components: List<ScadaComponent>
    ): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        // Validate basic properties
        results.add(validateWorkspaceName(name))
        results.add(validateWorkspaceDescription(description))

        // Validate components
        results.addAll(validateComponents(components))

        // Validate workspace-level rules
        results.addAll(validateWorkspaceRules(components))

        return results
    }

    /**
     * Validate all components in a workspace
     */
    fun validateComponents(components: List<ScadaComponent>): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        components.forEachIndexed { index, component ->
            results.addAll(validateComponent(component, index))
        }

        return results
    }

    /**
     * Validate individual component
     */
    fun validateComponent(component: ScadaComponent, index: Int = 0): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        // Basic component validation
        if (component.id.isBlank()) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Component ${index + 1} has no ID",
                field = "component_$index",
                suggestion = "Component IDs are automatically generated"
            ))
        }

        // Position validation
        if (component.position.x < 0 || component.position.y < 0) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.WARNING,
                message = "Component ${component.type.name.replace("_", " ")} is positioned off-screen",
                field = "component_$index",
                suggestion = "Move the component to a visible position"
            ))
        }

        // Size validation
        if (component.size.width.value < 20 || component.size.height.value < 20) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.WARNING,
                message = "Component ${component.type.name.replace("_", " ")} is very small",
                field = "component_$index",
                suggestion = "Increase the component size for better usability"
            ))
        }

        // Component-specific validation
        results.addAll(validateComponentConfig(component))

        return results
    }

    /**
     * Validate component configuration
     */
    private fun validateComponentConfig(component: ScadaComponent): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        when (component.config) {
            is PushButtonConfig -> {
                val config = component.config as PushButtonConfig
                results.addAll(validatePushButtonConfig(config))
            }
            is ValueDisplayConfig -> {
                val config = component.config as ValueDisplayConfig
                results.addAll(validateValueDisplayConfig(config))
            }
            is ToggleSwitchConfig -> {
                val config = component.config as ToggleSwitchConfig
                results.addAll(validateToggleSwitchConfig(config))
            }
            // Add other component types as needed
        }

        return results
    }

    /**
     * Validate PushButton configuration
     */
    private fun validatePushButtonConfig(config: PushButtonConfig): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        if (config.label.isBlank()) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.WARNING,
                message = "Push button has no label",
                field = "label",
                suggestion = "Add a descriptive label to the button"
            ))
        }

        // Validate data binding
        results.addAll(validateDataBinding(config.dataBinding, "Push Button"))

        return results
    }

    /**
     * Validate ValueDisplay configuration
     */
    private fun validateValueDisplayConfig(config: ValueDisplayConfig): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        // Validate data binding (required for value display)
        val bindingResults = validateDataBinding(config.dataBinding, "Value Display")
        if (bindingResults.any { !it.isValid && it.severity == Severity.ERROR }) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.ERROR,
                message = "Value Display requires data binding configuration",
                field = "dataBinding",
                suggestion = "Configure data binding to connect this display to a data source"
            ))
        } else {
            results.addAll(bindingResults)
        }

        return results
    }

    /**
     * Validate ToggleSwitch configuration
     */
    private fun validateToggleSwitchConfig(config: ToggleSwitchConfig): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        if (config.label.isBlank()) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.WARNING,
                message = "Toggle switch has no label",
                field = "label",
                suggestion = "Add a descriptive label to the switch"
            ))
        }

        // Validate data binding
        results.addAll(validateDataBinding(config.dataBinding, "Toggle Switch"))

        return results
    }

    /**
     * Validate data binding configuration
     */
    fun validateDataBinding(binding: DataBinding, componentName: String): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        // If protocol is set, address should be configured
        if (binding.protocol != null && binding.address.isBlank()) {
            results.add(ValidationResult(
                isValid = false,
                severity = Severity.WARNING,
                message = "$componentName has protocol selected but no address configured",
                field = "dataBinding.address",
                suggestion = "Enter the communication address (topic, URL, etc.)"
            ))
        }

        // Validate protocol-specific requirements
        when (binding.protocol) {
            CommunicationProtocol.MQTT -> {
                if (!binding.address.matches(Regex("^[a-zA-Z0-9/_\\-\\.#]+$"))) {
                    results.add(ValidationResult(
                        isValid = false,
                        severity = Severity.WARNING,
                        message = "MQTT topic contains invalid characters",
                        field = "dataBinding.address",
                        suggestion = "Use only letters, numbers, slashes, dots, hyphens, and underscores"
                    ))
                }
            }
            CommunicationProtocol.BLUETOOTH_BLE -> {
                if (!binding.address.matches(Regex("^[0-9a-fA-F]{4,8}$"))) {
                    results.add(ValidationResult(
                        isValid = false,
                        severity = Severity.WARNING,
                        message = "BLE characteristic UUID format is invalid",
                        field = "dataBinding.address",
                        suggestion = "Enter a valid BLE characteristic UUID (4-8 hex characters)"
                    ))
                }
            }
            // Add validation for other protocols as needed
            else -> {}
        }

        return results
    }

    /**
     * Validate workspace-level rules
     */
    private fun validateWorkspaceRules(components: List<ScadaComponent>): List<ValidationResult> {
        val results = mutableListOf<ValidationResult>()

        // Check for overlapping components
        val overlappingPairs = findOverlappingComponents(components)
        overlappingPairs.forEach { (comp1, comp2) ->
            results.add(ValidationResult(
                isValid = true, // Not an error, just a warning
                severity = Severity.WARNING,
                message = "Components '${comp1.type.name}' and '${comp2.type.name}' are overlapping",
                suggestion = "Consider repositioning components to avoid overlap"
            ))
        }

        // Check for components with identical data bindings
        val duplicateBindings = findDuplicateDataBindings(components)
        duplicateBindings.forEach { (binding, count) ->
            results.add(ValidationResult(
                isValid = true,
                severity = Severity.INFO,
                message = "$count components share the same data binding (${binding.protocol}:${binding.address})",
                suggestion = "This is normal if multiple displays show the same data"
            ))
        }

        // Performance warnings
        if (components.size > 50) {
            results.add(ValidationResult(
                isValid = true,
                severity = Severity.WARNING,
                message = "Workspace contains ${components.size} components, which may impact performance",
                suggestion = "Consider splitting into multiple workspaces or optimizing component count"
            ))
        }

        return results
    }

    /**
     * Find overlapping components
     */
    private fun findOverlappingComponents(components: List<ScadaComponent>): List<Pair<ScadaComponent, ScadaComponent>> {
        val overlapping = mutableListOf<Pair<ScadaComponent, ScadaComponent>>()

        for (i in components.indices) {
            for (j in i + 1 until components.size) {
                val comp1 = components[i]
                val comp2 = components[j]

                if (componentsOverlap(comp1, comp2)) {
                    overlapping.add(comp1 to comp2)
                }
            }
        }

        return overlapping
    }

    /**
     * Check if two components overlap
     */
    private fun componentsOverlap(comp1: ScadaComponent, comp2: ScadaComponent): Boolean {
        val x1 = comp1.position.x
        val y1 = comp1.position.y
        val w1 = comp1.size.width.value
        val h1 = comp1.size.height.value

        val x2 = comp2.position.x
        val y2 = comp2.position.y
        val w2 = comp2.size.width.value
        val h2 = comp2.size.height.value

        return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1)
    }

    /**
     * Find duplicate data bindings
     */
    private fun findDuplicateDataBindings(components: List<ScadaComponent>): Map<DataBinding, Int> {
        val bindingCounts = mutableMapOf<DataBinding, Int>()

        components.forEach { component ->
            val binding = when (component.config) {
                is PushButtonConfig -> (component.config as PushButtonConfig).dataBinding
                is ValueDisplayConfig -> (component.config as ValueDisplayConfig).dataBinding
                is ToggleSwitchConfig -> (component.config as ToggleSwitchConfig).dataBinding
                else -> DataBinding() // Empty binding
            }

            if (binding.protocol != null) {
                bindingCounts[binding] = bindingCounts.getOrDefault(binding, 0) + 1
            }
        }

        return bindingCounts.filter { it.value > 1 }
    }

    /**
     * Get validation summary
     */
    fun getValidationSummary(results: List<ValidationResult>): ValidationSummary {
        val errors = results.count { !it.isValid && it.severity == Severity.ERROR }
        val warnings = results.count { it.severity == Severity.WARNING }
        val infos = results.count { it.severity == Severity.INFO }

        return ValidationSummary(
            totalResults = results.size,
            errorCount = errors,
            warningCount = warnings,
            infoCount = infos,
            isValid = errors == 0,
            canProceedWithWarnings = warnings > 0 && errors == 0
        )
    }

    /**
     * Validation summary
     */
    data class ValidationSummary(
        val totalResults: Int,
        val errorCount: Int,
        val warningCount: Int,
        val infoCount: Int,
        val isValid: Boolean,
        val canProceedWithWarnings: Boolean
    )
}