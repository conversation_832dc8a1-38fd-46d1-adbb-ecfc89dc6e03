package com.tfkcolin.cebsscada.scada.components

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.cebsscada.scada.ComponentConfig
import com.tfkcolin.cebsscada.scada.DataBinding
import kotlinx.serialization.Serializable

/**
 * Serializable version of PushButtonConfig using primitive types
 */
@Serializable
data class SerializablePushButtonConfig(
    val label: String = "Button",
    val backgroundColor: ULong = 0xFF0000FFu, // Blue
    val borderColor: ULong = 0xFF000000u, // Black
    val borderWidth: Float = 2f,
    val textColor: ULong = 0xFFFFFFFFu, // White
    val textSize: Float = 14f,
    val buttonShape: String = "ROUNDED_RECTANGLE",
    val dataBinding: DataBinding = DataBinding()
) {
    fun toPushButtonConfig(): PushButtonConfig {
        return PushButtonConfig(
            label = label,
            backgroundColor = Color(backgroundColor),
            borderColor = Color(borderColor),
            borderWidth = borderWidth.dp,
            textColor = Color(textColor),
            textSize = textSize.sp,
            buttonShape = ButtonShape.valueOf(buttonShape),
            dataBinding = dataBinding
        )
    }

    companion object {
        fun fromPushButtonConfig(config: PushButtonConfig): SerializablePushButtonConfig {
            return SerializablePushButtonConfig(
                label = config.label,
                backgroundColor = config.backgroundColor.value,
                borderColor = config.borderColor.value,
                borderWidth = config.borderWidth.value,
                textColor = config.textColor.value,
                textSize = config.textSize.value,
                buttonShape = config.buttonShape.name,
                dataBinding = config.dataBinding
            )
        }
    }
}

/**
 * Serializable version of ValueDisplayConfig using primitive types
 */
@Serializable
data class SerializableValueDisplayConfig(
    val label: String = "",
    val backgroundColor: ULong = 0x00000000u, // Transparent
    val borderColor: ULong = 0x00000000u, // Transparent
    val borderWidth: Float = 0f,
    val textColor: ULong = 0xFF000000u, // Black
    val textSize: Float = 16f,
    val fontWeight: String = "NORMAL",
    val dataBinding: DataBinding = DataBinding(),
    val defaultText: String = "--"
) {
    fun toValueDisplayConfig(): ValueDisplayConfig {
        return ValueDisplayConfig(
            label = label,
            backgroundColor = Color(backgroundColor),
            borderColor = Color(borderColor),
            borderWidth = borderWidth.dp,
            textColor = Color(textColor),
            textSize = textSize.sp,
            fontWeight = FontWeight.valueOf(fontWeight),
            dataBinding = dataBinding,
            defaultText = defaultText
        )
    }

    companion object {
        fun fromValueDisplayConfig(config: ValueDisplayConfig): SerializableValueDisplayConfig {
            return SerializableValueDisplayConfig(
                label = config.label,
                backgroundColor = config.backgroundColor.value,
                borderColor = config.borderColor.value,
                borderWidth = config.borderWidth.value,
                textColor = config.textColor.value,
                textSize = config.textSize.value,
                fontWeight = config.fontWeight.name,
                dataBinding = config.dataBinding,
                defaultText = config.defaultText
            )
        }
    }
}

/**
 * Serializable version of ToggleSwitchConfig using primitive types
 */
@Serializable
data class SerializableToggleSwitchConfig(
    val label: String = "Switch",
    val backgroundColor: ULong = 0xFFD3D3D3u, // LightGray
    val borderColor: ULong = 0xFF808080u, // Gray
    val borderWidth: Float = 1f,
    val onColor: ULong = 0xFF00FF00u, // Green
    val offColor: ULong = 0xFF808080u, // Gray
    val switchStyle: String = "SLIDER",
    val dataBinding: DataBinding = DataBinding(),
    val onPayload: String = "true",
    val offPayload: String = "false",
    val stateSourceBinding: DataBinding? = null,
    val initialState: Boolean = false
) {
    fun toToggleSwitchConfig(): ToggleSwitchConfig {
        return ToggleSwitchConfig(
            label = label,
            backgroundColor = Color(backgroundColor),
            borderColor = Color(borderColor),
            borderWidth = borderWidth.dp,
            onColor = Color(onColor),
            offColor = Color(offColor),
            switchStyle = SwitchStyle.valueOf(switchStyle),
            dataBinding = dataBinding,
            onPayload = onPayload,
            offPayload = offPayload,
            stateSourceBinding = stateSourceBinding,
            initialState = initialState
        )
    }

    companion object {
        fun fromToggleSwitchConfig(config: ToggleSwitchConfig): SerializableToggleSwitchConfig {
            return SerializableToggleSwitchConfig(
                label = config.label,
                backgroundColor = config.backgroundColor.value,
                borderColor = config.borderColor.value,
                borderWidth = config.borderWidth.value,
                onColor = config.onColor.value,
                offColor = config.offColor.value,
                switchStyle = config.switchStyle.name,
                dataBinding = config.dataBinding,
                onPayload = config.onPayload,
                offPayload = config.offPayload,
                stateSourceBinding = config.stateSourceBinding,
                initialState = config.initialState
            )
        }
    }
}