package com.tfkcolin.cebsscada.ui.preview

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.ui.components.*
import com.tfkcolin.cebsscada.ui.theme.*

/**
 * Preview composables to showcase responsive design across different screen sizes
 */

@Preview(
    name = "Small Phone",
    device = "spec:width=320dp,height=480dp,dpi=160",
    showBackground = true
)
@Preview(
    name = "Normal Phone Portrait",
    device = Devices.PHONE,
    showBackground = true
)
@Preview(
    name = "Phone Landscape",
    device = "spec:width=851dp,height=393dp,dpi=480",
    showBackground = true
)
@Preview(
    name = "Large Phone",
    device = "spec:width=480dp,height=960dp,dpi=240",
    showBackground = true
)
@Preview(
    name = "Tablet Portrait",
    device = Devices.TABLET,
    showBackground = true
)
@Preview(
    name = "Tablet Landscape",
    device = "spec:width=1200dp,height=800dp,dpi=240",
    showBackground = true
)
@Preview(
    name = "Desktop",
    device = "spec:width=1280dp,height=800dp,dpi=240",
    showBackground = true
)
@Composable
fun ResponsiveLayoutPreview() {
    CEBSSCADATheme {
        val layout = rememberResponsiveLayout()
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(layout.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(layout.spacing.medium)
        ) {
            // Screen size indicator
            Card {
                Column(
                    modifier = Modifier.padding(layout.spacing.medium)
                ) {
                    Text(
                        text = "Screen Size: ${layout.screenSize}",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "Device Type: ${layout.deviceType}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "Layout: ${when {
                            layout.shouldUseSinglePane -> "Single Pane"
                            layout.shouldUseTwoPane -> "Two Pane"
                            layout.shouldUseThreePane -> "Three Pane"
                            else -> "Unknown"
                        }}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            // Adaptive buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(layout.spacing.small)
            ) {
                AdaptiveButton(onClick = { }) {
                    Text("Primary")
                }
                AdaptiveIconButton(onClick = { }) {
                    Icon(Icons.Default.Settings, contentDescription = "Settings")
                }
                AdaptiveFAB(onClick = { }) {
                    Icon(Icons.Default.Add, contentDescription = "Add")
                }
            }
            
            // Adaptive grid
            val sampleItems = listOf("Item 1", "Item 2", "Item 3", "Item 4", "Item 5", "Item 6")
            AdaptiveGrid(
                items = sampleItems,
                modifier = Modifier.height(200.dp)
            ) { item ->
                AdaptiveCard {
                    Text(
                        text = item,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Preview(
    name = "Three Pane Layout - Small Phone",
    device = "spec:width=320dp,height=480dp,dpi=160",
    showBackground = true
)
@Preview(
    name = "Three Pane Layout - Phone",
    device = Devices.PHONE,
    showBackground = true
)
@Preview(
    name = "Three Pane Layout - Tablet",
    device = Devices.TABLET,
    showBackground = true
)
@Preview(
    name = "Three Pane Layout - Desktop",
    device = "spec:width=1280dp,height=800dp,dpi=240",
    showBackground = true
)
@Composable
fun AdaptiveThreePanePreview() {
    CEBSSCADATheme {
        AdaptiveThreePane(
            leftContent = {
                Card(
                    modifier = Modifier.fillMaxHeight()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Left Panel",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        repeat(5) { index ->
                            Text(
                                text = "Left Item ${index + 1}",
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                        }
                    }
                }
            },
            centerContent = {
                Card(
                    modifier = Modifier.fillMaxHeight()
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.Dashboard,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Main Content Area",
                                style = MaterialTheme.typography.titleLarge
                            )
                            Text(
                                text = "This is the primary workspace",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            },
            rightContent = {
                Card(
                    modifier = Modifier.fillMaxHeight()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Right Panel",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        repeat(3) { index ->
                            Text(
                                text = "Property ${index + 1}",
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                        }
                    }
                }
            },
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Preview(
    name = "Typography Scaling - Small Phone",
    device = "spec:width=320dp,height=480dp,dpi=160",
    showBackground = true
)
@Preview(
    name = "Typography Scaling - Phone",
    device = Devices.PHONE,
    showBackground = true
)
@Preview(
    name = "Typography Scaling - Tablet",
    device = Devices.TABLET,
    showBackground = true
)
@Preview(
    name = "Typography Scaling - Desktop",
    device = "spec:width=1280dp,height=800dp,dpi=240",
    showBackground = true
)
@Composable
fun ResponsiveTypographyPreview() {
    CEBSSCADATheme {
        val layout = rememberResponsiveLayout()
        val typography = getResponsiveTypography(layout.screenSize)
        
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(layout.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(layout.spacing.small)
        ) {
            item {
                Text(
                    text = "Display Large",
                    style = typography.displayLarge
                )
            }
            item {
                Text(
                    text = "Display Medium",
                    style = typography.displayMedium
                )
            }
            item {
                Text(
                    text = "Headline Large",
                    style = typography.headlineLarge
                )
            }
            item {
                Text(
                    text = "Title Large",
                    style = typography.titleLarge
                )
            }
            item {
                Text(
                    text = "Body Large - This is the main body text that users will read most often. It should be comfortable to read at all screen sizes.",
                    style = typography.bodyLarge
                )
            }
            item {
                Text(
                    text = "Body Medium - Secondary body text for additional information.",
                    style = typography.bodyMedium
                )
            }
            item {
                Text(
                    text = "Label Large",
                    style = typography.labelLarge
                )
            }
        }
    }
}

@Preview(
    name = "Adaptive Cards - Small Phone",
    device = "spec:width=320dp,height=480dp,dpi=160",
    showBackground = true
)
@Preview(
    name = "Adaptive Cards - Phone",
    device = Devices.PHONE,
    showBackground = true
)
@Preview(
    name = "Adaptive Cards - Tablet",
    device = Devices.TABLET,
    showBackground = true
)
@Preview(
    name = "Adaptive Cards - Desktop",
    device = "spec:width=1280dp,height=800dp,dpi=240",
    showBackground = true
)
@Composable
fun AdaptiveCardsPreview() {
    CEBSSCADATheme {
        val layout = rememberResponsiveLayout()
        val sampleCards = listOf(
            "Workspace 1" to "Industrial monitoring system",
            "Workspace 2" to "Temperature control dashboard",
            "Workspace 3" to "Production line overview",
            "Workspace 4" to "Quality assurance metrics"
        )
        
        AdaptiveGrid(
            items = sampleCards,
            modifier = Modifier
                .fillMaxSize()
                .padding(layout.spacing.medium)
        ) { (title, description) ->
            AdaptiveCard(
                onClick = { }
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(layout.spacing.small))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(layout.spacing.medium))
                Row(
                    horizontalArrangement = Arrangement.spacedBy(layout.spacing.small)
                ) {
                    AdaptiveButton(
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Edit")
                    }
                    AdaptiveButton(
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Run")
                    }
                }
            }
        }
    }
}
