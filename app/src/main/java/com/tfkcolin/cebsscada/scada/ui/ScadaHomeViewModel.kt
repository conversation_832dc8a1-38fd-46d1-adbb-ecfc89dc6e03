package com.tfkcolin.cebsscada.scada.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.scada.persistence.WorkspaceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for SCADA home screen - workspace management
 */
@HiltViewModel
class ScadaHomeViewModel @Inject constructor(
    private val workspaceRepository: WorkspaceRepository
) : ViewModel() {

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage

    // Combine all workspaces with search filtering
    val workspaces = combine(
        workspaceRepository.getAllWorkspaces(),
        _searchQuery
    ) { allWorkspaces, query ->
        if (query.isBlank()) {
            allWorkspaces
        } else {
            allWorkspaces.filter { workspace ->
                workspace.name.contains(query, ignoreCase = true) ||
                workspace.description.contains(query, ignoreCase = true)
            }
        }
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    /**
     * Delete a workspace
     */
    fun deleteWorkspace(workspaceId: Long) {
        viewModelScope.launch {
            val result = workspaceRepository.deleteWorkspace(workspaceId)
            if (result.isError) {
                _errorMessage.value = result.errorOrNull()
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Get workspace statistics
     */
    suspend fun getWorkspaceStats(): HomeWorkspaceStats {
        val allWorkspaces = workspaceRepository.getAllWorkspaces().first()
        val currentTime = System.currentTimeMillis()
        val sevenDaysAgo = currentTime - (7 * 24 * 60 * 60 * 1000L)

        return HomeWorkspaceStats(
            totalWorkspaces = allWorkspaces.size,
            totalComponents = allWorkspaces.sumOf { it.componentCount },
            recentlyModified = allWorkspaces.count { it.modifiedAt > sevenDaysAgo }
        )
    }
}

/**
 * Workspace statistics
 */
data class HomeWorkspaceStats(
    val totalWorkspaces: Int,
    val totalComponents: Int,
    val recentlyModified: Int // Last 7 days
)