package com.tfkcolin.cebsscada.scada.components

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.scada.*

/**
 * Base implementation for all SCADA components
 */
abstract class BaseScadaComponent<T : ComponentConfig>(
    override val id: String,
    override val type: ComponentType,
    initialPosition: ComponentPosition,
    initialSize: ComponentSize,
    initialConfig: T,
    initialGroupId: String? = null,
    initialLayer: Int = 0,
    initialIsLocked: Boolean = false,
    initialIsVisible: Boolean = true,
    initialTags: Set<String> = emptySet()
) : ScadaComponent {

    override val position: ComponentPosition
        get() = _position.value

    override val size: ComponentSize
        get() = _size.value

    override val config: ComponentConfig
        get() = _config.value

    // Organization properties
    override val groupId: String?
        get() = _groupId.value

    override val layer: Int
        get() = _layer.value

    override val isLocked: Boolean
        get() = _isLocked.value

    override val isVisible: Boolean
        get() = _isVisible.value

    override val tags: Set<String>
        get() = _tags.value

    // Internal mutable states
    protected val _position: MutableState<ComponentPosition> = mutableStateOf(initialPosition)
    protected val _size: MutableState<ComponentSize> = mutableStateOf(initialSize)
    protected val _config: MutableState<T> = mutableStateOf(initialConfig)

    // Organization mutable states
    protected val _groupId: MutableState<String?> = mutableStateOf(initialGroupId)
    protected val _layer: MutableState<Int> = mutableStateOf(initialLayer)
    protected val _isLocked: MutableState<Boolean> = mutableStateOf(initialIsLocked)
    protected val _isVisible: MutableState<Boolean> = mutableStateOf(initialIsVisible)
    protected val _tags: MutableState<Set<String>> = mutableStateOf(initialTags)

    override fun moveTo(newPosition: ComponentPosition): ScadaComponent {
        _position.value = newPosition
        return this
    }

    override fun resizeTo(newSize: ComponentSize): ScadaComponent {
        _size.value = newSize
        return this
    }

    @Suppress("UNCHECKED_CAST")
    override fun updateConfig(newConfig: ComponentConfig): ScadaComponent {
        _config.value = newConfig as T
        return this
    }

    /**
     * Get the typed config
     */
    fun getTypedConfig(): T = _config.value

    /**
     * Update the typed config
     */
    fun updateTypedConfig(newConfig: T) {
        _config.value = newConfig
    }

    override fun updateOrganization(groupId: String?, layer: Int, isLocked: Boolean, isVisible: Boolean, tags: Set<String>): ScadaComponent {
        _groupId.value = groupId
        _layer.value = layer
        _isLocked.value = isLocked
        _isVisible.value = isVisible
        _tags.value = tags
        return this
    }

    override fun validate(): List<ValidationError> {
        val errors = mutableListOf<ValidationError>()

        // Basic validation - can be overridden by subclasses
        val config = getTypedConfig()

        if (config.label.isBlank()) {
            errors.add(ValidationError("label", "Label cannot be empty", ValidationSeverity.WARNING))
        }

        if (size.width.value <= 0.dp.value || size.height.value <= 0.dp.value) {
            errors.add(ValidationError("size", "Component size must be positive", ValidationSeverity.ERROR))
        }

        if (position.x < 0 || position.y < 0) {
            errors.add(ValidationError("position", "Component position cannot be negative", ValidationSeverity.WARNING))
        }

        return errors
    }
}