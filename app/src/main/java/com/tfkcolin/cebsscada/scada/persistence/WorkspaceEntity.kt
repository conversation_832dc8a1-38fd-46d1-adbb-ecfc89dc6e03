package com.tfkcolin.cebsscada.scada.persistence

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.tfkcolin.cebsscada.scada.ComponentType
import kotlinx.serialization.Serializable

/**
 * Room entity for SCADA workspace
 */
@Entity(tableName = "workspaces")
@TypeConverters(Converters::class)
data class WorkspaceEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val modifiedAt: Long = System.currentTimeMillis(),
    val thumbnail: String? = null, // Base64 encoded image
    val componentCount: Int = 0
)

/**
 * Room entity for SCADA components within a workspace
 */
@Entity(
    tableName = "workspace_components",
    primaryKeys = ["workspaceId", "componentId"]
)
@TypeConverters(Converters::class)
data class ComponentEntity(
    val workspaceId: Long,
    val componentId: String,
    val type: ComponentType,
    val positionX: Float,
    val positionY: Float,
    val sizeWidth: Float, // Dp as float
    val sizeHeight: Float, // Dp as float
    val configJson: String // Serialized component configuration
)

/**
 * Data class for workspace with components (for easy manipulation)
 */
data class WorkspaceWithComponents(
    val workspace: WorkspaceEntity,
    val components: List<ComponentEntity>
)