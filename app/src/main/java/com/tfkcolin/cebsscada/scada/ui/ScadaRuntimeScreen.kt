package com.tfkcolin.cebsscada.scada.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.scada.ScadaComponent
import com.tfkcolin.cebsscada.scada.ComponentPosition
import com.tfkcolin.cebsscada.ui.components.ScreenConfig

/**
 * Communication status bar showing connection states
 */
@Composable
fun CommunicationStatusBar(
    connectionStates: Map<CommunicationProtocol, ConnectionState>,
    activeProtocols: Set<CommunicationProtocol>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            activeProtocols.forEach { protocol ->
                val state = connectionStates[protocol] ?: ConnectionState.DISCONNECTED
                CommunicationStatusChip(protocol = protocol, state = state)
            }
        }
    }
}

/**
 * Individual communication status chip
 */
@Composable
fun CommunicationStatusChip(
    protocol: CommunicationProtocol,
    state: ConnectionState
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Status indicator
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    color = when (state) {
                        ConnectionState.CONNECTED -> Color.Green
                        ConnectionState.CONNECTING -> Color.Yellow
                        ConnectionState.ERROR -> Color.Red
                        else -> Color.Gray
                    },
                    shape = CircleShape
                )
        )

        // Protocol name and status
        Text(
            text = "${protocol.name.replace("_", " ")}: ${state.name}",
            style = MaterialTheme.typography.bodySmall,
            color = when (state) {
                ConnectionState.CONNECTED -> Color.Green
                ConnectionState.CONNECTING -> Color.Yellow
                ConnectionState.ERROR -> Color.Red
                else -> Color.Gray
            }
        )
    }
}

/**
 * Get communication status icon based on connection states
 */
@Composable
private fun getCommunicationStatusIcon(
    connectionStates: Map<CommunicationProtocol, ConnectionState>,
    activeProtocols: Set<CommunicationProtocol>
): androidx.compose.ui.graphics.vector.ImageVector {
    val hasConnected = activeProtocols.any { protocol ->
        connectionStates[protocol] == ConnectionState.CONNECTED
    }
    val hasErrors = activeProtocols.any { protocol ->
        connectionStates[protocol] == ConnectionState.ERROR
    }

    return when {
        hasErrors -> Icons.Default.Error
        hasConnected -> Icons.Default.Wifi
        else -> Icons.Default.WifiOff
    }
}

/**
 * Get communication status color based on connection states
 */
@Composable
private fun getCommunicationStatusColor(
    connectionStates: Map<CommunicationProtocol, ConnectionState>,
    activeProtocols: Set<CommunicationProtocol>
): Color {
    val hasConnected = activeProtocols.any { protocol ->
        connectionStates[protocol] == ConnectionState.CONNECTED
    }
    val hasErrors = activeProtocols.any { protocol ->
        connectionStates[protocol] == ConnectionState.ERROR
    }

    return when {
        hasErrors -> MaterialTheme.colorScheme.error
        hasConnected -> Color.Green
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }
}

/**
 * Runtime mode for executing saved SCADA workspaces (read-only)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScadaRuntimeScreen(
    workspaceId: Long,
    viewModel: ScadaRuntimeViewModel = hiltViewModel(),
    onBackToHome: () -> Unit,
    onScreenConfigChange: (ScreenConfig) -> Unit
) {
    val components by viewModel.components.collectAsStateWithLifecycle()
    val workspaceName by viewModel.workspaceName.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val connectionStates by viewModel.connectionStates.collectAsStateWithLifecycle()
    val communicationErrors by viewModel.communicationErrors.collectAsStateWithLifecycle()
    val activeProtocols by viewModel.activeProtocols.collectAsStateWithLifecycle()

    var showConnectionDialog by remember { mutableStateOf(false) }
    var showErrorDialog by remember { mutableStateOf(false) }

    LaunchedEffect(workspaceId) {
        viewModel.loadWorkspace(workspaceId)
    }

    // Configure the central scaffold
    LaunchedEffect(workspaceName, activeProtocols, communicationErrors) {
        onScreenConfigChange(
            ScreenConfig(
                title = workspaceName ?: "Running Workspace",
                showBackButton = true,
                showSettingsButton = false,
                actions = buildList {
                    // Communication status indicator
                    if (activeProtocols.isNotEmpty()) {
                        add {
                            IconButton(onClick = { showConnectionDialog = true }) {
                                Icon(
                                    imageVector = getCommunicationStatusIcon(connectionStates, activeProtocols),
                                    contentDescription = "Communication status",
                                    tint = getCommunicationStatusColor(connectionStates, activeProtocols)
                                )
                            }
                        }
                    }

                    // Error indicator
                    if (communicationErrors.isNotEmpty()) {
                        add {
                            IconButton(onClick = { showErrorDialog = true }) {
                                Icon(
                                    Icons.Default.Error,
                                    contentDescription = "Communication errors",
                                    tint = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                }
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                components.isEmpty() -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "No components in this workspace",
                            style = MaterialTheme.typography.headlineSmall
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Edit the workspace to add components",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                else -> {
                    Box(modifier = Modifier.fillMaxSize()) {
                        // Runtime workspace canvas - read-only
                        ScadaWorkspaceRuntime(
                            components = components,
                            modifier = Modifier.fillMaxSize()
                        )

                        // Communication status bar
                        if (activeProtocols.isNotEmpty()) {
                            CommunicationStatusBar(
                                connectionStates = connectionStates,
                                activeProtocols = activeProtocols,
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .padding(16.dp)
                            )
                        }
                    }
                }
            }
        }

    /**
     * Connection management dialog
     */
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun CommunicationConnectionDialog(
        connectionStates: Map<CommunicationProtocol, ConnectionState>,
        activeProtocols: Set<CommunicationProtocol>,
        onConnectDevice: (CommunicationProtocol, String) -> Unit,
        onDisconnectProtocol: (CommunicationProtocol) -> Unit,
        onDismiss: () -> Unit
    ) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("Communication Connections") },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "Manage connections for active communication protocols:",
                        style = MaterialTheme.typography.bodyMedium
                    )
    
                    activeProtocols.forEach { protocol ->
                        val state = connectionStates[protocol] ?: ConnectionState.DISCONNECTED
    
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = protocol.name.replace("_", " "),
                                        style = MaterialTheme.typography.titleSmall
                                    )
    
                                    // Status indicator
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                                    ) {
                                        Box(
                                            modifier = Modifier
                                                .size(8.dp)
                                                .background(
                                                    color = when (state) {
                                                        ConnectionState.CONNECTED -> Color.Green
                                                        ConnectionState.CONNECTING -> Color.Yellow
                                                        ConnectionState.ERROR -> Color.Red
                                                        else -> Color.Gray
                                                    },
                                                    shape = CircleShape
                                                )
                                        )
                                        Text(
                                            text = state.name,
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }
    
                                Spacer(modifier = Modifier.height(8.dp))
    
                                // Connection actions
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    if (state == ConnectionState.CONNECTED) {
                                        OutlinedButton(
                                            onClick = { onDisconnectProtocol(protocol) },
                                            modifier = Modifier.weight(1f)
                                        ) {
                                            Text("Disconnect")
                                        }
                                    } else {
                                        // In a real implementation, you'd show available devices to connect to
                                        Text(
                                            text = "Device connection UI would be implemented here",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = onDismiss) {
                    Text("Close")
                }
            }
        )
    }
    
    /**
     * Communication error display dialog
     */
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun CommunicationErrorDialog(
        errors: List<String>,
        onClearErrors: () -> Unit,
        onDismiss: () -> Unit
    ) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("Communication Errors") },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp)
                ) {
                    Text(
                        text = "Recent communication errors:",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
    
                    LazyColumn(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(errors) { error ->
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.errorContainer
                                )
                            ) {
                                Text(
                                    text = error,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onErrorContainer,
                                    modifier = Modifier.padding(8.dp)
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = onClearErrors) {
                    Text("Clear All")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("Close")
                }
            }
        )
    }
    

    // Connection management dialog
    if (showConnectionDialog) {
        CommunicationConnectionDialog(
            connectionStates = connectionStates,
            activeProtocols = activeProtocols,
            onConnectDevice = { protocol, deviceId ->
                viewModel.connectToDevice(protocol, deviceId)
            },
            onDisconnectProtocol = { protocol ->
                viewModel.disconnectProtocol(protocol)
            },
            onDismiss = { showConnectionDialog = false }
        )
    }

    // Error display dialog
    if (showErrorDialog && communicationErrors.isNotEmpty()) {
        CommunicationErrorDialog(
            errors = communicationErrors,
            onClearErrors = { viewModel.clearCommunicationErrors() },
            onDismiss = { showErrorDialog = false }
        )
    }
}

/**
 * Runtime workspace canvas - read-only mode for executing workspaces
 */
@Composable
fun ScadaWorkspaceRuntime(
    components: List<ScadaComponent>,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // Static grid background (no drag functionality)
        Canvas(modifier = Modifier.fillMaxSize()) {
            val gridSize = 20f
            val width = size.width
            val height = size.height

            // Draw vertical lines
            for (x in 0..(width / gridSize).toInt()) {
                drawLine(
                    color = Color.LightGray.copy(alpha = 0.3f),
                    start = Offset(x * gridSize, 0f),
                    end = Offset(x * gridSize, height),
                    strokeWidth = 1f
                )
            }

            // Draw horizontal lines
            for (y in 0..(height / gridSize).toInt()) {
                drawLine(
                    color = Color.LightGray.copy(alpha = 0.3f),
                    start = Offset(0f, y * gridSize),
                    end = Offset(width, y * gridSize),
                    strokeWidth = 1f
                )
            }
        }

        // Render all components in read-only mode
        components.forEach { component ->
            key(component.id) {
                ScadaComponentRuntimeRenderer(
                    component = component
                )
            }
        }
    }
}

/**
 * Runtime component renderer - no editing capabilities
 */
@Composable
fun ScadaComponentRuntimeRenderer(component: ScadaComponent) {
    Box(
        modifier = Modifier
            .offset(
                x = component.position.x.dp,
                y = component.position.y.dp
            )
            .width(component.size.width)
            .height(component.size.height)
    ) {
        when (component.type) {
            com.tfkcolin.cebsscada.scada.ComponentType.PUSH_BUTTON -> {
                val pushButton = component as com.tfkcolin.cebsscada.scada.components.PushButton
                val config = pushButton.getTypedConfig()

                Button(
                    onClick = { pushButton.onPressed() },
                    modifier = Modifier.fillMaxSize(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = config.backgroundColor
                    ),
                    shape = when (config.buttonShape) {
                        com.tfkcolin.cebsscada.scada.components.ButtonShape.CIRCLE ->
                            CircleShape
                        else -> RoundedCornerShape(8.dp)
                    }
                ) {
                    Text(
                        text = config.label,
                        color = config.textColor,
                        fontSize = config.textSize
                    )
                }
            }

            com.tfkcolin.cebsscada.scada.ComponentType.VALUE_DISPLAY -> {
                val valueDisplay = component as com.tfkcolin.cebsscada.scada.components.ValueDisplay
                val config = valueDisplay.getTypedConfig()

                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(config.backgroundColor)
                        .border(
                            config.borderWidth,
                            config.borderColor,
                            RoundedCornerShape(4.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = valueDisplay.currentValue,
                        color = config.textColor,
                        fontSize = config.textSize,
                        fontWeight = when (config.fontWeight) {
                            com.tfkcolin.cebsscada.scada.components.FontWeight.BOLD ->
                                FontWeight.Bold
                            else -> FontWeight.Normal
                        }
                    )
                }
            }

            com.tfkcolin.cebsscada.scada.ComponentType.TOGGLE_SWITCH -> {
                val toggleSwitch = component as com.tfkcolin.cebsscada.scada.components.ToggleSwitch
                val config = toggleSwitch.getTypedConfig()

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(config.backgroundColor)
                        .border(config.borderWidth, config.borderColor, RoundedCornerShape(8.dp))
                        .padding(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // Label
                    if (config.label.isNotEmpty()) {
                        Text(
                            text = config.label,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }

                    // Switch
                    Switch(
                        checked = toggleSwitch.isChecked,
                        onCheckedChange = { checked ->
                            toggleSwitch.onToggle(checked)
                        },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = config.onColor,
                            checkedTrackColor = config.onColor.copy(alpha = 0.5f),
                            uncheckedThumbColor = config.offColor,
                            uncheckedTrackColor = config.offColor.copy(alpha = 0.5f)
                        )
                    )
                }
            }

            else -> {
                // Placeholder for other component types
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.LightGray),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "${component.type.name.replace("_", " ")}\n(Runtime)",
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}